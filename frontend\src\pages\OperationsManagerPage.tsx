import React, { useState } from 'react';
import { 
  Clipboard, 
  Search, 
  Upload, 
  Trash2,
  LogOut,
  User,
  Bell
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

const OperationsManagerPage: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeModal, setActiveModal] = useState<string | null>(null);

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('تم تسجيل الخروج بنجاح');
    } catch (error) {
      toast.error('خطأ في تسجيل الخروج');
    }
  };

  const managerCards = [
    {
      id: 'clipboard',
      title: 'الحافظة',
      icon: Clipboard,
      color: 'bg-blue-500',
      description: 'الملفات المرسلة والمستلمة'
    },
    {
      id: 'search',
      title: 'البحث',
      icon: Search,
      color: 'bg-green-500',
      description: 'البحث في الملفات'
    },
    {
      id: 'publish',
      title: 'النشر',
      icon: Upload,
      color: 'bg-purple-500',
      description: 'نشر ملف إلى الشاشة الرئيسية'
    },
    {
      id: 'deleted',
      title: 'المحذوفات',
      icon: Trash2,
      color: 'bg-red-500',
      description: 'الملفات المحذوفة'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Title */}
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                لوحة مدير التشغيل
              </h1>
              <p className="text-sm text-gray-500">إدارة ونشر الملفات</p>
            </div>

            {/* User Info and Actions */}
            <div className="flex items-center space-x-4 space-x-reverse">
              {/* Notifications */}
              <button className="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Bell className="h-6 w-6" />
                <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
              </button>

              {/* User Info */}
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user?.fullName}</p>
                  <p className="text-xs text-gray-500">{user?.jobTitle}</p>
                </div>
                <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-600" />
                </div>
              </div>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="btn btn-secondary flex items-center space-x-2 space-x-reverse"
              >
                <LogOut className="h-4 w-4" />
                <span>خروج</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً، {user?.fullName}
          </h2>
          <p className="text-gray-600">
            يمكنك نشر الملفات إلى الشاشة الرئيسية وإدارة المحتوى
          </p>
        </div>

        {/* Manager Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {managerCards.map((card) => {
            const IconComponent = card.icon;
            return (
              <div
                key={card.id}
                onClick={() => setActiveModal(card.id)}
                className="home-card group"
              >
                <div className={`home-card-icon ${card.color}`}>
                  <IconComponent className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2 text-center">
                  {card.title}
                </h3>
                <p className="text-gray-600 text-center text-sm">
                  {card.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Recent Activity */}
        <div className="mt-12">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">النشاط الأخير</h3>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <p className="text-gray-500 text-center py-8">
              لا توجد أنشطة حديثة
            </p>
          </div>
        </div>
      </main>

      {/* Modals - Temporary placeholders */}
      {activeModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setActiveModal(null)} />
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="relative w-full max-w-2xl bg-white rounded-xl shadow-xl p-6">
              <h3 className="text-lg font-semibold mb-4">
                {managerCards.find(card => card.id === activeModal)?.title}
              </h3>
              <p className="text-gray-600 mb-4">
                هذه النافذة قيد التطوير...
              </p>
              <button
                onClick={() => setActiveModal(null)}
                className="btn btn-secondary"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OperationsManagerPage;
