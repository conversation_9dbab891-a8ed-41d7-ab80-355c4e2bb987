const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { supabase } = require('../config/supabase');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'tv-office-super-secret-jwt-key-2024';

// تسجيل الدخول
router.post('/login', [
  body('username').notEmpty().withMessage('اسم المستخدم مطلوب'),
  body('password').notEmpty().withMessage('كلمة المرور مطلوبة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { username, password } = req.body;

    // البحث عن المستخدم
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }

    // إنشاء رمز مميز
    const token = jwt.sign(
      { userId: user.id, userType: user.user_type },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // إرجاع البيانات (بدون كلمة المرور)
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: userWithoutPassword
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'خطأ في تسجيل الدخول' });
  }
});

// تسجيل الخروج
router.post('/logout', (req, res) => {
  res.json({ message: 'تم تسجيل الخروج بنجاح' });
});

// التحقق من صحة الرمز المميز
router.get('/verify', authenticateToken, (req, res) => {
  const { password: _, ...userWithoutPassword } = req.user;
  res.json({ 
    message: 'الرمز المميز صالح',
    user: userWithoutPassword 
  });
});

// طلب إعادة تعيين كلمة المرور
router.post('/request-password-reset', [
  body('username').notEmpty().withMessage('اسم المستخدم مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { username, message } = req.body;

    // البحث عن المستخدم
    const { data: user, error } = await supabase
      .from('users')
      .select('id, username, full_name')
      .eq('username', username)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // إنشاء طلب إعادة تعيين كلمة المرور
    const { error: insertError } = await supabase
      .from('password_reset_requests')
      .insert({
        user_id: user.id,
        message: message || 'طلب إعادة تعيين كلمة المرور'
      });

    if (insertError) {
      throw insertError;
    }

    res.json({ 
      message: 'تم إرسال طلب إعادة تعيين كلمة المرور للمدير' 
    });

  } catch (error) {
    console.error('Password reset request error:', error);
    res.status(500).json({ message: 'خطأ في إرسال الطلب' });
  }
});

module.exports = router;
