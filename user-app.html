<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المحتوى - برنامج المستخدمين</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: bold;
            color: #4a5568;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #4a5568;
        }

        .main-container {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .notification-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 600px;
            width: 90%;
            text-align: center;
            display: none;
        }

        .notification-popup.show {
            display: block;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translate(-50%, -60%); }
            to { opacity: 1; transform: translate(-50%, -50%); }
        }

        .book-preview {
            width: 200px;
            height: 280px;
            margin: 0 auto 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
        }

        .book-title {
            font-size: 24px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .book-author {
            font-size: 16px;
            color: #718096;
            margin-bottom: 20px;
        }

        .notification-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3182ce;
            color: white;
        }

        .btn-primary:hover {
            background: #2c5aa0;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .publish-area {
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .publish-area:hover {
            border-color: #3182ce;
            background: rgba(49, 130, 206, 0.05);
        }

        .publish-area.dragover {
            border-color: #3182ce;
            background: rgba(49, 130, 206, 0.1);
        }

        .file-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .overlay.show {
            display: block;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-online {
            background: #48bb78;
        }

        .status-offline {
            background: #f56565;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="header">
        <div class="logo">
            📚 نظام إدارة المحتوى
        </div>
        <div class="user-info">
            <span>مرحباً، أحمد محمد</span>
            <span class="status-indicator status-online"></span>
            <span>متصل</span>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-container">
        <div class="content-grid">
            <!-- قسم النشر -->
            <div class="section">
                <div class="section-title">
                    📤 نشر محتوى جديد
                </div>
                <div class="publish-area" onclick="selectFile()" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">اسحب الملف هنا أو انقر للاختيار</div>
                    <div style="color: #718096;">يدعم: PDF, صور (JPG, PNG), مستندات Word</div>
                </div>
                <input type="file" id="fileInput" style="display: none;" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" onchange="handleFileSelect(event)">
            </div>

            <!-- قسم الملفات المستلمة -->
            <div class="section">
                <div class="section-title">
                    📥 الملفات المستلمة
                </div>
                <div class="file-list" id="receivedFiles">
                    <!-- سيتم إدراج الملفات هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الإشعار -->
    <div class="overlay" id="overlay"></div>
    <div class="notification-popup" id="notificationPopup">
        <div class="book-preview" id="bookPreview">
            📖
        </div>
        <div class="book-title" id="bookTitle">كتاب جديد</div>
        <div class="book-author" id="bookAuthor">تم نشره للتو</div>
        <div class="notification-buttons">
            <button class="btn btn-primary" onclick="saveToReceived()">حفظ في المستلمة</button>
            <button class="btn btn-secondary" onclick="closeNotification()">إغلاق</button>
        </div>
    </div>

    <script>
        // نظام إدارة البيانات
        class ContentManager {
            constructor() {
                this.initializeData();
                this.loadReceivedFiles();
                this.simulateIncomingContent();
            }

            initializeData() {
                if (!localStorage.getItem('userAppData')) {
                    const defaultData = {
                        receivedFiles: [
                            { id: 1, name: 'دليل الموظف الجديد.pdf', author: 'إدارة الموارد البشرية', date: '2024-01-15', type: 'pdf' },
                            { id: 2, name: 'السياسات المحدثة.doc', author: 'الإدارة العامة', date: '2024-01-14', type: 'doc' },
                            { id: 3, name: 'تعليمات السلامة.jpg', author: 'قسم الأمان', date: '2024-01-13', type: 'image' }
                        ],
                        publishedFiles: []
                    };
                    this.saveData(defaultData);
                }
            }

            getData() {
                return JSON.parse(localStorage.getItem('userAppData'));
            }

            saveData(data) {
                localStorage.setItem('userAppData', JSON.stringify(data));
            }

            addReceivedFile(file) {
                const data = this.getData();
                file.id = Date.now();
                file.date = new Date().toISOString().split('T')[0];
                data.receivedFiles.unshift(file);
                this.saveData(data);
                this.loadReceivedFiles();
            }

            publishFile(file) {
                const data = this.getData();
                const publishedFile = {
                    id: Date.now(),
                    name: file.name,
                    author: 'أنت',
                    date: new Date().toISOString().split('T')[0],
                    type: this.getFileType(file.type),
                    size: this.formatFileSize(file.size)
                };
                data.publishedFiles.unshift(publishedFile);
                this.saveData(data);
                
                // محاكاة إرسال للشاشات
                this.broadcastToDisplays(publishedFile);
                
                alert(`تم نشر الملف بنجاح!
📄 ${publishedFile.name}
📅 ${publishedFile.date}
📊 ${publishedFile.size}

سيظهر الآن على جميع الشاشات المتصلة.`);
            }

            broadcastToDisplays(file) {
                // محاكاة إرسال للشاشات
                const displayData = {
                    currentContent: file,
                    timestamp: Date.now()
                };
                localStorage.setItem('displayContent', JSON.stringify(displayData));
            }

            getFileType(mimeType) {
                if (mimeType.includes('pdf')) return 'pdf';
                if (mimeType.includes('image')) return 'image';
                if (mimeType.includes('word') || mimeType.includes('document')) return 'doc';
                return 'other';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            loadReceivedFiles() {
                const data = this.getData();
                const container = document.getElementById('receivedFiles');
                
                if (data.receivedFiles.length === 0) {
                    container.innerHTML = '<div style="text-align: center; color: #718096; padding: 40px;">لا توجد ملفات مستلمة</div>';
                    return;
                }

                container.innerHTML = data.receivedFiles.map(file => `
                    <div class="file-item">
                        <div class="file-info">
                            <div class="file-icon" style="background: ${this.getFileColor(file.type)}">
                                ${this.getFileIcon(file.type)}
                            </div>
                            <div>
                                <div style="font-weight: 600; color: #2d3748;">${file.name}</div>
                                <div style="font-size: 14px; color: #718096;">من: ${file.author} • ${file.date}</div>
                            </div>
                        </div>
                        <button class="btn btn-secondary" onclick="viewFile(${file.id})" style="padding: 8px 15px; font-size: 14px;">عرض</button>
                    </div>
                `).join('');
            }

            getFileColor(type) {
                const colors = {
                    pdf: '#e53e3e',
                    doc: '#3182ce',
                    image: '#38a169',
                    other: '#718096'
                };
                return colors[type] || colors.other;
            }

            getFileIcon(type) {
                const icons = {
                    pdf: 'PDF',
                    doc: 'DOC',
                    image: '🖼️',
                    other: '📄'
                };
                return icons[type] || icons.other;
            }

            simulateIncomingContent() {
                // محاكاة استقبال محتوى جديد كل 30 ثانية
                setInterval(() => {
                    if (Math.random() > 0.7) { // 30% احتمال
                        this.showIncomingNotification();
                    }
                }, 30000);
            }

            showIncomingNotification() {
                const sampleBooks = [
                    { name: 'تعليمات جديدة للعمل عن بُعد', author: 'إدارة الموارد البشرية', icon: '💼' },
                    { name: 'دليل استخدام النظام الجديد', author: 'قسم تقنية المعلومات', icon: '💻' },
                    { name: 'سياسة الأمان المحدثة', author: 'قسم الأمان', icon: '🔒' },
                    { name: 'تقرير الأداء الشهري', author: 'الإدارة العامة', icon: '📊' }
                ];

                const randomBook = sampleBooks[Math.floor(Math.random() * sampleBooks.length)];
                
                document.getElementById('bookPreview').textContent = randomBook.icon;
                document.getElementById('bookTitle').textContent = randomBook.name;
                document.getElementById('bookAuthor').textContent = `من: ${randomBook.author}`;
                
                document.getElementById('overlay').classList.add('show');
                document.getElementById('notificationPopup').classList.add('show');
                
                // حفظ البيانات للاستخدام عند الحفظ
                window.currentNotification = randomBook;
            }
        }

        // إنشاء مثيل من مدير المحتوى
        const contentManager = new ContentManager();

        // دوال التفاعل
        function selectFile() {
            document.getElementById('fileInput').click();
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                contentManager.publishFile(file);
            }
        }

        function handleDrop(event) {
            event.preventDefault();
            const publishArea = event.target.closest('.publish-area');
            publishArea.classList.remove('dragover');
            
            const file = event.dataTransfer.files[0];
            if (file) {
                contentManager.publishFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.target.closest('.publish-area').classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.target.closest('.publish-area').classList.remove('dragover');
        }

        function saveToReceived() {
            if (window.currentNotification) {
                contentManager.addReceivedFile({
                    name: window.currentNotification.name,
                    author: window.currentNotification.author,
                    type: 'pdf'
                });
                closeNotification();
                alert('تم حفظ الملف في المستلمة بنجاح!');
            }
        }

        function closeNotification() {
            document.getElementById('overlay').classList.remove('show');
            document.getElementById('notificationPopup').classList.remove('show');
            window.currentNotification = null;
        }

        function viewFile(fileId) {
            alert('عرض الملف رقم: ' + fileId);
        }

        // إغلاق النافذة عند النقر على الخلفية
        document.getElementById('overlay').addEventListener('click', closeNotification);
    </script>
</body>
</html>
