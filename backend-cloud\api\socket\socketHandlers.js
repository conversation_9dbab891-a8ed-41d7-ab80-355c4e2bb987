const jwt = require('jsonwebtoken');
const { supabase } = require('../config/supabase');

const JWT_SECRET = process.env.JWT_SECRET || 'tv-office-super-secret-jwt-key-2024';

const setupSocketHandlers = (io) => {
  // Middleware للتحقق من المصادقة
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, JWT_SECRET);
      
      // جلب بيانات المستخدم من قاعدة البيانات
      const { data: user, error } = await supabase
        .from('users')
        .select('id, username, full_name, user_type, is_active')
        .eq('id', decoded.userId)
        .eq('is_active', true)
        .single();

      if (error || !user) {
        return next(new Error('User not found or inactive'));
      }

      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user.full_name} (${socket.user.user_type})`);

    // انضمام المستخدم لغرفة حسب نوع المستخدم
    socket.join(socket.user.user_type);
    socket.join(`user_${socket.user.id}`);

    // معالج لطلب الاتصال بشاشة العرض
    socket.on('connectToDisplay', async (data) => {
      try {
        if (socket.user.user_type !== 'DISPLAY_OP' && socket.user.user_type !== 'MAIN_ADMIN') {
          socket.emit('error', { message: 'ليس لديك صلاحية للاتصال بالشاشات' });
          return;
        }

        const { displayId } = data;
        
        // التحقق من أن الشاشة تخص المستخدم أو أنه مدير رئيسي
        const { data: display, error } = await supabase
          .from('displays')
          .select('*')
          .eq('id', displayId)
          .single();

        if (error || !display) {
          socket.emit('error', { message: 'الشاشة غير موجودة' });
          return;
        }

        if (socket.user.user_type !== 'MAIN_ADMIN' && display.operator_id !== socket.user.id) {
          socket.emit('error', { message: 'ليس لديك صلاحية للاتصال بهذه الشاشة' });
          return;
        }

        // انضمام لغرفة الشاشة
        socket.join(`display_${displayId}`);
        
        // تحديث حالة الاتصال في قاعدة البيانات
        const { error: updateError } = await supabase
          .from('displays')
          .update({ is_connected: true })
          .eq('id', displayId);

        if (updateError) {
          console.error('Update display connection error:', updateError);
          socket.emit('error', { message: 'خطأ في تحديث حالة الاتصال' });
          return;
        }

        socket.emit('displayConnected', { displayId });
        
        // إشعار المديرين بالاتصال
        io.to('MAIN_ADMIN').emit('displayConnectionChanged', {
          display: { ...display, is_connected: true },
          operator: socket.user
        });

      } catch (error) {
        console.error('Connect to display error:', error);
        socket.emit('error', { message: 'خطأ في الاتصال بالشاشة' });
      }
    });

    // معالج لقطع الاتصال بشاشة العرض
    socket.on('disconnectFromDisplay', async (data) => {
      try {
        const { displayId } = data;
        
        // التحقق من أن الشاشة تخص المستخدم
        const { data: display, error } = await supabase
          .from('displays')
          .select('*')
          .eq('id', displayId)
          .single();

        if (error || !display) {
          socket.emit('error', { message: 'الشاشة غير موجودة' });
          return;
        }

        if (socket.user.user_type !== 'MAIN_ADMIN' && display.operator_id !== socket.user.id) {
          socket.emit('error', { message: 'ليس لديك صلاحية لقطع الاتصال بهذه الشاشة' });
          return;
        }

        // مغادرة غرفة الشاشة
        socket.leave(`display_${displayId}`);
        
        // تحديث حالة الاتصال
        const { error: updateError } = await supabase
          .from('displays')
          .update({ is_connected: false })
          .eq('id', displayId);

        if (updateError) {
          console.error('Update display disconnection error:', updateError);
        }

        socket.emit('displayDisconnected', { displayId });
        
        // إشعار المديرين بقطع الاتصال
        io.to('MAIN_ADMIN').emit('displayConnectionChanged', {
          display: { ...display, is_connected: false },
          operator: socket.user
        });

      } catch (error) {
        console.error('Disconnect from display error:', error);
        socket.emit('error', { message: 'خطأ في قطع الاتصال' });
      }
    });

    // معالج لإرسال ملف للعرض
    socket.on('sendFileToDisplay', async (data) => {
      try {
        const { fileId, displayId } = data;
        
        // الحصول على بيانات الملف
        const { data: file, error: fileError } = await supabase
          .from('files')
          .select(`
            *,
            publisher:users!inner(id, full_name, department)
          `)
          .eq('id', fileId)
          .eq('is_deleted', false)
          .single();

        if (fileError || !file) {
          socket.emit('error', { message: 'الملف غير موجود' });
          return;
        }

        // التحقق من وجود الشاشة
        const { data: display, error: displayError } = await supabase
          .from('displays')
          .select('*')
          .eq('id', displayId)
          .single();

        if (displayError || !display) {
          socket.emit('error', { message: 'الشاشة غير موجودة' });
          return;
        }

        // إرسال الملف لشاشة العرض
        io.to(`display_${displayId}`).emit('newFileToDisplay', {
          file,
          timestamp: new Date().toISOString(),
          sender: socket.user
        });

        socket.emit('fileSentToDisplay', { fileId, displayId });

        // إشعار المديرين
        io.to('MAIN_ADMIN').emit('fileDisplayed', {
          file,
          display,
          sender: socket.user
        });

      } catch (error) {
        console.error('Send file to display error:', error);
        socket.emit('error', { message: 'خطأ في إرسال الملف للعرض' });
      }
    });

    // معالج لإشعار نشر ملف جديد
    socket.on('filePublished', async (data) => {
      try {
        const { fileId } = data;
        
        // الحصول على بيانات الملف
        const { data: file, error } = await supabase
          .from('files')
          .select(`
            *,
            publisher:users!inner(id, full_name, department)
          `)
          .eq('id', fileId)
          .single();

        if (error || !file) {
          return;
        }

        // إشعار جميع المستخدمين بالملف الجديد
        socket.broadcast.emit('newFilePublished', {
          file,
          publisher: socket.user,
          timestamp: new Date().toISOString()
        });

        // إشعار خاص لمديري الأقسام (استلام تلقائي)
        io.to('DEPT_MANAGER').emit('autoReceiveFile', {
          file,
          publisher: socket.user
        });

      } catch (error) {
        console.error('File published notification error:', error);
      }
    });

    // معالج عند قطع الاتصال
    socket.on('disconnect', async () => {
      console.log(`User disconnected: ${socket.user.full_name}`);
      
      // تحديث حالة الاتصال لجميع الشاشات التي يديرها المستخدم
      if (socket.user.user_type === 'DISPLAY_OP') {
        try {
          const { error } = await supabase
            .from('displays')
            .update({ is_connected: false })
            .eq('operator_id', socket.user.id);

          if (error) {
            console.error('Update displays on disconnect error:', error);
          }

          // إشعار المديرين بقطع الاتصال
          io.to('MAIN_ADMIN').emit('operatorDisconnected', {
            operatorId: socket.user.id,
            operatorName: socket.user.full_name
          });
        } catch (error) {
          console.error('Disconnect cleanup error:', error);
        }
      }
    });

    // معالج للحصول على حالة الشاشات
    socket.on('getDisplaysStatus', async () => {
      try {
        const { data: displays, error } = await supabase
          .from('displays')
          .select(`
            *,
            operator:users!inner(id, full_name, username)
          `)
          .order('created_at', { ascending: false });

        if (error) {
          socket.emit('error', { message: 'خطأ في جلب حالة الشاشات' });
          return;
        }

        socket.emit('displaysStatus', displays);
      } catch (error) {
        console.error('Get displays status error:', error);
        socket.emit('error', { message: 'خطأ في جلب حالة الشاشات' });
      }
    });

    // معالج للانضمام لغرفة شاشة معينة (للعرض)
    socket.on('joinDisplayRoom', (data) => {
      const { displayId } = data;
      socket.join(`display_${displayId}`);
      socket.emit('joinedDisplayRoom', { displayId });
    });

    // معالج لمغادرة غرفة شاشة
    socket.on('leaveDisplayRoom', (data) => {
      const { displayId } = data;
      socket.leave(`display_${displayId}`);
      socket.emit('leftDisplayRoom', { displayId });
    });
  });

  return io;
};

module.exports = { setupSocketHandlers };
