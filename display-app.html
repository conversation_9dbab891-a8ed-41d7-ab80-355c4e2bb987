<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شاشة العرض - نظام إدارة المحتوى</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            overflow: hidden;
        }

        .display-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .header-bar {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 100;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: bold;
            color: #4a5568;
        }

        .status-info {
            display: flex;
            align-items: center;
            gap: 20px;
            color: #4a5568;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #48bb78;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .content-area {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            position: relative;
        }

        .content-display {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            padding: 60px;
            text-align: center;
            max-width: 800px;
            width: 100%;
            min-height: 500px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .content-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .book-preview {
            width: 300px;
            height: 400px;
            margin: 0 auto 30px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 120px;
            position: relative;
            transition: all 0.3s ease;
        }

        .book-preview:hover {
            transform: scale(1.05);
        }

        .content-title {
            font-size: 36px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .content-author {
            font-size: 20px;
            color: #718096;
            margin-bottom: 20px;
        }

        .content-date {
            font-size: 16px;
            color: #a0aec0;
            background: #f7fafc;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
        }

        .waiting-state {
            color: #718096;
            font-size: 24px;
            text-align: center;
        }

        .waiting-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.7;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .fullscreen-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            transition: all 0.3s ease;
            z-index: 200;
        }

        .fullscreen-btn:hover {
            background: white;
            transform: scale(1.1);
        }

        .connection-status {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            color: #4a5568;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 200;
        }

        /* وضع العرض الكامل */
        .fullscreen-mode {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
        }

        .fullscreen-mode .header-bar {
            display: none;
        }

        .fullscreen-mode .content-area {
            padding: 20px;
        }

        .fullscreen-mode .content-display {
            max-width: none;
            width: 100%;
            height: 100%;
            border-radius: 0;
            padding: 80px;
        }

        .fullscreen-mode .book-preview {
            width: 400px;
            height: 500px;
            font-size: 150px;
        }

        .fullscreen-mode .content-title {
            font-size: 48px;
        }

        .fullscreen-mode .content-author {
            font-size: 24px;
        }

        .fullscreen-mode .fullscreen-btn {
            display: none;
        }

        .slide-in {
            animation: slideInFromRight 0.8s ease-out;
        }

        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out;
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="display-container" id="displayContainer">
        <!-- الشريط العلوي -->
        <div class="header-bar" id="headerBar">
            <div class="logo">
                📺 شاشة العرض
            </div>
            <div class="status-info">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>متصل</span>
                </div>
                <div id="currentTime"></div>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <div class="content-display" id="contentDisplay">
                <!-- حالة الانتظار -->
                <div class="waiting-state" id="waitingState">
                    <div class="waiting-icon">⏳</div>
                    <div>في انتظار محتوى جديد...</div>
                    <div style="font-size: 16px; margin-top: 10px; opacity: 0.7;">
                        سيتم عرض المحتوى تلقائياً عند النشر
                    </div>
                </div>

                <!-- عرض المحتوى -->
                <div id="contentView" style="display: none;">
                    <div class="book-preview" id="bookPreview">
                        📖
                    </div>
                    <div class="content-title" id="contentTitle">عنوان المحتوى</div>
                    <div class="content-author" id="contentAuthor">المؤلف</div>
                    <div class="content-date" id="contentDate">تاريخ النشر</div>
                </div>
            </div>
        </div>

        <!-- زر العرض الكامل -->
        <button class="fullscreen-btn" onclick="toggleFullscreen()" title="العرض الكامل">
            ⛶
        </button>

        <!-- حالة الاتصال -->
        <div class="connection-status">
            🌐 متصل بالشبكة
        </div>
    </div>

    <script>
        class DisplayApp {
            constructor() {
                this.isFullscreen = false;
                this.currentContent = null;
                this.init();
            }

            init() {
                this.updateTime();
                this.checkForContent();
                this.startContentMonitoring();
                this.setupKeyboardShortcuts();
                
                // تحديث الوقت كل ثانية
                setInterval(() => this.updateTime(), 1000);
            }

            updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                document.getElementById('currentTime').textContent = timeString;
            }

            checkForContent() {
                const displayData = localStorage.getItem('displayContent');
                if (displayData) {
                    const content = JSON.parse(displayData);
                    if (content.currentContent && content.currentContent !== this.currentContent) {
                        this.displayContent(content.currentContent);
                    }
                }
            }

            startContentMonitoring() {
                // مراقبة التغييرات في المحتوى كل ثانية
                setInterval(() => {
                    this.checkForContent();
                }, 1000);

                // محاكاة محتوى تجريبي كل 45 ثانية
                setInterval(() => {
                    if (Math.random() > 0.6) {
                        this.simulateNewContent();
                    }
                }, 45000);
            }

            simulateNewContent() {
                const sampleContent = [
                    { 
                        name: 'دليل السلامة في المكتب', 
                        author: 'قسم الأمان والسلامة', 
                        icon: '🛡️',
                        date: new Date().toLocaleDateString('ar-SA')
                    },
                    { 
                        name: 'تعليمات العمل الجديدة', 
                        author: 'إدارة الموارد البشرية', 
                        icon: '📋',
                        date: new Date().toLocaleDateString('ar-SA')
                    },
                    { 
                        name: 'تقرير الأداء الشهري', 
                        author: 'الإدارة العامة', 
                        icon: '📊',
                        date: new Date().toLocaleDateString('ar-SA')
                    },
                    { 
                        name: 'دليل استخدام النظام الجديد', 
                        author: 'قسم تقنية المعلومات', 
                        icon: '💻',
                        date: new Date().toLocaleDateString('ar-SA')
                    }
                ];

                const randomContent = sampleContent[Math.floor(Math.random() * sampleContent.length)];
                this.displayContent(randomContent);
            }

            displayContent(content) {
                this.currentContent = content;
                
                // إخفاء حالة الانتظار
                document.getElementById('waitingState').style.display = 'none';
                
                // عرض المحتوى الجديد
                const contentView = document.getElementById('contentView');
                contentView.style.display = 'block';
                contentView.classList.add('slide-in');
                
                // تحديث المحتوى
                document.getElementById('bookPreview').textContent = content.icon || '📖';
                document.getElementById('contentTitle').textContent = content.name;
                document.getElementById('contentAuthor').textContent = `بواسطة: ${content.author}`;
                document.getElementById('contentDate').textContent = `تاريخ النشر: ${content.date}`;

                // إزالة الكلاس بعد انتهاء الأنيميشن
                setTimeout(() => {
                    contentView.classList.remove('slide-in');
                }, 800);

                console.log('تم عرض محتوى جديد:', content.name);
            }

            toggleFullscreen() {
                const container = document.getElementById('displayContainer');
                
                if (!this.isFullscreen) {
                    // تفعيل العرض الكامل
                    container.classList.add('fullscreen-mode');
                    
                    // طلب العرض الكامل من المتصفح
                    if (document.documentElement.requestFullscreen) {
                        document.documentElement.requestFullscreen();
                    } else if (document.documentElement.webkitRequestFullscreen) {
                        document.documentElement.webkitRequestFullscreen();
                    } else if (document.documentElement.msRequestFullscreen) {
                        document.documentElement.msRequestFullscreen();
                    }
                    
                    this.isFullscreen = true;
                    
                    // إخفاء مؤشر الماوس بعد 3 ثوان
                    this.setupMouseHiding();
                    
                } else {
                    // إلغاء العرض الكامل
                    container.classList.remove('fullscreen-mode');
                    document.body.style.cursor = 'default';
                    
                    // إلغاء العرض الكامل من المتصفح
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                    
                    this.isFullscreen = false;
                }
            }

            setupMouseHiding() {
                let mouseTimer;
                const hideMouseCursor = () => {
                    document.body.style.cursor = 'none';
                };
                
                const showMouseCursor = () => {
                    document.body.style.cursor = 'default';
                    clearTimeout(mouseTimer);
                    mouseTimer = setTimeout(hideMouseCursor, 3000);
                };
                
                document.addEventListener('mousemove', showMouseCursor);
                mouseTimer = setTimeout(hideMouseCursor, 3000);
            }

            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    // F11 للعرض الكامل
                    if (e.key === 'F11') {
                        e.preventDefault();
                        this.toggleFullscreen();
                    }
                    
                    // ESC للخروج من العرض الكامل
                    if (e.key === 'Escape' && this.isFullscreen) {
                        this.toggleFullscreen();
                    }
                });
            }
        }

        // تشغيل التطبيق
        const displayApp = new DisplayApp();

        // دالة العرض الكامل (للاستخدام من HTML)
        function toggleFullscreen() {
            displayApp.toggleFullscreen();
        }

        // التعامل مع تغيير حالة العرض الكامل من المتصفح
        document.addEventListener('fullscreenchange', function() {
            if (!document.fullscreenElement && displayApp.isFullscreen) {
                displayApp.toggleFullscreen();
            }
        });

        // عرض محتوى تجريبي بعد 5 ثوان
        setTimeout(() => {
            displayApp.simulateNewContent();
        }, 5000);
    </script>
</body>
</html>
