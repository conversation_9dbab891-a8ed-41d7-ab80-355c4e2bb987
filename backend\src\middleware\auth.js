const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'رمز الوصول مطلوب' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        fullName: true,
        jobTitle: true,
        department: true,
        userType: true,
        isActive: true
      }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({ message: 'المستخدم غير موجود أو غير نشط' });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({ message: 'رمز الوصول غير صالح' });
  }
};

const authorizeRoles = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: 'غير مصرح بالوصول' });
    }

    if (!roles.includes(req.user.userType)) {
      return res.status(403).json({ message: 'ليس لديك صلاحية للوصول لهذه الخدمة' });
    }

    next();
  };
};

module.exports = {
  authenticateToken,
  authorizeRoles
};
