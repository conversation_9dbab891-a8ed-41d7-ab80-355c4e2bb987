import React, { useState, useEffect } from 'react';
import { X, Download, Eye, Trash2, FileText, User, Calendar } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { formatDate, formatFileSize } from '@/types';
import api from '@/services/api';
import toast from 'react-hot-toast';

interface ClipboardModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FileItem {
  id: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  createdAt: string;
  publisher: {
    fullName: string;
    department: string;
  };
}

const ClipboardModal: React.FC<ClipboardModalProps> = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchFiles();
    }
  }, [isOpen]);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const response = await api.get('/files');
      setFiles(response.data);
    } catch (error) {
      toast.error('خطأ في تحميل الملفات');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (fileId: string, fileName: string) => {
    try {
      const response = await api.get(`/files/${fileId}/download`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success('تم تحميل الملف بنجاح');
    } catch (error) {
      toast.error('خطأ في تحميل الملف');
    }
  };

  const handleDelete = async (fileId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الملف؟')) return;
    
    try {
      await api.delete(`/files/${fileId}`);
      setFiles(files.filter(f => f.id !== fileId));
      toast.success('تم حذف الملف بنجاح');
    } catch (error) {
      toast.error('خطأ في حذف الملف');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50">
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-4xl bg-white rounded-xl shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">الحافظة</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="mr-3 text-gray-600">جاري التحميل...</span>
              </div>
            ) : files.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">لا توجد ملفات في الحافظة</p>
              </div>
            ) : (
              <div className="space-y-3">
                {files.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center space-x-4 space-x-reverse flex-1">
                      <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FileText className="h-5 w-5 text-blue-600" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {file.fileName}
                        </h4>
                        <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500 mt-1">
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <User className="h-3 w-3" />
                            <span>{file.publisher.fullName}</span>
                          </div>
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(file.createdAt)}</span>
                          </div>
                          <span>{formatFileSize(file.fileSize)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => setSelectedFile(file)}
                        className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                        title="عرض"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDownload(file.id, file.originalName)}
                        className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                        title="تحميل"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                      {user?.userType === 'MAIN_ADMIN' && (
                        <button
                          onClick={() => handleDelete(file.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* File Viewer Modal */}
      {selectedFile && (
        <div className="fixed inset-0 z-60 bg-black bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h4 className="font-medium">{selectedFile.fileName}</h4>
              <button
                onClick={() => setSelectedFile(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="p-4 h-96 bg-gray-100 flex items-center justify-center">
              <div className="text-center">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">عارض الملفات قيد التطوير</p>
                <p className="text-sm text-gray-500 mt-2">{selectedFile.originalName}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClipboardModal;
