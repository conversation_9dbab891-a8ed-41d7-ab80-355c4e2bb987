<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكي شاشة التلفاز</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Arial', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            height: 100vh; 
            display: flex; 
            flex-direction: column; 
            justify-content: center; 
            align-items: center; 
            text-align: center;
        }
        
        .container { 
            max-width: 600px; 
            padding: 40px; 
            background: rgba(255, 255, 255, 0.1); 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo { 
            font-size: 48px; 
            margin-bottom: 20px; 
        }
        
        .title { 
            font-size: 32px; 
            font-weight: bold; 
            margin-bottom: 30px; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .security-code { 
            font-size: 64px; 
            font-weight: 900; 
            background: white; 
            color: #374151; 
            padding: 20px 40px; 
            border-radius: 15px; 
            margin: 20px 0; 
            letter-spacing: 6px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            font-family: 'Courier New', monospace;
        }
        
        .instructions { 
            font-size: 18px; 
            margin-bottom: 15px; 
            line-height: 1.6;
        }
        
        .status { 
            font-size: 18px; 
            padding: 12px 25px; 
            border-radius: 10px; 
            margin-top: 15px; 
            font-weight: bold;
        }
        
        .status.waiting { 
            background: rgba(59, 130, 246, 0.3); 
            border: 2px solid #3b82f6; 
        }
        
        .status.connected { 
            background: rgba(16, 185, 129, 0.3); 
            border: 2px solid #10b981; 
        }
        
        .ip-info { 
            font-size: 16px; 
            margin-top: 15px; 
            opacity: 0.9;
            background: rgba(0,0,0,0.2);
            padding: 10px;
            border-radius: 8px;
        }
        
        .content-area { 
            display: none; 
            position: fixed; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 100%; 
            background: black; 
            z-index: 1000;
        }
        
        .file-type-header { 
            background: transparent; 
            color: black; 
            padding: 20px; 
            text-align: center; 
            position: absolute; 
            top: 0; 
            left: 50%; 
            transform: translateX(-50%); 
            z-index: 10;
        }
        
        .file-type-text { 
            font-size: 36px; 
            font-weight: 900; 
            margin: 0; 
            color: black;
        }

        .demo-note {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            color: #fff;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
        }

        .refresh-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            transition: all 0.2s;
        }

        .refresh-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📺</div>
        <div class="title">محاكي شاشة التلفاز</div>
        
        <div class="instructions">
            استخدم رمز الأمان التالي في البرنامج:
        </div>
        
        <div class="security-code" id="securityCode">123456</div>
        
        <div class="status waiting" id="status">
            🔄 في انتظار الاتصال...
        </div>
        
        <div class="ip-info">
            <strong>عنوان IP:</strong> *************:8080<br>
            <strong>رمز الأمان:</strong> <span id="codeDisplay">123456</span>
        </div>

        <div class="demo-note">
            <strong>📝 ملاحظة:</strong> هذا محاكي للتجربة<br>
            استخدم IP: <strong>*************</strong><br>
            ورمز الأمان: <strong><span id="codeDisplay2">123456</span></strong>
        </div>

        <button class="refresh-btn" onclick="generateNewCode()">🔄 رمز جديد</button>
        
        <div class="instructions" style="font-size: 14px; margin-top: 20px; opacity: 0.8;">
            💡 في البرنامج: شاشات العرض ← إضافة شاشة ← أدخل البيانات أعلاه
        </div>
    </div>

    <!-- Content Display Area -->
    <div class="content-area" id="contentArea">
        <!-- Content will be displayed here -->
    </div>

    <script>
        let securityCode = '123456';
        let isConnected = false;

        // Generate random 6-digit security code
        function generateSecurityCode() {
            return Math.floor(100000 + Math.random() * 900000).toString();
        }

        // Generate new code manually
        function generateNewCode() {
            securityCode = generateSecurityCode();
            updateCodeDisplay();
        }

        // Update code display
        function updateCodeDisplay() {
            document.getElementById('securityCode').textContent = securityCode;
            document.getElementById('codeDisplay').textContent = securityCode;
            document.getElementById('codeDisplay2').textContent = securityCode;
        }

        // Check for connection attempts
        function checkForConnections() {
            try {
                const connectionAttempt = localStorage.getItem('tv_connection_attempt');
                if (connectionAttempt) {
                    const data = JSON.parse(connectionAttempt);
                    if (data.timestamp > Date.now() - 5000) { // Within last 5 seconds
                        console.log('🔗 محاولة اتصال جديدة:', data);
                        handleConnectionAttempt(data);
                        localStorage.removeItem('tv_connection_attempt');
                    }
                }

                const displayContentData = localStorage.getItem('tv_display_content');
                if (displayContentData) {
                    const data = JSON.parse(displayContentData);
                    console.log('📺 محتوى جديد للعرض:', data);
                    if (data.timestamp > Date.now() - 10000) { // Within last 10 seconds (increased time)
                        showContentOnTV(data);
                        localStorage.removeItem('tv_display_content');
                    } else {
                        console.log('⏰ المحتوى قديم، تم تجاهله');
                    }
                }
            } catch (e) {
                console.error('❌ خطأ في فحص الاتصالات:', e);
            }
        }

        // Handle connection attempts
        function handleConnectionAttempt(data) {
            console.log('محاولة اتصال:', data);
            
            if (data.token === securityCode) {
                isConnected = true;
                document.getElementById('status').innerHTML = '✅ متصل بنجاح مع: ' + data.deviceName;
                document.getElementById('status').className = 'status connected';
                
                // Store connection success
                localStorage.setItem('tv_connection_result', JSON.stringify({
                    success: true,
                    message: 'تم التوصيل بنجاح!',
                    timestamp: Date.now()
                }));
                
                // Hide setup screen after 3 seconds
                setTimeout(() => {
                    document.querySelector('.container').style.display = 'none';
                }, 3000);
                
            } else {
                console.log('رمز خاطئ. المطلوب:', securityCode, 'المرسل:', data.token);
                
                // Store connection failure
                localStorage.setItem('tv_connection_result', JSON.stringify({
                    success: false,
                    message: 'رمز الأمان غير صحيح',
                    timestamp: Date.now()
                }));
            }
        }

        // Display content on TV
        function showContentOnTV(message) {
            console.log('📺 بدء عرض المحتوى:', message);

            const contentArea = document.getElementById('contentArea');
            const { fileType, content, contentType } = message;

            // Hide the main container first
            document.querySelector('.container').style.display = 'none';

            let displayHTML = '';

            if (contentType === 'image' && content) {
                console.log('🖼️ عرض صورة');
                displayHTML = `
                    <div style="background: white; padding: 0; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div style="background: transparent; color: black; padding: 20px; text-align: center;">
                            <h1 style="font-size: 36px; font-weight: 900; margin: 0; color: black;">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: black; display: flex; align-items: center; justify-content: center;">
                            <img src="${content}" style="max-width: 100%; max-height: 100%; object-fit: contain;" onload="console.log('✅ تم تحميل الصورة')" onerror="console.log('❌ فشل تحميل الصورة')">
                        </div>
                        <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.7); color: white; padding: 10px 20px; border-radius: 20px; font-size: 14px;">
                            اضغط أي مكان للعودة
                        </div>
                    </div>
                `;
            } else if (contentType === 'pdf' && content) {
                console.log('📄 عرض PDF');
                displayHTML = `
                    <div style="background: white; padding: 0; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div style="background: transparent; color: black; padding: 20px; text-align: center;">
                            <h1 style="font-size: 36px; font-weight: 900; margin: 0; color: black;">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: white; display: flex; align-items: center; justify-content: center;">
                            <embed src="${content}" type="application/pdf" width="100%" height="100%" style="border: none;">
                        </div>
                    </div>
                `;
            } else {
                console.log('📄 عرض ملف عام');
                displayHTML = `
                    <div style="background: white; padding: 0; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div style="background: transparent; color: black; padding: 20px; text-align: center;">
                            <h1 style="font-size: 36px; font-weight: 900; margin: 0; color: black;">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; flex-direction: column; align-items: center; justify-content: center;">
                            <svg width="200" height="200" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="1" style="margin: 0 auto; display: block; opacity: 0.8;">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                            </svg>
                            <p style="color: white; font-size: 24px; margin-top: 20px; font-weight: bold;">${fileType}</p>
                        </div>
                        <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.7); color: white; padding: 10px 20px; border-radius: 20px; font-size: 14px;">
                            اضغط أي مكان للعودة
                        </div>
                    </div>
                `;
            }

            contentArea.innerHTML = displayHTML;
            contentArea.style.display = 'block';

            // Add click to return
            contentArea.onclick = function() {
                console.log('🔙 العودة للشاشة الرئيسية');
                contentArea.style.display = 'none';
                document.querySelector('.container').style.display = 'flex';
                contentArea.onclick = null;
            };

            console.log('✅ تم عرض المحتوى بنجاح');

            // Auto hide after 15 seconds for demo
            setTimeout(() => {
                if (contentArea.style.display === 'block') {
                    console.log('⏰ إخفاء تلقائي بعد 15 ثانية');
                    contentArea.style.display = 'none';
                    document.querySelector('.container').style.display = 'flex';
                    contentArea.onclick = null;
                }
            }, 15000);
        }

        // Initialize
        function init() {
            console.log('تم تشغيل محاكي التلفاز');
            updateCodeDisplay();
            
            // Check for connections every second
            setInterval(checkForConnections, 1000);
        }

        // Start when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
