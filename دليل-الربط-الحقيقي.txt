🚀 دليل الربط الحقيقي مع التلفزيونات
==========================================

📋 الخطوات السريعة:
===================

1️⃣ تشغيل الخادم:
   - انقر مرتين على ملف: start-server.bat
   - أو افتح CMD واكتب: python -m http.server 8080

2️⃣ في التلفاز:
   - افتح متصفح التلفاز
   - اذهب إلى: http://IP-الكمبيوتر:8080/tv-screen.html
   - مثال: http://************:8080/tv-screen.html

3️⃣ في البرنامج:
   - شاشات العرض ← إضافة شاشة
   - اسم الشاشة: "شاشة الاستقبال"
   - عنوان IP: انسخ من شاشة التلفاز
   - ربط الشاشة

4️⃣ النشر:
   - اذهب لقسم النشر
   - اختر ملف (صورة/PDF)
   - انشر الملف
   - سيظهر فوراً على التلفاز! 🎊

🔧 معرفة IP الكمبيوتر:
=======================
- Windows: اكتب في CMD: ipconfig
- ابحث عن: IPv4 Address
- مثال: ************

📺 معرفة IP التلفاز:
====================
- إعدادات التلفاز ← الشبكة ← معلومات الاتصال
- أو سيظهر تلقائياً في صفحة tv-screen.html

🚨 حل المشاكل:
===============
❌ لا يفتح الرابط في التلفاز:
   ✅ تأكد من تشغيل الخادم
   ✅ تأكد من نفس الشبكة
   ✅ جرب IP مختلف

❌ فشل الربط:
   ✅ تأكد من صحة IP
   ✅ أعد تحميل صفحة التلفاز
   ✅ تأكد من عدم وجود جدار حماية

❌ لا يظهر المحتوى:
   ✅ تأكد من الربط الناجح
   ✅ جرب ملف صغير الحجم
   ✅ أعد تحميل الصفحتين

💡 نصائح مهمة:
===============
- استخدم شبكة WiFi مستقرة
- تأكد من شحن/طاقة الأجهزة
- اغلق البرامج غير الضرورية
- استخدم ملفات أقل من 10MB

🎯 الملفات المطلوبة:
====================
✅ complete-system.html (البرنامج الرئيسي)
✅ tv-screen.html (صفحة التلفاز)
✅ start-server.bat (تشغيل الخادم)

🎊 النظام جاهز للاستخدام الحقيقي!
===================================
