import React, { useState, useEffect } from 'react';
import { X, Search, FileText, Download, Eye, Filter, Calendar, User, SortAsc, SortDesc, Grid, List, Clock, FileType, Building } from 'lucide-react';
import { useForm } from 'react-hook-form';
import api from '@/services/api';
import { formatDate, formatFileSize } from '@/types';
import toast from 'react-hot-toast';

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SearchFormData {
  search: string;
  publisher: string;
  department: string;
  fileType: string;
  dateFrom: string;
  dateTo: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface FileItem {
  id: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  createdAt: string;
  publisher: {
    fullName: string;
    department: string;
  };
}

const SearchModal: React.FC<SearchModalProps> = ({ isOpen, onClose }) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showSearchHistory, setShowSearchHistory] = useState(false);
  const [totalResults, setTotalResults] = useState(0);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue
  } = useForm<SearchFormData>({
    defaultValues: {
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }
  });

  const searchTerm = watch('search');

  useEffect(() => {
    if (isOpen) {
      // Load all files initially
      searchFiles({});
    }
  }, [isOpen]);

  const searchFiles = async (filters: Partial<SearchFormData>) => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      if (filters.search) {
        params.append('search', filters.search);
        // إضافة إلى تاريخ البحث
        if (filters.search.length > 2 && !searchHistory.includes(filters.search)) {
          setSearchHistory(prev => [filters.search!, ...prev.slice(0, 4)]);
        }
      }
      if (filters.publisher) params.append('publisher', filters.publisher);
      if (filters.department) params.append('department', filters.department);
      if (filters.fileType) params.append('fileType', filters.fileType);
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters.dateTo) params.append('dateTo', filters.dateTo);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);

      const response = await api.get(`/files?${params.toString()}`);
      setFiles(response.data);
      setTotalResults(response.data.length);
    } catch (error) {
      toast.error('خطأ في البحث');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = (data: SearchFormData) => {
    searchFiles(data);
  };

  const handleQuickSearch = (term: string) => {
    searchFiles({ search: term });
  };

  const handleDownload = async (fileId: string, fileName: string) => {
    try {
      const response = await api.get(`/files/${fileId}/download`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success('تم تحميل الملف بنجاح');
    } catch (error) {
      toast.error('خطأ في تحميل الملف');
    }
  };

  const clearSearch = () => {
    reset();
    searchFiles({});
    setShowSearchHistory(false);
  };

  const handleSortChange = (sortBy: string) => {
    const currentSort = watch('sortBy');
    const currentOrder = watch('sortOrder');

    let newOrder: 'asc' | 'desc' = 'desc';
    if (currentSort === sortBy && currentOrder === 'desc') {
      newOrder = 'asc';
    }

    setValue('sortBy', sortBy);
    setValue('sortOrder', newOrder);

    const formData = watch();
    searchFiles(formData);
  };

  const handleSearchFromHistory = (term: string) => {
    setValue('search', term);
    searchFiles({ search: term });
    setShowSearchHistory(false);
  };

  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'xls':
      case 'xlsx':
        return '📊';
      case 'ppt':
      case 'pptx':
        return '📋';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return '🖼️';
      case 'mp4':
      case 'avi':
      case 'mov':
        return '🎥';
      default:
        return '📁';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50">
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-5xl bg-white rounded-xl shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="h-10 w-10 bg-green-500 rounded-lg flex items-center justify-center">
                <Search className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">البحث في الملفات</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Search Form */}
          <div className="p-6 border-b border-gray-200">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Quick Search */}
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="flex-1 relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    {...register('search')}
                    type="text"
                    placeholder="ابحث عن ملف..."
                    className="input w-full pr-10"
                    onFocus={() => setShowSearchHistory(searchHistory.length > 0)}
                    onChange={(e) => {
                      if (e.target.value.length > 2 || e.target.value.length === 0) {
                        handleQuickSearch(e.target.value);
                      }
                    }}
                  />

                  {/* Search History Dropdown */}
                  {showSearchHistory && searchHistory.length > 0 && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                      <div className="p-2 border-b border-gray-100">
                        <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          <span>عمليات البحث الأخيرة</span>
                        </div>
                      </div>
                      {searchHistory.map((term, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleSearchFromHistory(term)}
                          className="w-full text-right px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          {term}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                <button
                  type="button"
                  onClick={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                  className="btn btn-secondary flex items-center space-x-2 space-x-reverse"
                  title={viewMode === 'list' ? 'عرض شبكي' : 'عرض قائمة'}
                >
                  {viewMode === 'list' ? <Grid className="h-4 w-4" /> : <List className="h-4 w-4" />}
                </button>

                <button
                  type="button"
                  onClick={() => setShowFilters(!showFilters)}
                  className={`btn ${showFilters ? 'btn-primary' : 'btn-secondary'} flex items-center space-x-2 space-x-reverse`}
                >
                  <Filter className="h-4 w-4" />
                  <span>فلاتر</span>
                </button>
                <button
                  type="button"
                  onClick={clearSearch}
                  className="btn btn-secondary"
                >
                  مسح
                </button>
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <div className="p-4 bg-gray-50 rounded-lg space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        <User className="inline h-4 w-4 ml-1" />
                        اسم الناشر
                      </label>
                      <input
                        {...register('publisher')}
                        type="text"
                        placeholder="اسم الناشر"
                        className="input w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        <Building className="inline h-4 w-4 ml-1" />
                        القسم
                      </label>
                      <input
                        {...register('department')}
                        type="text"
                        placeholder="اسم القسم"
                        className="input w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        <FileType className="inline h-4 w-4 ml-1" />
                        نوع الملف
                      </label>
                      <select {...register('fileType')} className="input w-full">
                        <option value="">جميع الأنواع</option>
                        <option value="pdf">PDF</option>
                        <option value="doc">Word</option>
                        <option value="xls">Excel</option>
                        <option value="ppt">PowerPoint</option>
                        <option value="image">صور</option>
                        <option value="video">فيديو</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        ترتيب حسب
                      </label>
                      <select {...register('sortBy')} className="input w-full">
                        <option value="createdAt">تاريخ الإنشاء</option>
                        <option value="fileName">اسم الملف</option>
                        <option value="fileSize">حجم الملف</option>
                        <option value="publisher">الناشر</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        <Calendar className="inline h-4 w-4 ml-1" />
                        من تاريخ
                      </label>
                      <input
                        {...register('dateFrom')}
                        type="date"
                        className="input w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        <Calendar className="inline h-4 w-4 ml-1" />
                        إلى تاريخ
                      </label>
                      <input
                        {...register('dateTo')}
                        type="date"
                        className="input w-full"
                      />
                    </div>
                    <div className="flex items-end">
                      <button
                        type="submit"
                        className="btn btn-primary w-full"
                      >
                        بحث متقدم
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Results */}
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                <span className="mr-3 text-gray-600">جاري البحث...</span>
              </div>
            ) : (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <p className="text-sm text-gray-600">
                    تم العثور على {files.length} ملف
                  </p>

                  {files.length > 0 && (
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-sm text-gray-500">ترتيب:</span>
                      <button
                        onClick={() => handleSortChange('fileName')}
                        className={`text-sm px-2 py-1 rounded transition-colors ${
                          watch('sortBy') === 'fileName'
                            ? 'bg-green-100 text-green-700'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        الاسم
                        {watch('sortBy') === 'fileName' && (
                          watch('sortOrder') === 'asc' ?
                            <SortAsc className="inline h-3 w-3 ml-1" /> :
                            <SortDesc className="inline h-3 w-3 ml-1" />
                        )}
                      </button>
                      <button
                        onClick={() => handleSortChange('createdAt')}
                        className={`text-sm px-2 py-1 rounded transition-colors ${
                          watch('sortBy') === 'createdAt'
                            ? 'bg-green-100 text-green-700'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        التاريخ
                        {watch('sortBy') === 'createdAt' && (
                          watch('sortOrder') === 'asc' ?
                            <SortAsc className="inline h-3 w-3 ml-1" /> :
                            <SortDesc className="inline h-3 w-3 ml-1" />
                        )}
                      </button>
                      <button
                        onClick={() => handleSortChange('fileSize')}
                        className={`text-sm px-2 py-1 rounded transition-colors ${
                          watch('sortBy') === 'fileSize'
                            ? 'bg-green-100 text-green-700'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        الحجم
                        {watch('sortBy') === 'fileSize' && (
                          watch('sortOrder') === 'asc' ?
                            <SortAsc className="inline h-3 w-3 ml-1" /> :
                            <SortDesc className="inline h-3 w-3 ml-1" />
                        )}
                      </button>
                    </div>
                  )}
                </div>

                {files.length === 0 ? (
                  <div className="text-center py-12">
                    <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">لم يتم العثور على ملفات</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {files.map((file) => (
                      <div
                        key={file.id}
                        className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center space-x-4 space-x-reverse flex-1">
                          <div className="h-12 w-12 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center text-2xl">
                            {getFileTypeIcon(file.fileName)}
                          </div>

                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {file.fileName}
                            </h4>
                            <p className="text-xs text-gray-500 truncate">
                              {file.originalName}
                            </p>
                            <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500 mt-1">
                              <div className="flex items-center space-x-1 space-x-reverse">
                                <User className="h-3 w-3" />
                                <span>{file.publisher.fullName}</span>
                              </div>
                              <div className="flex items-center space-x-1 space-x-reverse">
                                <Building className="h-3 w-3" />
                                <span>{file.publisher.department}</span>
                              </div>
                              <div className="flex items-center space-x-1 space-x-reverse">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(file.createdAt)}</span>
                              </div>
                              <span className="font-medium">{formatFileSize(file.fileSize)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => setSelectedFile(file)}
                            className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                            title="عرض"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDownload(file.id, file.originalName)}
                            className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                            title="تحميل"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* File Viewer Modal */}
      {selectedFile && (
        <div className="fixed inset-0 z-60 bg-black bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h4 className="font-medium">{selectedFile.fileName}</h4>
              <button
                onClick={() => setSelectedFile(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="p-4 h-96 bg-gray-100 flex items-center justify-center">
              <div className="text-center">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">عارض الملفات قيد التطوير</p>
                <p className="text-sm text-gray-500 mt-2">{selectedFile.originalName}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchModal;
