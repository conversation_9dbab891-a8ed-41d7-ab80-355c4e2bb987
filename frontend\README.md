# نظام إدارة التلفزيونات المكتبية - TV Office Management System

## 🌐 **الرابط المباشر للنظام**

### **للمستخدمين:**
- **الرابط الرئيسي:** `https://your-domain.com`
- **رابط النظام:** `https://your-domain.com/demo.html`

---

## 🚀 **طرق نشر النظام على الإنترنت**

### **الطريقة الأولى: GitHub Pages (مجاني)**

#### **الخطوات:**
1. **إنشاء حساب GitHub:**
   - اذهب إلى [github.com](https://github.com)
   - أنشئ حساب جديد

2. **إنشاء مستودع جديد:**
   - ان<PERSON>ر على "New Repository"
   - اسم المستودع: `tv-office-system`
   - اختر "Public"
   - انقر "Create Repository"

3. **رفع الملفات:**
   - ارفع جميع الملفات:
     - `index.html`
     - `demo.html`
     - `src/index.css`
   
4. **تفعيل GitHub Pages:**
   - اذهب إلى Settings → Pages
   - اختر "Deploy from a branch"
   - اختر "main branch"
   - احفظ

5. **الحصول على الرابط:**
   - الرابط سيكون: `https://username.github.io/tv-office-system`

---

### **الطريقة الثانية: Netlify (مجاني)**

#### **الخطوات:**
1. **اذهب إلى [netlify.com](https://netlify.com)**
2. **أنشئ حساب جديد**
3. **اسحب وأفلت المجلد** الذي يحتوي على الملفات
4. **احصل على الرابط:** `https://random-name.netlify.app`
5. **غير الاسم:** اذهب إلى Site Settings → Change Site Name

---

### **الطريقة الثالثة: Vercel (مجاني)**

#### **الخطوات:**
1. **اذهب إلى [vercel.com](https://vercel.com)**
2. **أنشئ حساب جديد**
3. **انقر "New Project"**
4. **ارفع الملفات**
5. **احصل على الرابط:** `https://tv-office.vercel.app`

---

## 👥 **معلومات المستخدمين**

### **حسابات النظام:**

#### **المدير الرئيسي:**
- **الإيميل:** `<EMAIL>`
- **كلمة المرور:** `admin123`
- **نوع الفرع:** A (الإدارة العامة)
- **الصلاحيات:** جميع الصلاحيات

#### **مستخدم فرع B:**
- **الإيميل:** `<EMAIL>`
- **كلمة المرور:** `sales123`
- **نوع الفرع:** B (المبيعات)

#### **مستخدم فرع C:**
- **الإيميل:** `<EMAIL>`
- **كلمة المرور:** `quality123`
- **نوع الفرع:** C (الجودة)

#### **شاشة العرض:**
- **الإيميل:** `<EMAIL>`
- **كلمة المرور:** `display123`
- **نوع الفرع:** D (شاشة العرض)

---

## 🔧 **إعدادات النظام**

### **أنواع الفروع:**
- **فرع A:** الإدارة العامة (جميع الصلاحيات)
- **فرع B:** المبيعات (الحافظة، البحث، المحذوفات، النشر)
- **فرع C:** الجودة (الحافظة، البحث، المحذوفات)
- **فرع D:** شاشات العرض (عرض المحتوى فقط)

### **الميزات:**
- ✅ **إدارة المستخدمين**
- ✅ **نشر المحتوى**
- ✅ **البحث في الملفات**
- ✅ **إدارة الملفات المحذوفة**
- ✅ **إدارة شاشات العرض**
- ✅ **نظام الصلاحيات**
- ✅ **واجهة متعددة اللغات (عربي/إنجليزي)**

---

## 📱 **التوافق**

### **المتصفحات المدعومة:**
- ✅ Chrome
- ✅ Firefox
- ✅ Safari
- ✅ Edge

### **الأجهزة المدعومة:**
- ✅ أجهزة الكمبيوتر
- ✅ الأجهزة اللوحية
- ✅ الهواتف الذكية
- ✅ شاشات التلفزيون

---

## 🆘 **الدعم التقني**

### **للمساعدة:**
- **الإيميل:** <EMAIL>
- **إنشاء حسابات جديدة:** من خلال المدير الرئيسي
- **إعادة تعيين كلمة المرور:** من خلال النظام

---

## 🔒 **الأمان**

- ✅ **تشفير البيانات المحلية**
- ✅ **نظام صلاحيات متقدم**
- ✅ **حماية من الوصول غير المصرح**
- ✅ **تسجيل العمليات**

---

## 📊 **إحصائيات الاستخدام**

- **عدد المستخدمين المتوقع:** 40+ مستخدم
- **عدد الفروع:** 4 فروع
- **نوع البيانات:** ملفات، صور، مستندات
- **التخزين:** محلي في المتصفح

---

## 🔄 **التحديثات**

- **الإصدار الحالي:** v1.0
- **آخر تحديث:** 2025-01-11
- **التحديثات القادمة:** تحسينات الأداء والميزات الجديدة
