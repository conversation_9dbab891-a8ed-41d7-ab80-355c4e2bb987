# دليل التشغيل السريع

## متطلبات النظام

- Node.js 18 أو أحدث
- PostgreSQL 14 أو أحدث
- Git

## خطوات التشغيل السريع

### 1. تحضير قاعدة البيانات

```bash
# إنشاء قاعدة بيانات جديدة
createdb tv_office_db

# أو باستخدام psql
psql -U postgres
CREATE DATABASE tv_office_db;
\q
```

### 2. إعداد متغيرات البيئة

```bash
# نسخ ملف البيئة
cp backend/.env.example backend/.env

# تحرير الملف وإضافة رابط قاعدة البيانات
# DATABASE_URL="postgresql://username:password@localhost:5432/tv_office_db"
# JWT_SECRET="your-super-secret-jwt-key-here"
```

### 3. التشغيل التلقائي

#### على Windows:
```bash
# تشغيل الملف التلقائي
start.bat
```

#### على Linux/Mac:
```bash
# جعل الملف قابل للتنفيذ
chmod +x start.sh

# تشغيل الملف التلقائي
./start.sh
```

### 4. التشغيل اليدوي

```bash
# تثبيت المتطلبات
npm run install:all

# إعداد قاعدة البيانات
cd backend
npm run db:setup
cd ..

# تشغيل النظام
npm run dev
```

## الوصول للنظام

- **التطبيق**: http://localhost:3000
- **API**: http://localhost:5000/api

## بيانات تسجيل الدخول

### المدير الرئيسي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### مدير التشغيل
- **اسم المستخدم**: operations_mgr
- **كلمة المرور**: 123456

### مدير القسم (الموارد البشرية)
- **اسم المستخدم**: dept_mgr_hr
- **كلمة المرور**: 123456

### مدير القسم (المالية)
- **اسم المستخدم**: dept_mgr_finance
- **كلمة المرور**: 123456

### مسؤول الاستعلامات
- **اسم المستخدم**: display_op
- **كلمة المرور**: 123456

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل PostgreSQL
sudo service postgresql start

# تأكد من صحة رابط قاعدة البيانات في .env
```

### خطأ في المنافذ
```bash
# تأكد من أن المنافذ 3000 و 5000 غير مستخدمة
netstat -tulpn | grep :3000
netstat -tulpn | grep :5000
```

### مشاكل في التثبيت
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules backend/node_modules frontend/node_modules
npm run install:all
```

## الدعم

للحصول على المساعدة:
1. راجع ملف README.md للتفاصيل الكاملة
2. تحقق من logs في terminal
3. تأكد من تشغيل جميع الخدمات المطلوبة
