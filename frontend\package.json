{"name": "tv-office-frontend", "version": "1.0.0", "description": "Frontend for TV Office Management System", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@tanstack/react-query": "^5.8.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-pdf": "^7.6.0", "pdfjs-dist": "^3.11.174"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}