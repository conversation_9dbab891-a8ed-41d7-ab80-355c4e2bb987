import { io, Socket } from 'socket.io-client';
import toast from 'react-hot-toast';
import { API_CONFIG, getAuthToken } from '../config/api';

class SocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isConnecting = false;

  // الاتصال بالخادم
  connect(): Socket | null {
    if (this.socket?.connected || this.isConnecting) {
      return this.socket;
    }

    const token = getAuthToken();
    if (!token) {
      console.warn('No auth token found, cannot connect to socket');
      return null;
    }

    this.isConnecting = true;

    try {
      this.socket = io(API_CONFIG.SOCKET_URL, {
        ...API_CONFIG.SOCKET_OPTIONS,
        auth: {
          token
        }
      });

      this.setupEventHandlers();
      return this.socket;
    } catch (error) {
      console.error('Socket connection error:', error);
      this.isConnecting = false;
      return null;
    }
  }

  // إعداد معالجات الأحداث
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // عند الاتصال بنجاح
    this.socket.on('connect', () => {
      console.log('Socket connected successfully');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      toast.success('تم الاتصال بالخادم بنجاح');
    });

    // عند قطع الاتصال
    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnecting = false;
      
      if (reason === 'io server disconnect') {
        // الخادم قطع الاتصال، لا نحاول إعادة الاتصال
        toast.error('تم قطع الاتصال من الخادم');
      } else {
        // قطع اتصال من العميل، سيحاول إعادة الاتصال تلقائياً
        toast.error('انقطع الاتصال بالخادم');
      }
    });

    // عند محاولة إعادة الاتصال
    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log(`Reconnection attempt ${attemptNumber}`);
      this.reconnectAttempts = attemptNumber;
      
      if (attemptNumber <= 3) {
        toast.loading(`محاولة إعادة الاتصال ${attemptNumber}...`);
      }
    });

    // عند نجاح إعادة الاتصال
    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`Reconnected after ${attemptNumber} attempts`);
      this.reconnectAttempts = 0;
      toast.success('تم استعادة الاتصال بالخادم');
    });

    // عند فشل إعادة الاتصال
    this.socket.on('reconnect_failed', () => {
      console.log('Failed to reconnect');
      toast.error('فشل في إعادة الاتصال بالخادم');
    });

    // معالجة الأخطاء
    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnecting = false;
      
      if (error.message.includes('Authentication')) {
        toast.error('خطأ في المصادقة، يرجى تسجيل الدخول مرة أخرى');
        // إعادة توجيه لصفحة تسجيل الدخول
        window.location.href = '/login';
      } else {
        toast.error('خطأ في الاتصال بالخادم');
      }
    });

    // معالجة الأخطاء العامة
    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
      toast.error(error.message || 'حدث خطأ في الاتصال');
    });
  }

  // قطع الاتصال
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
    }
  }

  // الحصول على Socket instance
  getSocket(): Socket | null {
    return this.socket;
  }

  // التحقق من حالة الاتصال
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // إرسال حدث
  emit(event: string, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('Socket not connected, cannot emit event:', event);
      toast.error('لا يوجد اتصال بالخادم');
    }
  }

  // الاستماع لحدث
  on(event: string, callback: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  // إلغاء الاستماع لحدث
  off(event: string, callback?: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }

  // دوال خاصة بالتطبيق

  // الاتصال بشاشة عرض
  connectToDisplay(displayId: string): void {
    this.emit('connectToDisplay', { displayId });
  }

  // قطع الاتصال بشاشة عرض
  disconnectFromDisplay(displayId: string): void {
    this.emit('disconnectFromDisplay', { displayId });
  }

  // إرسال ملف للعرض
  sendFileToDisplay(fileId: string, displayId: string): void {
    this.emit('sendFileToDisplay', { fileId, displayId });
  }

  // إشعار بنشر ملف جديد
  notifyFilePublished(fileId: string): void {
    this.emit('filePublished', { fileId });
  }

  // الانضمام لغرفة شاشة عرض
  joinDisplayRoom(displayId: string): void {
    this.emit('joinDisplayRoom', { displayId });
  }

  // مغادرة غرفة شاشة عرض
  leaveDisplayRoom(displayId: string): void {
    this.emit('leaveDisplayRoom', { displayId });
  }

  // طلب حالة الشاشات
  getDisplaysStatus(): void {
    this.emit('getDisplaysStatus');
  }

  // الاستماع للأحداث الخاصة بالتطبيق

  // ملف جديد تم نشره
  onNewFilePublished(callback: (data: any) => void): void {
    this.on('newFilePublished', callback);
  }

  // تغيير حالة اتصال الشاشة
  onDisplayConnectionChanged(callback: (data: any) => void): void {
    this.on('displayConnectionChanged', callback);
  }

  // ملف جديد للعرض
  onNewFileToDisplay(callback: (data: any) => void): void {
    this.on('newFileToDisplay', callback);
  }

  // استلام تلقائي للملف
  onAutoReceiveFile(callback: (data: any) => void): void {
    this.on('autoReceiveFile', callback);
  }

  // اتصال بشاشة عرض
  onDisplayConnected(callback: (data: any) => void): void {
    this.on('displayConnected', callback);
  }

  // قطع اتصال بشاشة عرض
  onDisplayDisconnected(callback: (data: any) => void): void {
    this.on('displayDisconnected', callback);
  }

  // تم إرسال ملف للعرض
  onFileSentToDisplay(callback: (data: any) => void): void {
    this.on('fileSentToDisplay', callback);
  }

  // حالة الشاشات
  onDisplaysStatus(callback: (data: any) => void): void {
    this.on('displaysStatus', callback);
  }

  // انضمام لغرفة شاشة
  onJoinedDisplayRoom(callback: (data: any) => void): void {
    this.on('joinedDisplayRoom', callback);
  }

  // مغادرة غرفة شاشة
  onLeftDisplayRoom(callback: (data: any) => void): void {
    this.on('leftDisplayRoom', callback);
  }

  // مشغل منقطع
  onOperatorDisconnected(callback: (data: any) => void): void {
    this.on('operatorDisconnected', callback);
  }

  // ملف تم عرضه
  onFileDisplayed(callback: (data: any) => void): void {
    this.on('fileDisplayed', callback);
  }

  // إلغاء جميع المستمعين
  removeAllListeners(): void {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
const socketService = new SocketService();

export default socketService;
