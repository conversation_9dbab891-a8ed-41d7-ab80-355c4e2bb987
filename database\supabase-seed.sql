-- بيانات أساسية لنظام إدارة التلفزيونات المكتبية
-- TV Office Management System Seed Data

-- إدراج المستخدمين الأساسيين
-- كلمات المرور مُشفرة باستخدام bcrypt

-- المدير الرئيسي
INSERT INTO users (id, username, password, full_name, job_title, department, user_type, is_active) VALUES
('11111111-1111-1111-1111-111111111111', 'admin', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hGzBqxe6u', 'المدير الرئيسي', 'مدير النظام', 'تقنية المعلومات', 'MAIN_ADMIN', true);

-- مدير التشغيل
INSERT INTO users (id, username, password, full_name, job_title, department, user_type, is_active) VALUES
('22222222-2222-2222-2222-222222222222', 'operations_mgr', '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد علي', 'مدير التشغيل', 'العمليات', 'OPERATIONS_MGR', true);

-- مدير الموارد البشرية
INSERT INTO users (id, username, password, full_name, job_title, department, user_type, is_active) VALUES
('33333333-3333-3333-3333-333333333333', 'dept_mgr_hr', '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة أحمد سالم', 'مدير الموارد البشرية', 'الموارد البشرية', 'DEPT_MANAGER', true);

-- مدير المالية
INSERT INTO users (id, username, password, full_name, job_title, department, user_type, is_active) VALUES
('44444444-4444-4444-4444-444444444444', 'dept_mgr_finance', '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد سعد الدين', 'مدير المالية', 'المالية', 'DEPT_MANAGER', true);

-- مسؤول الاستعلامات
INSERT INTO users (id, username, password, full_name, job_title, department, user_type, is_active) VALUES
('55555555-5555-5555-5555-555555555555', 'display_op', '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'علي حسن محمد', 'مسؤول الاستعلامات', 'خدمة العملاء', 'DISPLAY_OP', true);

-- إدراج شاشات العرض الأساسية
INSERT INTO displays (id, screen_id, screen_name, operator_id, is_connected) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'TV-001', 'شاشة الاستقبال الرئيسية', '55555555-5555-5555-5555-555555555555', false),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'TV-002', 'شاشة قاعة الاجتماعات', '55555555-5555-5555-5555-555555555555', false),
('cccccccc-cccc-cccc-cccc-cccccccccccc', 'TV-003', 'شاشة الكافتيريا', '55555555-5555-5555-5555-555555555555', false);

-- إدراج ملفات تجريبية (اختيارية)
INSERT INTO files (id, file_name, original_name, file_path, file_type, file_size, publisher_id, is_deleted) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'دليل الموظف الجديد', 'employee-handbook.pdf', '/uploads/sample-employee-handbook.pdf', 'application/pdf', 1024000, '11111111-1111-1111-1111-111111111111', false),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'سياسات الشركة', 'company-policies.pdf', '/uploads/sample-company-policies.pdf', 'application/pdf', 2048000, '22222222-2222-2222-2222-222222222222', false);

-- ربط الملفات بالمستقبلين
INSERT INTO file_receivers (file_id, receiver_id) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', '33333333-3333-3333-3333-333333333333'),
('dddddddd-dddd-dddd-dddd-dddddddddddd', '44444444-4444-4444-4444-444444444444'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '33333333-3333-3333-3333-333333333333'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '44444444-4444-4444-4444-444444444444'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '55555555-5555-5555-5555-555555555555');

-- تحديث الإحصائيات
ANALYZE users;
ANALYZE files;
ANALYZE displays;
ANALYZE password_reset_requests;
ANALYZE file_receivers;

-- إنشاء views مفيدة للاستعلامات
CREATE VIEW user_stats AS
SELECT 
    user_type,
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_active THEN 1 END) as active_users
FROM users 
GROUP BY user_type;

CREATE VIEW file_stats AS
SELECT 
    u.full_name as publisher_name,
    u.department,
    COUNT(f.id) as total_files,
    COUNT(CASE WHEN f.is_deleted = false THEN 1 END) as active_files,
    SUM(f.file_size) as total_size
FROM users u
LEFT JOIN files f ON u.id = f.publisher_id
GROUP BY u.id, u.full_name, u.department;

CREATE VIEW display_stats AS
SELECT 
    u.full_name as operator_name,
    COUNT(d.id) as total_displays,
    COUNT(CASE WHEN d.is_connected THEN 1 END) as connected_displays
FROM users u
LEFT JOIN displays d ON u.id = d.operator_id
WHERE u.user_type = 'DISPLAY_OP'
GROUP BY u.id, u.full_name;

-- إنشاء functions مفيدة
CREATE OR REPLACE FUNCTION get_user_permissions(user_id UUID)
RETURNS JSON AS $$
DECLARE
    user_record users%ROWTYPE;
    permissions JSON;
BEGIN
    SELECT * INTO user_record FROM users WHERE id = user_id;
    
    IF NOT FOUND THEN
        RETURN '{"error": "User not found"}'::JSON;
    END IF;
    
    CASE user_record.user_type
        WHEN 'MAIN_ADMIN' THEN
            permissions := '{
                "canViewHome": true,
                "canManageUsers": true,
                "canPublishFiles": true,
                "canViewFiles": true,
                "canDeleteFiles": true,
                "canManageDisplays": true,
                "canViewDisplays": true,
                "canReceiveFiles": true
            }'::JSON;
        WHEN 'OPERATIONS_MGR' THEN
            permissions := '{
                "canViewHome": false,
                "canManageUsers": false,
                "canPublishFiles": true,
                "canViewFiles": true,
                "canDeleteFiles": false,
                "canManageDisplays": false,
                "canViewDisplays": true,
                "canReceiveFiles": true
            }'::JSON;
        WHEN 'DEPT_MANAGER' THEN
            permissions := '{
                "canViewHome": false,
                "canManageUsers": false,
                "canPublishFiles": false,
                "canViewFiles": true,
                "canDeleteFiles": false,
                "canManageDisplays": false,
                "canViewDisplays": false,
                "canReceiveFiles": true
            }'::JSON;
        WHEN 'DISPLAY_OP' THEN
            permissions := '{
                "canViewHome": false,
                "canManageUsers": false,
                "canPublishFiles": false,
                "canViewFiles": true,
                "canDeleteFiles": false,
                "canManageDisplays": true,
                "canViewDisplays": true,
                "canReceiveFiles": false
            }'::JSON;
        ELSE
            permissions := '{
                "canViewHome": false,
                "canManageUsers": false,
                "canPublishFiles": false,
                "canViewFiles": false,
                "canDeleteFiles": false,
                "canManageDisplays": false,
                "canViewDisplays": false,
                "canReceiveFiles": false
            }'::JSON;
    END CASE;
    
    RETURN permissions;
END;
$$ LANGUAGE plpgsql;

-- إنشاء function للبحث في الملفات
CREATE OR REPLACE FUNCTION search_files(
    search_term TEXT DEFAULT NULL,
    publisher_name TEXT DEFAULT NULL,
    department_name TEXT DEFAULT NULL,
    file_type_filter TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    file_name VARCHAR,
    original_name VARCHAR,
    file_type VARCHAR,
    file_size INTEGER,
    publisher_name TEXT,
    publisher_department TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.id,
        f.file_name,
        f.original_name,
        f.file_type,
        f.file_size,
        u.full_name as publisher_name,
        u.department as publisher_department,
        f.created_at
    FROM files f
    JOIN users u ON f.publisher_id = u.id
    WHERE f.is_deleted = false
        AND (search_term IS NULL OR 
             f.file_name ILIKE '%' || search_term || '%' OR 
             f.original_name ILIKE '%' || search_term || '%')
        AND (publisher_name IS NULL OR u.full_name ILIKE '%' || publisher_name || '%')
        AND (department_name IS NULL OR u.department ILIKE '%' || department_name || '%')
        AND (file_type_filter IS NULL OR f.file_type = file_type_filter)
    ORDER BY f.created_at DESC;
END;
$$ LANGUAGE plpgsql;
