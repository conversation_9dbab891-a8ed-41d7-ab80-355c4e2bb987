# GitHub Pages Configuration for TV Office Management System

# Site settings
title: "نظام إدارة التلفزيونات المكتبية"
description: "نظام متكامل لإدارة ونشر المحتوى على شاشات العرض في المكاتب والمؤسسات"
baseurl: "/tv-office"
url: "https://skayhr.github.io"

# Build settings
markdown: kramdown
highlighter: rouge
theme: minima

# Exclude files from processing
exclude:
  - README.md
  - Gemfile
  - Gemfile.lock
  - node_modules
  - vendor
  - .sass-cache
  - .jekyll-cache
  - .jekyll-metadata

# Include files
include:
  - .nojekyll

# Plugins
plugins:
  - jekyll-feed
  - jekyll-sitemap
  - jekyll-seo-tag

# SEO settings
lang: ar
author: "TV Office Team"
twitter:
  username: skayhr
  card: summary_large_image

# Social links
github_username: skayhr

# Google Analytics (optional)
# google_analytics: UA-XXXXXXXX-X

# Custom variables
custom:
  app_name: "TV Office"
  app_version: "1.0.0"
  demo_url: "/tv-office/demo.html"
