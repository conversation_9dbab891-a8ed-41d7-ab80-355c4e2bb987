<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة التلفزيونات المكتبية - عرض تفاعلي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 1200px;
            width: 100%;
            text-align: center;
        }

        .demo-header {
            margin-bottom: 40px;
        }

        .demo-title {
            font-size: 32px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 16px;
        }

        .demo-subtitle {
            font-size: 18px;
            color: #718096;
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 16px;
            padding: 30px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
        }

        .feature-title {
            font-size: 20px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 12px;
        }

        .feature-description {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .feature-status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            background: #48bb78;
            color: white;
        }

        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .demo-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .demo-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .demo-btn.secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }

        .demo-btn.secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #718096;
            font-size: 14px;
        }

        .tech-stack {
            margin-top: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 16px;
        }

        .tech-title {
            font-size: 20px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
        }

        .tech-tag {
            background: white;
            color: #4a5568;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">🖥️ نظام إدارة التلفزيونات المكتبية</h1>
            <p class="demo-subtitle">نظام متقدم وشامل لإدارة وعرض المحتوى على شاشات التلفزيون في المكاتب والمؤسسات</p>
        </div>

        <div class="stats-container">
            <div class="stat-item">
                <div class="stat-number">4</div>
                <div class="stat-label">خصائص رئيسية</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">تفاعلي</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5+</div>
                <div class="stat-label">أنواع ملفات</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">∞</div>
                <div class="stat-label">شاشات</div>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3 class="feature-title">البحث المتقدم</h3>
                <p class="feature-description">بحث ذكي في الملفات مع فلاتر متقدمة حسب النوع والقسم والتاريخ</p>
                <span class="feature-status">✅ مفعل</span>
            </div>

            <div class="feature-card">
                <div class="feature-icon">☁️</div>
                <h3 class="feature-title">النشر السحابي</h3>
                <p class="feature-description">رفع ونشر الملفات مع معاينة فورية وجدولة النشر</p>
                <span class="feature-status">✅ مفعل</span>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🗑️</div>
                <h3 class="feature-title">إدارة المحذوفات</h3>
                <p class="feature-description">نظام سلة المهملات مع إمكانية الاستعادة لمدة 30 يوم</p>
                <span class="feature-status">✅ مفعل</span>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📺</div>
                <h3 class="feature-title">إدارة الشاشات</h3>
                <p class="feature-description">ربط وإدارة شاشات التلفزيون عبر WiFi أو كابل</p>
                <span class="feature-status">✅ مفعل</span>
            </div>
        </div>

        <div class="demo-buttons">
            <a href="demo.html" class="demo-btn primary">🚀 تجربة النظام الآن</a>
            <button class="demo-btn secondary" onclick="showTechDetails()">📋 التفاصيل التقنية</button>
            <button class="demo-btn secondary" onclick="showFeatures()">⭐ المميزات الكاملة</button>
        </div>

        <div class="tech-stack">
            <h3 class="tech-title">التقنيات المستخدمة</h3>
            <div class="tech-tags">
                <span class="tech-tag">HTML5</span>
                <span class="tech-tag">CSS3</span>
                <span class="tech-tag">JavaScript ES6+</span>
                <span class="tech-tag">LocalStorage API</span>
                <span class="tech-tag">File API</span>
                <span class="tech-tag">Responsive Design</span>
                <span class="tech-tag">Progressive Web App</span>
            </div>
        </div>
    </div>

    <script>
        function showTechDetails() {
            alert(`🔧 التفاصيل التقنية:

📱 واجهة مستخدم متجاوبة
💾 تخزين محلي للبيانات
🔄 تحديث فوري للمحتوى
📁 دعم أنواع ملفات متعددة
🌐 ربط شاشات عبر WiFi/كابل
🔍 بحث متقدم مع فلاتر
⏰ جدولة النشر
🗑️ نظام سلة المهملات
⚙️ إعدادات شاملة
🔐 نظام المستخدمين والصلاحيات`);
        }

        function showFeatures() {
            alert(`⭐ المميزات الكاملة:

🎯 إدارة شاملة للمحتوى
📊 إحصائيات تفاعلية
🎨 تصميم عصري وأنيق
🔄 تحديث فوري للبيانات
📱 متوافق مع جميع الأجهزة
🌙 وضع ليلي/نهاري
🔔 نظام إشعارات
📈 تقارير مفصلة
🔒 أمان عالي
🌍 دعم متعدد اللغات`);
        }

        // تأثيرات تفاعلية
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', function() {
                const title = this.querySelector('.feature-title').textContent;
                const description = this.querySelector('.feature-description').textContent;
                alert(`${title}\n\n${description}\n\nهذه الخاصية مفعلة ومتاحة للاستخدام!`);
            });
        });

        // إضافة تأثيرات الحركة
        window.addEventListener('load', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
