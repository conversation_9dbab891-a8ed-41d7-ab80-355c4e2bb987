<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شاشة العرض التلفزيونية</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Arial', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            height: 100vh; 
            display: flex; 
            flex-direction: column; 
            justify-content: center; 
            align-items: center; 
            text-align: center;
        }
        
        .container { 
            max-width: 800px; 
            padding: 40px; 
            background: rgba(255, 255, 255, 0.1); 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo { 
            font-size: 48px; 
            margin-bottom: 20px; 
        }
        
        .title { 
            font-size: 36px; 
            font-weight: bold; 
            margin-bottom: 30px; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .security-code { 
            font-size: 72px; 
            font-weight: 900; 
            background: white; 
            color: #374151; 
            padding: 30px 50px; 
            border-radius: 15px; 
            margin: 30px 0; 
            letter-spacing: 8px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            font-family: 'Courier New', monospace;
        }
        
        .instructions { 
            font-size: 24px; 
            margin-bottom: 20px; 
            line-height: 1.6;
        }
        
        .status { 
            font-size: 20px; 
            padding: 15px 30px; 
            border-radius: 10px; 
            margin-top: 20px; 
            font-weight: bold;
        }
        
        .status.waiting { 
            background: rgba(59, 130, 246, 0.3); 
            border: 2px solid #3b82f6; 
        }
        
        .status.connected { 
            background: rgba(16, 185, 129, 0.3); 
            border: 2px solid #10b981; 
        }
        
        .ip-info { 
            font-size: 18px; 
            margin-top: 20px; 
            opacity: 0.8;
        }
        
        .content-area { 
            display: none; 
            position: fixed; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 100%; 
            background: black; 
            z-index: 1000;
        }
        
        .file-type-header { 
            background: transparent; 
            color: black; 
            padding: 20px; 
            text-align: center; 
            position: absolute; 
            top: 0; 
            left: 50%; 
            transform: translateX(-50%); 
            z-index: 10;
        }
        
        .file-type-text { 
            font-size: 36px; 
            font-weight: 900; 
            margin: 0; 
            color: black;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📺</div>
        <div class="title">شاشة العرض التلفزيونية</div>
        
        <div class="instructions">
            أدخل رمز الأمان التالي في البرنامج لربط هذه الشاشة:
        </div>
        
        <div class="security-code" id="securityCode">------</div>
        
        <div class="status waiting" id="status">
            🔄 في انتظار الاتصال...
        </div>
        
        <div class="ip-info" id="ipInfo">
            جاري تحديد عنوان IP...
        </div>
        
        <div class="instructions" style="font-size: 18px; margin-top: 30px;">
            📱 في البرنامج: شاشات العرض ← إضافة شاشة ← أدخل IP + رمز الأمان
        </div>
    </div>

    <!-- Content Display Area -->
    <div class="content-area" id="contentArea">
        <!-- Content will be displayed here -->
    </div>

    <script>
        let socket = null;
        let securityCode = '';
        let isConnected = false;

        // Generate random 6-digit security code
        function generateSecurityCode() {
            return Math.floor(100000 + Math.random() * 900000).toString();
        }

        // Get local IP address (approximate)
        function getLocalIP() {
            return new Promise((resolve) => {
                const pc = new RTCPeerConnection({
                    iceServers: []
                });
                
                pc.createDataChannel('');
                pc.createOffer().then(offer => pc.setLocalDescription(offer));
                
                pc.onicecandidate = (ice) => {
                    if (!ice || !ice.candidate || !ice.candidate.candidate) return;
                    const myIP = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/.exec(ice.candidate.candidate)[1];
                    pc.onicecandidate = () => {};
                    resolve(myIP);
                };
            });
        }

        // Initialize WebSocket server (simplified for demo)
        function initializeWebSocket() {
            console.log('تم تشغيل نظام العرض التلفزيوني');

            // For demo purposes, we'll use localStorage to simulate connection
            // In real implementation, you would set up a proper WebSocket server

            // Check for incoming connections every second
            setInterval(checkForConnections, 1000);
        }

        // Check for connection attempts via localStorage (demo simulation)
        function checkForConnections() {
            try {
                const connectionAttempt = localStorage.getItem('tv_connection_attempt');
                if (connectionAttempt) {
                    const data = JSON.parse(connectionAttempt);
                    if (data.timestamp > Date.now() - 5000) { // Within last 5 seconds
                        handleConnectionAttempt(data);
                        localStorage.removeItem('tv_connection_attempt');
                    }
                }

                const displayContent = localStorage.getItem('tv_display_content');
                if (displayContent) {
                    const data = JSON.parse(displayContent);
                    if (data.timestamp > Date.now() - 2000) { // Within last 2 seconds
                        displayContent(data);
                        localStorage.removeItem('tv_display_content');
                    }
                }
            } catch (e) {
                // Ignore errors
            }
        }

        // Handle connection attempts
        function handleConnectionAttempt(data) {
            if (data.token === securityCode && data.ip) {
                isConnected = true;
                document.getElementById('status').innerHTML = '✅ متصل بنجاح!';
                document.getElementById('status').className = 'status connected';

                // Store connection success
                localStorage.setItem('tv_connection_result', JSON.stringify({
                    success: true,
                    message: 'تم التوصيل بنجاح!',
                    timestamp: Date.now()
                }));

                // Hide setup screen after 3 seconds
                setTimeout(() => {
                    document.querySelector('.container').style.display = 'none';
                }, 3000);

            } else {
                // Store connection failure
                localStorage.setItem('tv_connection_result', JSON.stringify({
                    success: false,
                    message: 'رمز الأمان غير صحيح',
                    timestamp: Date.now()
                }));
            }
        }

        // Handle incoming messages
        function handleMessage(message) {
            switch (message.type) {
                case 'auth':
                    handleAuth(message);
                    break;
                case 'display_content':
                    displayContent(message);
                    break;
                case 'ping':
                    socket.send(JSON.stringify({ type: 'pong' }));
                    break;
            }
        }

        // Handle authentication
        function handleAuth(message) {
            if (message.token === securityCode) {
                isConnected = true;
                document.getElementById('status').innerHTML = '✅ متصل بنجاح!';
                document.getElementById('status').className = 'status connected';
                
                // Send success response
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify({
                        type: 'auth_success',
                        message: 'Authentication successful'
                    }));
                }
                
                // Hide setup screen after 3 seconds
                setTimeout(() => {
                    document.querySelector('.container').style.display = 'none';
                }, 3000);
                
            } else {
                // Send failure response
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify({
                        type: 'auth_failed',
                        message: 'Invalid security code'
                    }));
                }
            }
        }

        // Display content on TV
        function displayContent(message) {
            const contentArea = document.getElementById('contentArea');
            const { fileType, content, contentType } = message;
            
            let displayHTML = '';
            
            if (contentType === 'image') {
                displayHTML = `
                    <div style="background: white; padding: 0; border-radius: 12px; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div class="file-type-header">
                            <h1 class="file-type-text">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: black; display: flex; align-items: center; justify-content: center; border-radius: 0 0 12px 12px;">
                            <img src="${content}" style="max-width: 100%; max-height: 100%; object-fit: contain;">
                        </div>
                    </div>
                `;
            } else if (contentType === 'pdf') {
                displayHTML = `
                    <div style="background: white; padding: 0; border-radius: 12px; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div class="file-type-header">
                            <h1 class="file-type-text">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: white; display: flex; align-items: center; justify-content: center; border-radius: 0 0 12px 12px;">
                            <embed src="${content}" type="application/pdf" width="100%" height="100%" style="border: none;">
                        </div>
                    </div>
                `;
            } else {
                displayHTML = `
                    <div style="background: white; padding: 0; border-radius: 12px; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div class="file-type-header">
                            <h1 class="file-type-text">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; border-radius: 0 0 12px 12px;">
                            <svg width="200" height="200" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="1" style="margin: 0 auto; display: block; opacity: 0.8;">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                            </svg>
                        </div>
                    </div>
                `;
            }
            
            contentArea.innerHTML = displayHTML;
            contentArea.style.display = 'block';
        }

        // Initialize everything
        function init() {
            // Generate security code
            securityCode = generateSecurityCode();
            document.getElementById('securityCode').textContent = securityCode;
            
            // Get and display IP
            getLocalIP().then(ip => {
                document.getElementById('ipInfo').textContent = `عنوان IP: ${ip}:8080`;
            }).catch(() => {
                document.getElementById('ipInfo').textContent = 'عنوان IP: 192.168.1.xxx:8080';
            });
            
            // Initialize WebSocket
            initializeWebSocket();
            
            console.log('TV Display initialized with security code:', securityCode);
        }

        // Start when page loads
        window.addEventListener('load', init);
        
        // Regenerate code every 10 minutes for security
        setInterval(() => {
            if (!isConnected) {
                securityCode = generateSecurityCode();
                document.getElementById('securityCode').textContent = securityCode;
                console.log('Security code regenerated:', securityCode);
            }
        }, 600000); // 10 minutes
    </script>
</body>
</html>
