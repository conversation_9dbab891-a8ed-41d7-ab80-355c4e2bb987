// إعدادات API للنسخة السحابية
export const API_CONFIG = {
  // رابط Backend السحابي (سيتم تحديثه بعد النشر على Vercel)
  BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://your-backend-url.vercel.app/api'  // سيتم تحديثه
    : 'http://localhost:5000/api',
  
  // إعدادات Socket.io
  SOCKET_URL: process.env.NODE_ENV === 'production'
    ? 'https://your-backend-url.vercel.app'  // سيتم تحديثه
    : 'http://localhost:5000',
  
  // إعدادات الطلبات
  TIMEOUT: 10000,
  
  // إعدادات رفع الملفات
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: ['application/pdf'],
  
  // إعدادات المصادقة
  TOKEN_KEY: 'tv_office_token',
  USER_KEY: 'tv_office_user',
  
  // إعدادات Socket.io
  SOCKET_OPTIONS: {
    transports: ['websocket', 'polling'],
    timeout: 20000,
    forceNew: true,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionAttempts: 5,
    maxReconnectionAttempts: 5
  }
};

// أنواع المستخدمين
export const USER_TYPES = {
  MAIN_ADMIN: 'MAIN_ADMIN',
  OPERATIONS_MGR: 'OPERATIONS_MGR', 
  DEPT_MANAGER: 'DEPT_MANAGER',
  DISPLAY_OP: 'DISPLAY_OP'
} as const;

// تسميات أنواع المستخدمين بالعربية
export const USER_TYPE_LABELS = {
  [USER_TYPES.MAIN_ADMIN]: 'المدير الرئيسي',
  [USER_TYPES.OPERATIONS_MGR]: 'مدير التشغيل',
  [USER_TYPES.DEPT_MANAGER]: 'مدير القسم',
  [USER_TYPES.DISPLAY_OP]: 'مسؤول الاستعلامات'
} as const;

// حالات الطلبات
export const REQUEST_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
} as const;

// رسائل الأخطاء الافتراضية
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'خطأ في الاتصال بالخادم',
  UNAUTHORIZED: 'غير مصرح لك بالوصول',
  FORBIDDEN: 'ليس لديك صلاحية لهذا الإجراء',
  NOT_FOUND: 'المورد المطلوب غير موجود',
  SERVER_ERROR: 'خطأ في الخادم، يرجى المحاولة لاحقاً',
  VALIDATION_ERROR: 'بيانات غير صحيحة',
  FILE_TOO_LARGE: 'حجم الملف كبير جداً',
  INVALID_FILE_TYPE: 'نوع الملف غير مدعوم'
} as const;

// إعدادات التنبيهات
export const TOAST_CONFIG = {
  duration: 4000,
  position: 'top-center' as const,
  style: {
    background: '#363636',
    color: '#fff',
    fontFamily: 'Cairo, sans-serif',
    direction: 'rtl' as const
  }
};

// إعدادات الصفحات
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100]
};

// إعدادات البحث
export const SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 2
};

// أنماط الملفات المدعومة
export const FILE_TYPES = {
  PDF: 'application/pdf'
} as const;

// أحجام الملفات
export const FILE_SIZES = {
  KB: 1024,
  MB: 1024 * 1024,
  GB: 1024 * 1024 * 1024
} as const;

// دالة لتنسيق حجم الملف
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// دالة لتنسيق التاريخ
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// دالة للتحقق من نوع الملف
export const isValidFileType = (file: File): boolean => {
  return API_CONFIG.ALLOWED_FILE_TYPES.includes(file.type);
};

// دالة للتحقق من حجم الملف
export const isValidFileSize = (file: File): boolean => {
  return file.size <= API_CONFIG.MAX_FILE_SIZE;
};

// دالة لإنشاء رابط التحميل
export const createDownloadUrl = (filePath: string): string => {
  if (filePath.startsWith('http')) {
    return filePath;
  }
  return `${API_CONFIG.BASE_URL.replace('/api', '')}${filePath}`;
};

// دالة للحصول على رمز المصادقة
export const getAuthToken = (): string | null => {
  return localStorage.getItem(API_CONFIG.TOKEN_KEY);
};

// دالة لحفظ رمز المصادقة
export const setAuthToken = (token: string): void => {
  localStorage.setItem(API_CONFIG.TOKEN_KEY, token);
};

// دالة لحذف رمز المصادقة
export const removeAuthToken = (): void => {
  localStorage.removeItem(API_CONFIG.TOKEN_KEY);
  localStorage.removeItem(API_CONFIG.USER_KEY);
};

// دالة للحصول على بيانات المستخدم
export const getCurrentUser = () => {
  const userStr = localStorage.getItem(API_CONFIG.USER_KEY);
  if (userStr) {
    try {
      return JSON.parse(userStr);
    } catch (error) {
      console.error('Error parsing user from localStorage:', error);
      removeAuthToken();
    }
  }
  return null;
};

// دالة لحفظ بيانات المستخدم
export const setCurrentUser = (user: any): void => {
  localStorage.setItem(API_CONFIG.USER_KEY, JSON.stringify(user));
};
