<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>نظام إدارة التلفزيونات المكتبية - عرض توضيحي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- EmailJS for sending real emails -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>

    <!-- Firebase SDK for real-time data sharing -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-storage-compat.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9fafb;
            direction: rtl;
            overflow-x: hidden;
        }

        /* تحسينات سطح المكتب */
        @media (min-width: 1024px) {
            .container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 0 20px;
            }

            .home-cards {
                grid-template-columns: repeat(4, 1fr) !important;
                gap: 30px !important;
                padding: 40px 20px !important;
            }

            .home-card {
                min-height: 200px;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .home-card:hover {
                transform: translateY(-8px);
                box-shadow: 0 12px 30px rgba(0,0,0,0.15);
            }

            .modal-content {
                max-width: 1200px !important;
                margin: 0 auto;
            }
        }

        /* تحسينات التابلت */
        @media (min-width: 768px) and (max-width: 1023px) {
            .home-cards {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 25px !important;
                padding: 30px 20px !important;
            }

            .home-card {
                min-height: 180px;
                padding: 25px !important;
            }

            .card-icon {
                width: 70px !important;
                height: 70px !important;
            }

            .icon-lg {
                width: 35px !important;
                height: 35px !important;
            }

            .card-title {
                font-size: 20px !important;
            }

            .modal-content {
                max-width: 90% !important;
                margin: 20px;
            }

            /* تحسين الأزرار للتابلت */
            button {
                min-height: 44px;
                font-size: 16px;
            }

            input, select, textarea {
                min-height: 44px;
                font-size: 16px;
            }
        }

        /* تحسينات الهاتف */
        @media (max-width: 767px) {
            /* الهيدر */
            .header {
                padding: 10px 15px !important;
                min-height: 60px;
            }

            .header-container {
                flex-wrap: wrap;
                gap: 10px;
            }

            .header-left {
                flex: 1;
                min-width: 200px;
            }

            .app-name {
                font-size: 16px !important;
            }

            .home-section span {
                font-size: 14px !important;
            }

            .user-info {
                padding: 6px 10px !important;
                font-size: 14px !important;
            }

            .user-info svg {
                width: 16px !important;
                height: 16px !important;
            }

            .logout-btn {
                padding: 8px 12px !important;
                font-size: 14px !important;
            }

            .fullscreen-btn {
                padding: 8px !important;
            }

            /* الكروت */
            .home-cards {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
                padding: 15px 10px !important;
            }

            .header {
                padding: 15px 20px !important;
            }

            .home-section span {
                font-size: 14px !important;
            }

            .modal-content {
                max-width: 95% !important;
                margin: 10px;
            }

            /* تحسينات الكروت للموبايل */
            .home-card {
                min-height: 100px !important;
                padding: 15px !important;
                border-radius: 12px !important;
            }

            .card-icon {
                width: 40px !important;
                height: 40px !important;
                margin-bottom: 10px !important;
            }

            .card-icon svg {
                width: 20px !important;
                height: 20px !important;
            }

            .card-title {
                font-size: 16px !important;
                margin-bottom: 5px !important;
            }

            .card-description {
                font-size: 12px !important;
                line-height: 1.4 !important;
            }

            /* تحسينات النوافذ للموبايل */
            .modal-content {
                padding: 15px !important;
                border-radius: 12px !important;
            }

            /* تحسينات الأزرار للموبايل */
            button {
                min-height: 44px !important;
                font-size: 14px !important;
                padding: 10px 15px !important;
            }
        }

        /* Header Styles */
        .header {
            background-color: #41B1C7;
            border-bottom: 2px solid #369fb3;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
            padding: 0 16px;
            max-width: 100%;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        .three-dots {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .three-dots:hover {
            background-color: #d1d5db;
        }

        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        .logo:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
        }

        .app-name {
            font-size: 18px;
            font-weight: bold;
            color: white;
        }

        .home-section {
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
        }

        .notification-bell {
            position: relative;
            background: none;
            border: none;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .notification-bell:hover {
            background-color: #d1d5db;
        }

        .notification-bell.has-notification {
            color: #ef4444;
            animation: bellRing 2s infinite;
        }

        .notification-dot {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 8px;
            height: 8px;
            background-color: #ef4444;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .user-info svg {
            color: white;
        }

        .logout-btn {
            background-color: #41B1C7;
            color: white;
            border: 2px solid transparent;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(65, 177, 199, 0.4);
        }

        .logout-btn:hover {
            background-color: #41B1C7;
            border-color: #d1d5db;
            box-shadow: 0 6px 20px rgba(65, 177, 199, 0.6);
            transform: translateY(-2px);
        }

        .fullscreen-btn {
            background-color: #6366f1;
            color: white;
            border: 2px solid transparent;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
        }

        .fullscreen-btn:hover {
            background-color: #4f46e5;
            border-color: #d1d5db;
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.6);
            transform: translateY(-2px);
        }

        /* وضع العرض الكامل */
        .fullscreen-mode {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
            background: #f9fafb !important;
        }

        .fullscreen-mode .header {
            padding: 10px 20px !important;
        }

        .fullscreen-mode .home-cards {
            grid-template-columns: repeat(4, 1fr) !important;
            gap: 40px !important;
            padding: 40px !important;
        }

        .fullscreen-mode .home-card {
            min-height: 250px !important;
            font-size: 18px !important;
        }

        .fullscreen-mode .card-icon {
            width: 100px !important;
            height: 100px !important;
        }

        .fullscreen-mode .icon-lg {
            width: 50px !important;
            height: 50px !important;
        }

        .fullscreen-mode .card-title {
            font-size: 24px !important;
        }

        .fullscreen-mode .card-description {
            font-size: 16px !important;
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 16px 32px 16px;
        }

        .welcome-section {
            margin-bottom: 32px;
        }

        .welcome-title {
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 8px;
        }

        .welcome-subtitle {
            color: #6b7280;
        }

        /* Home Cards Grid */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .home-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 3px solid #e5e7eb;
            padding: 32px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .home-card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            transform: translateY(-8px) scale(1.02);
        }

        /* Default border colors (hidden) */
        .home-card.blue-border { border-color: #e5e7eb; }
        .home-card.gray-border { border-color: #e5e7eb; }
        .home-card.green-border { border-color: #e5e7eb; }
        .home-card.purple-border { border-color: #e5e7eb; }
        .home-card.red-border { border-color: #e5e7eb; }
        .home-card.indigo-border { border-color: #e5e7eb; }

        /* Hover border colors (lighter shades) */
        .home-card.blue-border:hover {
            border-color: #93c5fd;
            background: linear-gradient(135deg, #ffffff, #eff6ff);
        }
        .home-card.gray-border:hover {
            border-color: #9ca3af;
            background: linear-gradient(135deg, #ffffff, #f9fafb);
        }
        .home-card.green-border:hover {
            border-color: #6ee7b7;
            background: linear-gradient(135deg, #ffffff, #f0fdf4);
        }
        .home-card.purple-border:hover {
            border-color: #c4b5fd;
            background: linear-gradient(135deg, #ffffff, #faf5ff);
        }
        .home-card.red-border:hover {
            border-color: #fca5a5;
            background: linear-gradient(135deg, #ffffff, #fef2f2);
        }
        .home-card.indigo-border:hover {
            border-color: #a5b4fc;
            background: linear-gradient(135deg, #ffffff, #eef2ff);
        }

        .card-icon {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            transition: all 0.3s ease;
        }

        .card-icon.blue { background-color: #3b82f6; }
        .card-icon.gray { background-color: #6b7280; }
        .card-icon.green { background-color: #10b981; }
        .card-icon.purple { background-color: #8b5cf6; }
        .card-icon.red { background-color: #ef4444; }
        .card-icon.indigo { background-color: #6366f1; }

        /* Icon hover effects */
        .home-card:hover .card-icon.blue {
            background-color: #2563eb;
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        .home-card:hover .card-icon.gray {
            background-color: #4b5563;
            transform: scale(1.1) rotate(-5deg);
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
        }
        .home-card:hover .card-icon.green {
            background-color: #059669;
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
        .home-card:hover .card-icon.purple {
            background-color: #7c3aed;
            transform: scale(1.1) rotate(-5deg);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
        }
        .home-card:hover .card-icon.red {
            background-color: #dc2626;
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }
        .home-card:hover .card-icon.indigo {
            background-color: #4f46e5;
            transform: scale(1.1) rotate(-5deg);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
        }

        .card-description {
            color: #6b7280;
            font-size: 14px;
        }

        /* Icons */
        .icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        .icon-lg {
            width: 32px;
            height: 32px;
            fill: currentColor;
        }

        /* Animations */
        @keyframes bellRing {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Dropdown Menu */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #41B1C7;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1;
            top: 100%;
            right: 0;
            margin-top: 8px;
            border: 1px solid #369fb3;
        }

        .dropdown:hover .dropdown-content,
        .dropdown-content:hover {
            display: block;
        }

        .dropdown-item {
            color: #4b5563;
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;
            border: none;
            background: none;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .dropdown-item svg {
            color: #4b5563;
            transition: color 0.3s ease;
        }

        .dropdown-item:hover svg {
            color: white;
        }

        .dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }

        .dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }

        /* Modal Overlay */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 20px;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 0;
            max-width: 1200px;
            width: 90vw;
            max-height: 95vh;
            text-align: center;
            margin: auto;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* تحسين شريط التمرير */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* تلوين خيارات القائمة المنسدلة */
        #centralUserSelect option:hover {
            background-color: #eff6ff !important;
            color: #1e40af !important;
        }

        #centralUserSelect:focus option:checked {
            background-color: #dbeafe !important;
            color: #1e40af !important;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #111827;
        }

        .modal-text {
            color: #6b7280;
            margin-bottom: 24px;
        }

        .modal-btn {
            background-color: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .modal-btn:hover {
            background-color: #4b5563;
        }





        /* شاشة تسجيل الدخول */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 20000;
            overflow-y: auto;
            padding: 20px 0;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            padding: 20px;
            max-width: 400px;
            width: 95%;
            position: relative;
            z-index: 2;
            margin: 10px auto;
            min-height: auto;
            max-height: 95vh;
            overflow-y: auto;
        }

        .login-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .logo-container {
            margin-bottom: 8px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            margin: 0 auto 8px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .logo-icon svg {
            width: 25px;
            height: 25px;
        }

        .system-title {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 3px;
            line-height: 1.2;
        }

        .system-subtitle {
            font-size: 12px;
            color: #718096;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .login-form-container {
            background: white;
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-top: 5px;
        }

        .form-title {
            font-size: 16px;
            font-weight: bold;
            color: #2d3748;
            text-align: center;
            margin-bottom: 10px;
        }

        .input-group {
            margin-bottom: 6px;
        }

        .input-group label {
            display: block;
            font-size: 12px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 4px;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            right: 8px;
            width: 16px;
            height: 16px;
            color: #9ca3af;
            z-index: 1;
        }

        .input-wrapper input,
        .input-wrapper select {
            width: 100%;
            padding: 6px 30px 6px 6px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 12px;
            transition: all 0.3s ease;
            background: #f9fafb;
            height: 32px;
        }

        .input-wrapper input:focus,
        .input-wrapper select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .toggle-password {
            position: absolute;
            left: 12px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            color: #9ca3af;
            transition: color 0.3s ease;
        }

        .toggle-password:hover {
            color: #667eea;
        }

        .toggle-password svg {
            width: 18px;
            height: 18px;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            margin-bottom: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .login-btn svg {
            width: 20px;
            height: 20px;
        }

        .form-footer {
            text-align: center;
        }

        .forgot-password-btn {
            background: none;
            border: none;
            color: #667eea;
            font-size: 14px;
            cursor: pointer;
            text-decoration: underline;
            transition: color 0.3s ease;
        }

        .forgot-password-btn:hover, .register-btn:hover {
            color: #4f46e5;
        }

        .register-btn {
            background: none;
            border: none;
            color: #667eea;
            font-size: 14px;
            cursor: pointer;
            text-decoration: underline;
            transition: color 0.3s ease;
        }

        .separator {
            color: #cbd5e1;
            margin: 0 10px;
        }

        /* Checkbox styling */
        .checkbox-container {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 11px;
            color: #4a5568;
            user-select: none;
        }

        .checkbox-container input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .checkmark {
            height: 14px;
            width: 14px;
            background-color: #fff;
            border: 1px solid #e2e8f0;
            border-radius: 3px;
            margin-right: 6px;
            position: relative;
            transition: all 0.3s ease;
        }

        .checkbox-container:hover input ~ .checkmark {
            border-color: #667eea;
        }

        .checkbox-container input:checked ~ .checkmark {
            background-color: #667eea;
            border-color: #667eea;
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        .checkbox-container input:checked ~ .checkmark:after {
            display: block;
        }

        .checkbox-container .checkmark:after {
            left: 5px;
            top: 2px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        /* Register form styling */
        .register-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .register-submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }

        .register-submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .register-submit-btn svg {
            width: 20px;
            height: 20px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        /* Header actions styling */
        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .profile-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .profile-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
        }

        .profile-btn .icon {
            width: 16px;
            height: 16px;
        }

        /* Profile modal styling */
        .profile-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .profile-avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            border: 4px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .profile-avatar:hover {
            border-color: #667eea;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .change-avatar-btn {
            background: #f8f9fa;
            color: #4a5568;
            border: 2px solid #e2e8f0;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .change-avatar-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .change-avatar-btn svg {
            width: 16px;
            height: 16px;
        }

        .profile-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .profile-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .edit-profile-btn, .change-password-btn, .save-profile-btn {
            background: #f8f9fa;
            color: #4a5568;
            border: 2px solid #e2e8f0;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .edit-profile-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .change-password-btn:hover {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }

        .save-profile-btn {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }

        .save-profile-btn:hover {
            background: #059669;
            border-color: #059669;
        }

        .edit-profile-btn svg, .change-password-btn svg, .save-profile-btn svg {
            width: 16px;
            height: 16px;
        }

        /* الخلفية المتحركة */
        .animated-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape-3 {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape-4 {
            width: 100px;
            height: 100px;
            top: 10%;
            right: 30%;
            animation-delay: 1s;
        }

        .shape-5 {
            width: 140px;
            height: 140px;
            bottom: 30%;
            right: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        /* تحسينات للموبايل */
        @media (max-width: 768px) {
            .login-screen {
                padding: 10px 0;
            }

            .login-container {
                padding: 20px;
                margin: 10px;
                width: 95%;
                max-height: 90vh;
                overflow-y: auto;
            }

            .system-title {
                font-size: 20px;
            }

            .login-form-container {
                padding: 20px;
            }
        }

        /* تحسينات للشاشات الصغيرة جداً */
        @media (max-width: 480px) {
            .login-container {
                padding: 12px !important;
                margin: 3px auto !important;
                width: 98% !important;
                max-height: 98vh !important;
            }

            .login-header {
                margin-bottom: 6px !important;
            }

            .logo-container {
                margin-bottom: 4px !important;
            }

            .logo-icon {
                width: 40px !important;
                height: 40px !important;
                margin: 0 auto 6px !important;
            }

            .logo-icon svg {
                width: 20px !important;
                height: 20px !important;
            }

            .system-title {
                font-size: 16px !important;
                margin-bottom: 2px !important;
            }

            .system-subtitle {
                font-size: 10px !important;
                margin-bottom: 6px !important;
            }

            .login-form-container {
                padding: 8px !important;
                margin-top: 2px !important;
            }

            .input-group {
                margin-bottom: 4px !important;
            }

            .input-group label {
                font-size: 10px !important;
                margin-bottom: 2px !important;
            }

            .input-wrapper input,
            .input-wrapper select {
                padding: 4px 25px 4px 4px !important;
                font-size: 11px !important;
                height: 28px !important;
            }

            .input-icon {
                width: 14px !important;
                height: 14px !important;
                right: 6px !important;
            }

            .login-btn {
                padding: 6px 10px !important;
                font-size: 12px !important;
                margin-bottom: 6px !important;
            }

            .checkbox-container {
                font-size: 9px !important;
            }

            .checkmark {
                height: 12px !important;
                width: 12px !important;
                margin-right: 4px !important;
            }
            /* شاشة تسجيل الدخول */
            .login-screen {
                padding: 5px 0;
            }

            .login-container {
                padding: 15px;
                margin: 5px;
                width: 98%;
                max-height: 95vh;
                border-radius: 16px;
            }

            .logo-icon {
                width: 45px !important;
                height: 45px !important;
            }

            .logo-icon svg {
                width: 22px !important;
                height: 22px !important;
            }

            .system-title {
                font-size: 18px;
            }

            .system-subtitle {
                font-size: 12px;
                margin-bottom: 10px !important;
            }

            .login-form-container {
                padding: 12px !important;
                margin-top: 8px !important;
            }

            .input-group {
                margin-bottom: 8px !important;
            }

            .input-group label {
                font-size: 12px !important;
                margin-bottom: 4px !important;
            }

            .input-wrapper input,
            .input-wrapper select {
                padding: 8px 35px 8px 8px !important;
                font-size: 14px !important;
            }

            .login-btn {
                padding: 10px 15px !important;
                font-size: 14px !important;
                margin-bottom: 10px !important;
            }

            /* الصفحة الرئيسية */
            .header {
                padding: 8px 10px !important;
                min-height: 50px !important;
            }

            .header-container {
                height: 50px !important;
                padding: 0 8px !important;
            }

            .app-name {
                font-size: 14px !important;
            }

            .home-section {
                display: none !important; /* إخفاء "الرئيسية" في الهواتف الصغيرة */
            }

            .user-info span {
                display: none !important; /* إخفاء اسم المستخدم في الهواتف الصغيرة */
            }

            .logout-btn span {
                display: none !important; /* إظهار أيقونة تسجيل الخروج فقط */
            }

            .home-cards {
                padding: 10px 5px !important;
                gap: 10px !important;
            }

            .home-card {
                min-height: 80px !important;
                padding: 10px !important;
            }

            .card-icon {
                width: 30px !important;
                height: 30px !important;
                margin-bottom: 8px !important;
            }

            .card-icon svg {
                width: 16px !important;
                height: 16px !important;
            }

            .card-title {
                font-size: 14px !important;
                margin-bottom: 3px !important;
            }

            .card-description {
                font-size: 11px !important;
            }
        }

        /* تحسينات للشاشات العالية القصيرة */
        @media (max-height: 600px) {
            .login-screen {
                align-items: flex-start;
                padding: 5px 0;
            }

            .login-container {
                margin: 5px auto;
                max-height: 98vh;
                overflow-y: auto;
                padding: 15px !important;
            }

            .logo-icon {
                width: 40px !important;
                height: 40px !important;
            }

            .logo-icon svg {
                width: 20px !important;
                height: 20px !important;
            }

            .login-header {
                margin-bottom: 8px !important;
            }

            .logo-container {
                margin-bottom: 5px !important;
            }

            .system-title {
                font-size: 16px !important;
                margin-bottom: 2px !important;
            }

            .system-subtitle {
                font-size: 11px !important;
                margin-bottom: 8px !important;
            }

            .login-form-container {
                padding: 10px !important;
                margin-top: 3px !important;
            }

            .input-group {
                margin-bottom: 6px !important;
            }

            .input-group label {
                font-size: 11px !important;
                margin-bottom: 3px !important;
            }

            .input-wrapper input,
            .input-wrapper select {
                padding: 6px 30px 6px 6px !important;
                font-size: 12px !important;
            }

            .login-btn {
                padding: 8px 12px !important;
                font-size: 13px !important;
                margin-bottom: 8px !important;
            }

            .checkbox-group {
                margin: 8px 0 !important;
                font-size: 11px !important;
            }

            .form-links {
                font-size: 11px !important;
                margin-top: 8px !important;
            }
        }

        /* CSS للنوافذ المنبثقة */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 15000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 30px;
            max-width: 700px;
            width: 90%;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header h3 {
            color: #2d3748;
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
            padding: 5px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }
    </style>
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <!-- الشعار والعنوان -->
            <div class="login-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                            <line x1="8" y1="21" x2="16" y2="21"/>
                            <line x1="12" y1="17" x2="12" y2="21"/>
                            <circle cx="12" cy="10" r="3"/>
                        </svg>
                    </div>
                    <h1 class="system-title">نظام إدارة التلفزيونات المكتبية</h1>
                    <p class="system-subtitle">Office TV Management System</p>
                </div>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <div class="login-form-container">
                <form class="login-form" onsubmit="handleLogin(event)">
                    <h2 class="form-title" data-translate="loginTitle">تسجيل الدخول</h2>

                    <div class="input-group">
                        <label for="username">اسم المستخدم</label>
                        <div class="input-wrapper">
                            <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            <input type="text" id="username" name="username" required placeholder="أدخل اسم المستخدم">
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="password">كلمة المرور</label>
                        <div class="input-wrapper">
                            <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                <circle cx="12" cy="16" r="1"/>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                            </svg>
                            <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
                            <button type="button" class="toggle-password" onclick="togglePassword()">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="branchType">نوع الفرع</label>
                        <div class="input-wrapper">
                            <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                <polyline points="9,22 9,12 15,12 15,22"/>
                            </svg>
                            <select id="branchType" name="branchType" required>
                                <option value="">اختر نوع الفرع</option>
                                <option value="A">A - الإدارة العامة</option>
                                <option value="B">B - المبيعات</option>
                                <option value="C">C - الجودة</option>
                                <option value="D">D - العرض</option>
                            </select>
                        </div>
                    </div>

                    <div class="input-group">
                        <label class="checkbox-container">
                            <input type="checkbox" id="rememberMe" name="rememberMe">
                            <span class="checkmark"></span>
                            تذكرني لمدة 30 يوم
                        </label>
                    </div>

                    <button type="submit" class="login-btn">
                        <span>تسجيل الدخول</span>
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                            <polyline points="16,17 21,12 16,7"/>
                            <line x1="21" y1="12" x2="9" y2="12"/>
                        </svg>
                    </button>

                    <div class="form-footer">
                        <button type="button" class="forgot-password-btn" onclick="showPasswordReset()">
                            نسيت كلمة المرور؟
                        </button>
                        <span class="separator">|</span>
                        <button type="button" class="register-btn" onclick="showRegister()">
                            إنشاء حساب جديد
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- خلفية متحركة -->
        <div class="animated-background">
            <div class="floating-shape shape-1"></div>
            <div class="floating-shape shape-2"></div>
            <div class="floating-shape shape-3"></div>
            <div class="floating-shape shape-4"></div>
            <div class="floating-shape shape-5"></div>
        </div>
    </div>

    <!-- نافذة التسجيل الجديد -->
    <div id="registerModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3>إنشاء حساب جديد</h3>
                <button onclick="closeRegister()" class="close-btn">✕</button>
            </div>
            <div class="modal-body">
                <form class="register-form" onsubmit="handleRegister(event)">
                    <div class="form-row">
                        <div class="input-group">
                            <label for="regFirstName">الاسم الأول</label>
                            <input type="text" id="regFirstName" name="firstName" required placeholder="أدخل الاسم الأول">
                        </div>
                        <div class="input-group">
                            <label for="regLastName">الاسم الأخير</label>
                            <input type="text" id="regLastName" name="lastName" required placeholder="أدخل الاسم الأخير">
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="regEmail">البريد الإلكتروني</label>
                        <input type="email" id="regEmail" name="email" required placeholder="أدخل البريد الإلكتروني">
                    </div>

                    <div class="form-row">
                        <div class="input-group">
                            <label for="regPassword">كلمة المرور</label>
                            <input type="password" id="regPassword" name="password" required placeholder="أدخل كلمة المرور">
                        </div>
                        <div class="input-group">
                            <label for="regConfirmPassword">تأكيد كلمة المرور</label>
                            <input type="password" id="regConfirmPassword" name="confirmPassword" required placeholder="أعد إدخال كلمة المرور">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="input-group">
                            <label for="regPosition">المنصب</label>
                            <input type="text" id="regPosition" name="position" required placeholder="مثل: مدير المبيعات">
                        </div>
                        <div class="input-group">
                            <label for="regDepartment">القسم</label>
                            <input type="text" id="regDepartment" name="department" required placeholder="مثل: قسم المبيعات">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="input-group">
                            <label for="regPhone">رقم الهاتف</label>
                            <input type="tel" id="regPhone" name="phone" placeholder="+964 xxx xxxx">
                        </div>
                        <div class="input-group">
                            <label for="regBranchType">نوع الفرع</label>
                            <select id="regBranchType" name="branchType" required>
                                <option value="">اختر نوع الفرع</option>
                                <option value="A">A - الإدارة العامة</option>
                                <option value="B">B - المبيعات</option>
                                <option value="C">C - الجودة</option>
                                <option value="D">D - العرض</option>
                            </select>
                        </div>
                    </div>

                    <div class="input-group">
                        <label class="checkbox-container">
                            <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                            <span class="checkmark"></span>
                            أوافق على <a href="#" onclick="showTerms()">شروط الاستخدام</a> و <a href="#" onclick="showPrivacy()">سياسة الخصوصية</a>
                        </label>
                    </div>

                    <button type="submit" class="register-submit-btn">
                        <span>إنشاء الحساب</span>
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="8.5" cy="7" r="4"/>
                            <line x1="20" y1="8" x2="20" y2="14"/>
                            <line x1="23" y1="11" x2="17" y2="11"/>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة البروفايل الشخصي -->
    <div id="profileModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3>البروفايل الشخصي</h3>
                <button onclick="closeProfile()" class="close-btn">✕</button>
            </div>
            <div class="modal-body">
                <div class="profile-container">
                    <!-- صورة البروفايل -->
                    <div class="profile-avatar-section">
                        <div class="profile-avatar">
                            <img id="profileImage" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik03NSA3NVY2OUE4IDggMCAwIDAgNjcgNjFIMzNBOCA4IDAgMCAwIDI1IDY5Vjc1IiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNDAiIHI9IjEyIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="صورة البروفايل">
                        </div>
                        <button class="change-avatar-btn" onclick="changeAvatar()">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                <circle cx="12" cy="13" r="4"/>
                            </svg>
                            تغيير الصورة
                        </button>
                    </div>

                    <!-- معلومات البروفايل -->
                    <form class="profile-form" onsubmit="updateProfile(event)">
                        <div class="form-row">
                            <div class="input-group">
                                <label for="profileFirstName">الاسم الأول</label>
                                <input type="text" id="profileFirstName" name="firstName" readonly>
                            </div>
                            <div class="input-group">
                                <label for="profileLastName">الاسم الأخير</label>
                                <input type="text" id="profileLastName" name="lastName" readonly>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="profileEmail">البريد الإلكتروني</label>
                            <input type="email" id="profileEmail" name="email" readonly>
                        </div>

                        <div class="form-row">
                            <div class="input-group">
                                <label for="profilePosition">المنصب</label>
                                <input type="text" id="profilePosition" name="position" readonly>
                            </div>
                            <div class="input-group">
                                <label for="profileDepartment">القسم</label>
                                <input type="text" id="profileDepartment" name="department" readonly>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="input-group">
                                <label for="profilePhone">رقم الهاتف</label>
                                <input type="tel" id="profilePhone" name="phone" readonly>
                            </div>
                            <div class="input-group">
                                <label for="profileBranch">نوع الفرع</label>
                                <input type="text" id="profileBranch" name="branch" readonly>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="profileJoinDate">تاريخ الانضمام</label>
                            <input type="text" id="profileJoinDate" name="joinDate" readonly>
                        </div>

                        <div class="profile-actions">
                            <button type="button" class="edit-profile-btn" onclick="toggleEditMode()">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                </svg>
                                تعديل المعلومات
                            </button>

                            <button type="button" class="change-password-btn" onclick="showChangePassword()">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                    <circle cx="12" cy="16" r="1"/>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                                </svg>
                                تغيير كلمة المرور
                            </button>

                            <button type="submit" class="save-profile-btn" style="display: none;">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                                    <polyline points="17,21 17,13 7,13 7,21"/>
                                    <polyline points="7,3 7,8 15,8"/>
                                </svg>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إعادة تعيين كلمة المرور -->
    <div id="passwordResetModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3>إعادة تعيين كلمة المرور</h3>
                <button onclick="closePasswordReset()" class="close-btn">✕</button>
            </div>
            <div class="modal-body">
                <div class="info-message" style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-bottom: 20px; color: #0c4a6e;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <svg style="width: 20px; height: 20px; color: #0ea5e9;" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        <strong>إعادة تعيين كلمة المرور</strong>
                    </div>
                    <p style="margin: 0; font-size: 14px; line-height: 1.5;">
                        سيتم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني المسجل في النظام.
                        <br><strong>ملاحظة:</strong> تأكد من فحص صندوق الرسائل المزعجة إذا لم تجد الإيميل في صندوق الوارد.
                    </p>
                </div>

                <div class="reset-form">
                    <div class="input-group" style="margin-bottom: 20px;">
                        <label for="resetUsername" style="display: block; font-size: 14px; font-weight: 600; color: #4a5568; margin-bottom: 8px;">البريد الإلكتروني</label>
                        <input type="email" id="resetUsername" placeholder="أدخل البريد الإلكتروني المسجل" style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: border-color 0.3s ease;">
                    </div>
                    <div class="input-group" style="margin-bottom: 20px;">
                        <label for="resetBranch" style="display: block; font-size: 14px; font-weight: 600; color: #4a5568; margin-bottom: 8px;">نوع الفرع</label>
                        <select id="resetBranch" style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: border-color 0.3s ease;">
                            <option value="">اختر نوع الفرع</option>
                            <option value="A">A - الإدارة العامة</option>
                            <option value="B">B - المبيعات</option>
                            <option value="C">C - الجودة</option>
                            <option value="D">D - العرض</option>
                        </select>
                    </div>
                    <div class="reset-info" style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin-top: 15px; border: 1px solid #bfdbfe;">
                        <p style="color: #0369a1; margin: 0; font-size: 14px; line-height: 1.5;">
                            📨 سيتم إرسال طلب إعادة تعيين كلمة المرور إلى الفرع الرئيسي للموافقة.
                        </p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="submitPasswordReset()" class="btn btn-primary">إرسال الطلب</button>
                <button onclick="closePasswordReset()" class="btn btn-secondary">إلغاء</button>
            </div>
        </div>
    </div>



    <!-- Header -->
    <header class="header" id="mainHeader" style="display: none;">
        <div class="header-container">
            <!-- Left Side -->
            <div class="header-left">
                <!-- Three Dots Dropdown -->
                <div class="dropdown" id="mainDropdown">
                    <button class="three-dots" onclick="toggleDropdown()">
                        <svg class="icon" viewBox="0 0 24 24">
                            <circle cx="12" cy="5" r="2"/>
                            <circle cx="12" cy="12" r="2"/>
                            <circle cx="12" cy="19" r="2"/>
                        </svg>
                    </button>
                    <div class="dropdown-content" id="dropdownContent">
                        <button class="dropdown-item" onclick="showProfile(); closeDropdown()">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            <span data-translate="profile">البروفايل الشخصي</span>
                        </button>
                        <button class="dropdown-item" onclick="showModal('الإعدادات'); closeDropdown()">
                            <svg class="icon" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                            <span data-translate="settings">الإعدادات</span>
                        </button>
                        <button class="dropdown-item" onclick="showLanguageModal(); closeDropdown()">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
                            </svg>
                            <span data-translate="language">اللغة</span>
                        </button>
                    </div>
                </div>

                <!-- Logo -->
                <div class="logo" onclick="showModal('TV-Office Management System v1.0')">
                    <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                        <line x1="8" y1="21" x2="16" y2="21"/>
                        <line x1="12" y1="17" x2="12" y2="21"/>
                        <circle cx="19" cy="6" r="1" fill="white"/>
                        <rect x="5" y="7" width="14" height="8" rx="1" fill="white" opacity="0.3"/>
                    </svg>
                </div>

                <!-- App Name -->
                <div class="app-name" id="systemTitle" data-translate="systemTitle">TV-Office</div>

                <!-- Home Section -->
                <div class="home-section">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <span data-translate="homePage">الرئيسية</span>
                </div>

                <!-- Notification Bell -->
                <button class="notification-bell has-notification" onclick="toggleNotification()">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                    </svg>
                    <span class="notification-dot" id="notificationDot"></span>
                </button>

                <!-- User Info -->
                <div class="user-info" onclick="showProfile()" title="عرض البروفايل الشخصي">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span>حسين نهاد</span>
                </div>
            </div>

            <!-- Fullscreen Button -->
            <button onclick="toggleFullscreen()" class="fullscreen-btn" title="وضع العرض الكامل للتلفاز">
                <svg class="icon" viewBox="0 0 24 24">
                    <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                </svg>
                عرض كامل
            </button>

            <!-- Right Side - Logout Only -->
            <div class="header-actions">
                <button class="logout-btn" onclick="logout()">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                        <polyline points="16,17 21,12 16,7"/>
                        <line x1="21" y1="12" x2="9" y2="12"/>
                    </svg>
                    <span data-translate="logout">تسجيل الخروج</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">


        <!-- Home Cards Grid -->
        <div class="cards-grid">
            <!-- الحافظة -->
            <div class="home-card blue-border" onclick="showModal('الحافظة')">
                <div class="card-icon blue">
                    <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
                        <circle cx="12" cy="13" r="2"/>
                        <path d="M12 15v4"/>
                        <path d="M10 17h4"/>
                    </svg>
                </div>
                <h3 class="card-title" data-translate="searchFiles">الحافظة</h3>
                <p class="card-description" data-translate="searchFilesDesc">الملفات المرسلة والمستلمة</p>
            </div>

            <!-- الإعدادات -->
            <div class="home-card gray-border" onclick="showModal('الإعدادات')">
                <div class="card-icon gray">
                    <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                </div>
                <h3 class="card-title" data-translate="systemSettings">الإعدادات</h3>
                <p class="card-description" data-translate="publishContentDesc">إدارة المستخدمين والصلاحيات</p>
            </div>

            <!-- البحث -->
            <div class="home-card green-border" data-type="search" onclick="showModal('البحث')">
                <div class="card-icon green">
                    <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="M21 21l-4.35-4.35"/>
                        <path d="M11 6v10"/>
                        <path d="M6 11h10"/>
                    </svg>
                </div>
                <h3 class="card-title" data-translate="searchFiles">البحث</h3>
                <p class="card-description" data-translate="deletedFilesDesc">البحث في الملفات المنشورة</p>
            </div>

            <!-- النشر -->
            <div class="home-card purple-border" data-type="publish" onclick="showModal('النشر')">
                <div class="card-icon purple">
                    <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/>
                        <polyline points="12,13 12,7"/>
                        <polyline points="9,10 12,7 15,10"/>
                    </svg>
                </div>
                <h3 class="card-title" data-translate="publishContent">النشر</h3>
                <p class="card-description" data-translate="displayManagementDesc">نشر ملف جديد</p>
            </div>

            <!-- المحذوفات -->
            <div class="home-card red-border" data-type="delete" onclick="showModal('المحذوفات')">
                <div class="card-icon red">
                    <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="3,6 5,6 21,6"/>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                        <line x1="10" y1="11" x2="10" y2="17"/>
                        <line x1="14" y1="11" x2="14" y2="17"/>
                    </svg>
                </div>
                <h3 class="card-title" data-translate="deletedFiles">المحذوفات</h3>
                <p class="card-description" data-translate="systemSettingsDesc">الملفات المحذوفة</p>
            </div>

            <!-- شاشات العرض -->
            <div class="home-card indigo-border" data-type="displays" onclick="showModal('شاشات العرض')">
                <div class="card-icon indigo">
                    <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                        <line x1="8" y1="21" x2="16" y2="21"/>
                        <line x1="12" y1="17" x2="12" y2="21"/>
                        <circle cx="19" cy="6" r="1"/>
                        <rect x="6" y="7" width="12" height="8" rx="1" fill="currentColor" opacity="0.2"/>
                        <path d="M9 10l3 2 3-2"/>
                    </svg>
                </div>
                <h3 class="card-title" data-translate="displayScreens">شاشات العرض</h3>
                <p class="card-description" data-translate="displayScreensDesc">إدارة شاشات التلفزيون</p>
            </div>
        </div>
    </main>

    <!-- Modal -->
    <div class="modal-overlay" id="modal">
        <div class="modal-content">
            <h3 class="modal-title" id="modalTitle">عنوان النافذة</h3>
            <p class="modal-text" id="modalText">هذه النافذة قيد التطوير...</p>
            <button class="modal-btn" onclick="closeModal()">إغلاق</button>
        </div>
    </div>

    <script>
        let hasNotification = true;

        function showModal(title) {
            document.getElementById('modalTitle').textContent = title;

            if (title === 'TV-Office Management System v1.0') {
                document.getElementById('modalText').textContent = 'نظام إدارة التلفزيونات المكتبية - الإصدار 1.0';
            } else if (title === 'الحافظة') {
                showArchiveWindow();
                return;
            } else if (title === 'الإعدادات') {
                showSettingsWindow();
                return;
            } else if (title === 'البحث') {
                showSearchWindow();
                return;
            } else if (title === 'النشر') {
                showPublishWindow();
                return;
            } else if (title === 'المحذوفات') {
                showDeletedFilesWindow();
                return;
            } else if (title === 'شاشات العرض') {
                showDisplaysWindow();
                return;
            } else {
                document.getElementById('modalText').textContent = 'هذه الخاصية قيد التطوير وستكون متاحة قريباً...';
            }

            document.getElementById('modal').style.display = 'flex';
        }

        let selectedFileType = '';
        let archiveSearchQuery = '';
        let currentSettingsTab = 'users';
        let selectedAction = '';
        let users = ['admin', 'sales', 'quality', 'display'];

        // نظام تخزين البيانات المحلي
        class DataStorage {
            constructor() {
                this.initializeData();
            }

            initializeData() {
                // إنشاء البيانات الافتراضية إذا لم تكن موجودة
                if (!localStorage.getItem('tvOfficeData')) {
                    const defaultData = {
                        users: [
                            { id: 1, name: 'أحمد محمد', email: '<EMAIL>', department: 'المبيعات', role: 'مستخدم', status: 'نشط' },
                            { id: 2, name: 'سارة أحمد', email: '<EMAIL>', department: 'الموارد البشرية', role: 'مدير', status: 'نشط' },
                            { id: 3, name: 'محمد علي', email: '<EMAIL>', department: 'المحاسبة', role: 'مستخدم', status: 'نشط' }
                        ],
                        files: [
                            { id: 1, name: 'تقرير المبيعات الشهري.pdf', user: 'أحمد محمد', department: 'المبيعات', type: 'pdf', date: '2024-01-15', time: '14:30', size: '2.5 MB' },
                            { id: 2, name: 'عقد عمل جديد.doc', user: 'سارة أحمد', department: 'الموارد البشرية', type: 'doc', date: '2024-01-14', time: '11:20', size: '1.2 MB' },
                            { id: 3, name: 'الميزانية السنوية.xlsx', user: 'محمد علي', department: 'المحاسبة', type: 'excel', date: '2024-01-13', time: '09:15', size: '3.8 MB' }
                        ],
                        deletedFiles: [
                            { id: 1, name: 'تقرير قديم.pdf', user: 'أحمد محمد', department: 'المبيعات', deleteDate: '2024-01-10', size: '2.5 MB' },
                            { id: 2, name: 'عقد منتهي.doc', user: 'سارة أحمد', department: 'الموارد البشرية', deleteDate: '2024-01-08', size: '1.2 MB' }
                        ],
                        displays: [
                            { id: 1, name: 'شاشة الاستقبال الرئيسية', location: 'الطابق الأول', status: 'connected', lastConnection: '2024-01-15 14:30', details: 'متصلة وتعمل بشكل طبيعي', reason: '', currentFile: 'تقرير المبيعات.pdf' },
                            { id: 2, name: 'شاشة قسم المبيعات', location: 'الطابق الثاني', status: 'connected', lastConnection: '2024-01-15 14:25', details: 'متصلة وتعمل بشكل طبيعي', reason: '', currentFile: 'عروض المنتجات.pdf' },
                            { id: 3, name: 'شاشة قسم الموارد البشرية', location: 'الطابق الثالث', status: 'disconnected', lastConnection: '2024-01-15 12:15', details: 'غير متصلة - مطفأة', reason: 'تم إطفاء الشاشة يدوياً', currentFile: '' }
                        ],
                        settings: {
                            systemName: 'نظام إدارة التلفزيونات المكتبية',
                            timezone: 'Asia/Riyadh',
                            displayDuration: 30,
                            quality: '1080p',
                            autoRotate: true,
                            showClock: true,
                            sessionTimeout: 60,
                            maxFileSize: 10,
                            requireApproval: false,
                            enableLogs: true,
                            emailNotifications: true,
                            smsNotifications: false,
                            systemAlerts: true,
                            notificationEmail: '<EMAIL>',
                            backupFrequency: 'daily',
                            backupTime: '02:00'
                        }
                    };
                    this.saveData(defaultData);
                }
            }

            getData() {
                return JSON.parse(localStorage.getItem('tvOfficeData'));
            }

            saveData(data) {
                localStorage.setItem('tvOfficeData', JSON.stringify(data));
            }

            addUser(user) {
                const data = this.getData();
                user.id = Date.now();
                data.users.push(user);
                this.saveData(data);
                return user;
            }

            addFile(file) {
                const data = this.getData();
                file.id = Date.now();
                file.date = new Date().toISOString().split('T')[0];
                file.time = new Date().toTimeString().split(' ')[0].substring(0, 5);
                data.files.push(file);
                this.saveData(data);
                return file;
            }

            addDisplay(display) {
                const data = this.getData();
                display.id = Date.now();
                display.status = 'connected';
                display.lastConnection = new Date().toISOString().replace('T', ' ').substring(0, 16);
                display.details = 'متصلة وتعمل بشكل طبيعي';
                display.reason = '';
                display.currentFile = '';
                data.displays.push(display);
                this.saveData(data);
                return display;
            }
        }

        // إنشاء مثيل من نظام التخزين
        const storage = new DataStorage();

        // نظام إدارة المستخدمين وتسجيل الدخول
        class AuthSystem {
            constructor() {
                this.initializeUsers();
                this.checkLoginStatus();
            }

            initializeUsers() {
                if (!localStorage.getItem('systemUsers')) {
                    const defaultUsers = {
                        'hussein': {
                            username: 'hussein',
                            password: 'admin123',
                            firstName: 'حسين',
                            lastName: 'جعفر',
                            position: 'مدير النظام',
                            department: 'قسم تقنية المعلومات',
                            phone: '+964 xxx xxxx',
                            role: 'admin',
                            branch: 'A',
                            joinDate: new Date().toLocaleDateString('ar-SA'),
                            avatar: null,
                            isActive: true,
                            mustChangePassword: false
                        },
                        'admin': {
                            username: 'admin',
                            password: 'admin123',
                            firstName: 'أحمد',
                            lastName: 'المدير',
                            position: 'المدير العام',
                            department: 'الإدارة العامة',
                            phone: '+964 xxx xxxx',
                            role: 'admin',
                            branch: 'A',
                            joinDate: new Date().toLocaleDateString('ar-SA'),
                            avatar: null,
                            isActive: true,
                            mustChangePassword: false
                        },
                        'sales': {
                            username: 'sales',
                            password: 'sales123',
                            firstName: 'محمد',
                            lastName: 'المبيعات',
                            position: 'مدير المبيعات',
                            department: 'قسم المبيعات',
                            phone: '+964 xxx xxxx',
                            role: 'sales',
                            branch: 'B',
                            joinDate: new Date().toLocaleDateString('ar-SA'),
                            avatar: null,
                            isActive: true,
                            mustChangePassword: false
                        },
                        'quality': {
                            username: 'quality',
                            password: 'quality123',
                            firstName: 'فاطمة',
                            lastName: 'الجودة',
                            position: 'مدير الجودة',
                            department: 'قسم الجودة',
                            phone: '+964 xxx xxxx',
                            role: 'quality',
                            branch: 'C',
                            joinDate: new Date().toLocaleDateString('ar-SA'),
                            avatar: null,
                            isActive: true,
                            mustChangePassword: false
                        },
                        'display': {
                            username: 'display',
                            password: 'display123',
                            firstName: 'شاشة',
                            lastName: 'العرض',
                            position: 'شاشة العرض',
                            department: 'قسم العرض',
                            phone: '+964 xxx xxxx',
                            role: 'display',
                            branch: 'D',
                            joinDate: new Date().toLocaleDateString('ar-SA'),
                            avatar: null,
                            isActive: true,
                            mustChangePassword: false
                        }
                    };
                    localStorage.setItem('systemUsers', JSON.stringify(defaultUsers));
                    console.log('✅ تم تحميل المستخدمين بنجاح، بما في ذلك:', 'hussein');
                    console.log('💾 البيانات المحفوظة:', JSON.stringify(defaultUsers, null, 2));
                }

                // التحقق من وجود مستخدم حسين
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                console.log('🔍 فحص البيانات المحفوظة:', users);

                if (users && users['hussein']) {
                    console.log('✅ مستخدم حسين موجود في النظام:', users['hussein']);
                } else {
                    console.log('❌ مستخدم حسين غير موجود في النظام');
                    console.log('📋 المستخدمين الموجودين:', users ? Object.keys(users) : 'لا توجد بيانات');
                }

                if (!localStorage.getItem('passwordResetRequests')) {
                    localStorage.setItem('passwordResetRequests', JSON.stringify([]));
                }
            }

            checkLoginStatus() {
                // إجبار إظهار شاشة تسجيل الدخول دائماً (للاختبار)
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('userType');
                this.showLoginScreen();
            }

            // دالة التحقق من تسجيل الدخول
            validateLogin(username, password, branch) {
                console.log('🔍 بدء التحقق من بيانات تسجيل الدخول...');
                console.log('👤 اسم المستخدم المطلوب:', username);

                try {
                    const usersData = localStorage.getItem('systemUsers');
                    console.log('📦 بيانات localStorage الخام:', usersData);

                    if (!usersData) {
                        console.log('❌ لا توجد بيانات في localStorage');
                        return { success: false, message: 'خطأ في النظام - لا توجد بيانات مستخدمين' };
                    }

                    const users = JSON.parse(usersData);
                    console.log('👥 جميع المستخدمين:', users);
                    console.log('🔑 مفاتيح المستخدمين:', Object.keys(users));

                    const user = users[username];
                    console.log('👤 المستخدم المطلوب:', user);

                    if (!user) {
                        console.log('❌ المستخدم غير موجود:', username);
                        console.log('📋 المستخدمين المتاحين:', Object.keys(users));
                        return { success: false, message: 'اسم المستخدم غير مسجل في النظام' };
                    }

                    if (user.password !== password) {
                        console.log('❌ كلمة المرور خاطئة. المتوقعة:', user.password, 'المدخلة:', password);
                        return { success: false, message: 'كلمة المرور غير صحيحة' };
                    }

                    if (user.branch !== branch) {
                        console.log('❌ نوع الفرع خاطئ. المتوقع:', user.branch, 'المدخل:', branch);
                        return { success: false, message: 'نوع الفرع غير صحيح لهذا المستخدم' };
                    }

                    if (user.isActive === false) {
                        console.log('❌ الحساب غير مفعل');
                        return { success: false, message: 'الحساب غير مفعل - يرجى التواصل مع الإدارة' };
                    }

                    console.log('✅ تم التحقق بنجاح من جميع البيانات');
                    return { success: true, user: user };

                } catch (error) {
                    console.error('❌ خطأ في معالجة البيانات:', error);
                    return { success: false, message: 'خطأ في معالجة بيانات المستخدمين' };
                }
            }

            // دالة تسجيل دخول المستخدم
            loginUser(username, branch, rememberMe) {
                console.log('🔑 تسجيل دخول المستخدم...');

                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', username);
                localStorage.setItem('userType', branch);

                if (rememberMe) {
                    const expiryDate = new Date();
                    expiryDate.setDate(expiryDate.getDate() + 30);
                    localStorage.setItem('loginExpiry', expiryDate.toISOString());
                    localStorage.setItem('rememberMe', 'true');

                    // حفظ جلسة TV-Office
                    localStorage.setItem('tvOfficeSession', 'active');
                    localStorage.setItem('tvOfficeRememberMe', 'true');
                    localStorage.setItem('tvOfficeSessionExpiry', expiryDate.getTime().toString());

                    console.log('💾 تم حفظ الجلسة لمدة 30 يوم');
                } else {
                    // إذا لم يختر تذكرني، احذف الجلسة المحفوظة
                    localStorage.removeItem('tvOfficeSession');
                    localStorage.removeItem('tvOfficeRememberMe');
                    localStorage.removeItem('tvOfficeSessionExpiry');

                    console.log('🗑️ لم يتم حفظ الجلسة');
                }

                // إخفاء شاشة تسجيل الدخول وإظهار النظام
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('mainHeader').style.display = 'block';

                // تحديث المتغيرات العامة
                window.currentUser = username;
                window.currentUserType = branch;

                console.log('✅ تم تسجيل الدخول بنجاح');
            }

            showLoginScreen() {
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('mainHeader').style.display = 'none';

                // إخفاء المحتوى الرئيسي
                const mainContent = document.querySelector('.container');
                if (mainContent) {
                    mainContent.style.display = 'none';
                }
            }

            hideLoginScreen() {
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('mainHeader').style.display = 'block';

                // إظهار المحتوى الرئيسي
                const mainContent = document.querySelector('.container');
                if (mainContent) {
                    mainContent.style.display = 'block';
                }
            }

            validateLogin(email, password, branch) {
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                const user = users[email];

                if (!user) {
                    return { success: false, message: 'البريد الإلكتروني غير مسجل في النظام' };
                }

                if (!user.isActive) {
                    return { success: false, message: 'الحساب معطل، يرجى التواصل مع الإدارة' };
                }

                if (user.password !== password) {
                    return { success: false, message: 'كلمة المرور غير صحيحة' };
                }

                if (user.branch !== branch) {
                    return { success: false, message: 'نوع الفرع غير صحيح لهذا المستخدم' };
                }

                return { success: true, user: user };
            }

            loginUser(email, branch, rememberMe = false) {
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                const user = users[email];

                // حفظ حالة تسجيل الدخول
                const loginData = {
                    isLoggedIn: 'true',
                    currentUser: {
                        email: email,
                        branch: branch,
                        firstName: user.firstName,
                        lastName: user.lastName,
                        position: user.position,
                        department: user.department,
                        phone: user.phone,
                        role: user.role,
                        joinDate: user.joinDate,
                        avatar: user.avatar
                    },
                    loginTime: new Date().toISOString(),
                    rememberMe: rememberMe
                };

                localStorage.setItem('isLoggedIn', loginData.isLoggedIn);
                localStorage.setItem('currentUser', JSON.stringify(loginData.currentUser));
                localStorage.setItem('loginTime', loginData.loginTime);

                if (rememberMe) {
                    localStorage.setItem('rememberMe', 'true');
                    // تعيين انتهاء صلاحية بعد 30 يوم
                    const expiryDate = new Date();
                    expiryDate.setDate(expiryDate.getDate() + 30);
                    localStorage.setItem('loginExpiry', expiryDate.toISOString());
                } else {
                    localStorage.removeItem('rememberMe');
                    localStorage.removeItem('loginExpiry');
                }

                // تحديد نوع المستخدم حسب الفرع
                const branchTypeMap = {
                    'A': 'admin',
                    'B': 'sales',
                    'C': 'quality',
                    'D': 'display'
                };

                currentUserType = branchTypeMap[branch];
                userPermissions = permissionSets[currentUserType].permissions;
                localStorage.setItem('userType', currentUserType);

                // إخفاء شاشة تسجيل الدخول وتطبيق الواجهة
                this.hideLoginScreen();

                // تطبيق الواجهة حسب النوع
                applyUserInterface();

                // تحديث اسم المستخدم في الهيدر
                updateHeaderUserName();
            }

            logout() {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('userType');
                location.reload();
            }

            addUser(username, password, branch, name, role = 'user') {
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                users[username] = { password, branch, name, role };
                localStorage.setItem('systemUsers', JSON.stringify(users));
                return true;
            }

            updatePassword(username, newPassword) {
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                if (users[username]) {
                    users[username].password = newPassword;
                    localStorage.setItem('systemUsers', JSON.stringify(users));
                    return true;
                }
                return false;
            }

            submitPasswordResetRequest(username, branch) {
                const requests = JSON.parse(localStorage.getItem('passwordResetRequests'));
                const newRequest = {
                    id: Date.now(),
                    username: username,
                    branch: branch,
                    timestamp: new Date().toISOString(),
                    status: 'pending'
                };
                requests.push(newRequest);
                localStorage.setItem('passwordResetRequests', JSON.stringify(requests));
                return newRequest.id;
            }

            getPasswordResetRequests() {
                return JSON.parse(localStorage.getItem('passwordResetRequests'));
            }

            approvePasswordReset(requestId, newPassword) {
                const requests = JSON.parse(localStorage.getItem('passwordResetRequests'));
                const request = requests.find(r => r.id === requestId);

                if (request) {
                    request.status = 'approved';
                    request.newPassword = newPassword;
                    this.updatePassword(request.username, newPassword);
                    localStorage.setItem('passwordResetRequests', JSON.stringify(requests));
                    return true;
                }
                return false;
            }

            rejectPasswordReset(requestId, reason) {
                const requests = JSON.parse(localStorage.getItem('passwordResetRequests'));
                const request = requests.find(r => r.id === requestId);

                if (request) {
                    request.status = 'rejected';
                    request.reason = reason;
                    localStorage.setItem('passwordResetRequests', JSON.stringify(requests));
                    return true;
                }
                return false;
            }
        }

        // إنشاء مثيل من نظام المصادقة
        const authSystem = new AuthSystem();

        // إعداد EmailJS لإرسال الإيميلات الحقيقية
        (function() {
            emailjs.init("YOUR_PUBLIC_KEY"); // سيتم تحديثه لاحقاً
        })();

        // نظام إرسال الإيميلات الحقيقي
        class EmailService {
            constructor() {
                this.serviceID = 'service_tv_office';
                this.templateID = 'template_password_reset';
                this.publicKey = 'YOUR_PUBLIC_KEY';
            }

            async sendPasswordResetEmail(email, resetCode, userName) {
                const templateParams = {
                    to_email: email,
                    to_name: userName,
                    reset_code: resetCode,
                    system_name: 'نظام إدارة التلفزيونات المكتبية',
                    company_name: 'مصفاة النفط',
                    reset_link: `${window.location.origin}/reset-password?code=${resetCode}&email=${encodeURIComponent(email)}`,
                    support_email: '<EMAIL>'
                };

                try {
                    const response = await emailjs.send(this.serviceID, this.templateID, templateParams);
                    console.log('Email sent successfully:', response);
                    return { success: true, messageId: response.text };
                } catch (error) {
                    console.error('Email sending failed:', error);
                    return { success: false, error: error.text };
                }
            }

            async sendWelcomeEmail(email, userName, tempPassword) {
                const templateParams = {
                    to_email: email,
                    to_name: userName,
                    temp_password: tempPassword,
                    system_name: 'نظام إدارة التلفزيونات المكتبية',
                    company_name: 'مصفاة النفط',
                    login_link: window.location.origin,
                    support_email: '<EMAIL>'
                };

                try {
                    const response = await emailjs.send(this.serviceID, 'template_welcome', templateParams);
                    return { success: true, messageId: response.text };
                } catch (error) {
                    console.error('Welcome email failed:', error);
                    return { success: false, error: error.text };
                }
            }
        }

        // إنشاء مثيل من خدمة الإيميل
        const emailService = new EmailService();

        // ===== نظام تغيير اللغة =====
        const translations = {
            ar: {
                // Header
                systemTitle: 'TV-Office',
                homePage: 'الصفحة الرئيسية',
                logout: 'تسجيل الخروج',
                profile: 'البروفايل الشخصي',
                settings: 'الإعدادات',
                language: 'اللغة',

                // Login
                loginTitle: 'تسجيل الدخول',
                username: 'اسم المستخدم',
                password: 'كلمة المرور',
                branchType: 'نوع الفرع',
                rememberMe: 'تذكرني لمدة 30 يوم',
                loginButton: 'تسجيل الدخول',
                forgotPassword: 'نسيت كلمة المرور؟',
                createAccount: 'إنشاء حساب جديد',

                // Branch Types
                branchA: 'A - الإدارة العامة',
                branchB: 'B - فرع المبيعات',
                branchC: 'C - فرع الجودة',
                branchD: 'D - شاشة العرض',

                // Home Cards
                searchFiles: 'البحث في الملفات',
                searchFilesDesc: 'الملفات المرسلة والمستلمة',
                publishContent: 'نشر المحتوى',
                publishContentDesc: 'إدارة المستخدمين والصلاحيات',
                deletedFiles: 'الملفات المحذوفة',
                deletedFilesDesc: 'البحث في الملفات المنشورة',
                displayManagement: 'إدارة الشاشات',
                displayManagementDesc: 'نشر ملف جديد',
                systemSettings: 'إعدادات النظام',
                systemSettingsDesc: 'الملفات المحذوفة',
                displayScreens: 'شاشات العرض',
                displayScreensDesc: 'إدارة شاشات التلفزيون',

                // Profile
                profileTitle: 'البروفايل الشخصي',
                firstName: 'الاسم الأول',
                lastName: 'الاسم الأخير',
                position: 'المنصب',
                department: 'القسم',
                phone: 'رقم الهاتف',
                joinDate: 'تاريخ الانضمام',
                changePassword: 'تغيير كلمة المرور',
                saveChanges: 'حفظ التغييرات',
                close: 'إغلاق',
                cancel: 'إلغاء',

                // Messages
                welcome: 'مرحباً',
                loginSuccess: 'تم تسجيل الدخول بنجاح',
                loginError: 'فشل تسجيل الدخول',
                fillAllFields: 'يرجى ملء جميع الحقول',
                invalidEmail: 'البريد الإلكتروني غير صحيح',
                wrongPassword: 'كلمة المرور غير صحيحة',
                wrongBranch: 'نوع الفرع غير صحيح',
                accountInactive: 'الحساب غير مفعل',
                changesSaved: 'تم حفظ التغييرات بنجاح',
                passwordChanged: 'تم تغيير كلمة المرور بنجاح'
            },
            en: {
                // Header
                systemTitle: 'TV-Office',
                homePage: 'Home Page',
                logout: 'Logout',
                profile: 'Profile',
                settings: 'Settings',
                language: 'Language',

                // Login
                loginTitle: 'Login',
                email: 'Email Address',
                password: 'Password',
                branchType: 'Branch Type',
                rememberMe: 'Remember me for 30 days',
                loginButton: 'Login',
                forgotPassword: 'Forgot Password?',
                createAccount: 'Create New Account',

                // Branch Types
                branchA: 'A - General Management',
                branchB: 'B - Sales Branch',
                branchC: 'C - Quality Branch',
                branchD: 'D - Display Screen',

                // Home Cards
                searchFiles: 'Search Files',
                searchFilesDesc: 'Sent and Received Files',
                publishContent: 'Publish Content',
                publishContentDesc: 'User and Permission Management',
                deletedFiles: 'Deleted Files',
                deletedFilesDesc: 'Search Published Files',
                displayManagement: 'Display Management',
                displayManagementDesc: 'Publish New File',
                systemSettings: 'System Settings',
                systemSettingsDesc: 'Deleted Files',
                displayScreens: 'Display Screens',
                displayScreensDesc: 'TV Screen Management',

                // Profile
                profileTitle: 'Personal Profile',
                firstName: 'First Name',
                lastName: 'Last Name',
                position: 'Position',
                department: 'Department',
                phone: 'Phone Number',
                joinDate: 'Join Date',
                changePassword: 'Change Password',
                saveChanges: 'Save Changes',
                close: 'Close',
                cancel: 'Cancel',

                // Messages
                welcome: 'Welcome',
                loginSuccess: 'Login successful',
                loginError: 'Login failed',
                fillAllFields: 'Please fill all fields',
                invalidEmail: 'Invalid email address',
                wrongPassword: 'Incorrect password',
                wrongBranch: 'Incorrect branch type',
                accountInactive: 'Account is inactive',
                changesSaved: 'Changes saved successfully',
                passwordChanged: 'Password changed successfully'
            }
        };

        // اللغة الحالية
        let currentLanguage = localStorage.getItem('language') || 'ar';

        // دالة تغيير اللغة
        function changeLanguage(lang) {
            console.log('🌐 تغيير اللغة إلى:', lang);
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // تغيير اتجاه الصفحة
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
            document.documentElement.lang = lang;
            console.log('📄 تم تغيير اتجاه الصفحة إلى:', document.documentElement.dir);

            // تطبيق الترجمات
            applyTranslations();
            console.log('✅ تم تطبيق الترجمات');

            // إغلاق قائمة اللغة
            closeLanguageModal();

            // رسالة تأكيد
            const message = lang === 'ar' ? 'تم تغيير اللغة إلى العربية' : 'Language changed to English';
            alert(message);
        }

        // تطبيق الترجمات على العناصر
        function applyTranslations() {
            const t = translations[currentLanguage];
            console.log('🔤 تطبيق ترجمات اللغة:', currentLanguage);
            console.log('📝 ملف الترجمة:', t);

            // تحديث العناصر بـ data-translate
            const elementsWithTranslate = document.querySelectorAll('[data-translate]');
            console.log('🎯 عدد العناصر للترجمة:', elementsWithTranslate.length);

            elementsWithTranslate.forEach(element => {
                const key = element.getAttribute('data-translate');
                if (t[key]) {
                    if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'email' || element.type === 'password')) {
                        element.placeholder = t[key];
                    } else {
                        element.textContent = t[key];
                    }
                }
            });

            // تحديث العناصر المحددة
            const systemTitle = document.getElementById('systemTitle');
            if (systemTitle) systemTitle.textContent = t.systemTitle;

            const homePageSpan = document.querySelector('.home-section span');
            if (homePageSpan) homePageSpan.textContent = t.homePage;

            // تحديث عنوان الصفحة
            document.title = t.systemTitle;

            // تحديث كروت الصفحة الرئيسية
            const homeCards = document.querySelectorAll('.home-card');
            console.log('🏠 عدد الكروت الموجودة:', homeCards.length);

            if (homeCards.length >= 6) {
                // الكرت الأول - الحافظة
                const card1Title = homeCards[0].querySelector('h3');
                const card1Desc = homeCards[0].querySelector('p');
                if (card1Title) card1Title.textContent = t.searchFiles;
                if (card1Desc) card1Desc.textContent = t.searchFilesDesc;

                // الكرت الثاني - الإعدادات
                const card2Title = homeCards[1].querySelector('h3');
                const card2Desc = homeCards[1].querySelector('p');
                if (card2Title) card2Title.textContent = t.systemSettings;
                if (card2Desc) card2Desc.textContent = t.publishContentDesc;

                // الكرت الثالث - البحث
                const card3Title = homeCards[2].querySelector('h3');
                const card3Desc = homeCards[2].querySelector('p');
                if (card3Title) card3Title.textContent = t.searchFiles;
                if (card3Desc) card3Desc.textContent = t.deletedFilesDesc;

                // الكرت الرابع - النشر
                const card4Title = homeCards[3].querySelector('h3');
                const card4Desc = homeCards[3].querySelector('p');
                if (card4Title) card4Title.textContent = t.publishContent;
                if (card4Desc) card4Desc.textContent = t.displayManagementDesc;

                // الكرت الخامس - المحذوفات
                const card5Title = homeCards[4].querySelector('h3');
                const card5Desc = homeCards[4].querySelector('p');
                if (card5Title) card5Title.textContent = t.deletedFiles;
                if (card5Desc) card5Desc.textContent = t.systemSettingsDesc;

                // الكرت السادس - شاشات العرض
                const card6Title = homeCards[5].querySelector('h3');
                const card6Desc = homeCards[5].querySelector('p');
                if (card6Title) card6Title.textContent = t.displayScreens;
                if (card6Desc) card6Desc.textContent = t.displayScreensDesc;
            }

            console.log('✅ تم تحديث جميع الترجمات');
        }

        // دالة للحصول على النص المترجم
        function getTranslation(key) {
            return translations[currentLanguage][key] || key;
        }

        // دالة لحذف الجلسة المحفوظة
        function clearSavedSession() {
            localStorage.removeItem('tvOfficeSession');
            localStorage.removeItem('tvOfficeRememberMe');
            localStorage.removeItem('tvOfficeSessionExpiry');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('userType');
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('loginExpiry');
            localStorage.removeItem('rememberMe');
            console.log('🗑️ تم حذف جميع بيانات الجلسة');
        }

        // دالة للحصول على اسم الفرع
        function getBranchName(branch) {
            const branchNames = {
                'A': getTranslation('branchA'),
                'B': getTranslation('branchB'),
                'C': getTranslation('branchC'),
                'D': getTranslation('branchD')
            };
            return branchNames[branch] || branch;
        }

        // إظهار نافذة اختيار اللغة
        function showLanguageModal() {
            console.log('🌐 فتح نافذة اختيار اللغة');
            const modalHTML = `
                <div id="languageModal" class="modal" style="display: flex;">
                    <div class="modal-content" style="max-width: 350px; background: rgba(255, 255, 255, 0.98); backdrop-filter: blur(20px); border-radius: 20px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15); padding: 25px;">
                        <div class="modal-header" style="text-align: center; margin-bottom: 20px;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
                                </svg>
                            </div>
                            <h3 style="color: #333; margin: 0 0 5px 0; font-size: 18px;" data-translate="language">اللغة</h3>
                            <p style="color: #666; font-size: 14px; margin: 0;">اختر لغة النظام</p>
                            <button onclick="closeLanguageModal()" class="close-btn" style="position: absolute; top: 15px; left: 15px; background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">✕</button>
                        </div>

                        <div style="display: grid; gap: 10px;">
                            <button onclick="changeLanguage('ar')" style="display: flex; align-items: center; gap: 15px; padding: 15px; background: ${currentLanguage === 'ar' ? '#f0f9ff' : 'white'}; border: 2px solid ${currentLanguage === 'ar' ? '#0ea5e9' : '#e5e7eb'}; border-radius: 12px; cursor: pointer; font-size: 16px; font-family: 'Cairo', sans-serif; transition: all 0.3s ease;">
                                <div style="width: 40px; height: 30px; background: linear-gradient(to bottom, #009639, white, #ce1126); border-radius: 4px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🇸🇦</div>
                                <div style="flex: 1; text-align: right;">
                                    <div style="font-weight: 600; color: #333;">العربية</div>
                                    <div style="font-size: 14px; color: #666;">Arabic</div>
                                </div>
                                ${currentLanguage === 'ar' ? '<div style="color: #0ea5e9;">✓</div>' : ''}
                            </button>

                            <button onclick="changeLanguage('en')" style="display: flex; align-items: center; gap: 15px; padding: 15px; background: ${currentLanguage === 'en' ? '#f0f9ff' : 'white'}; border: 2px solid ${currentLanguage === 'en' ? '#0ea5e9' : '#e5e7eb'}; border-radius: 12px; cursor: pointer; font-size: 16px; font-family: 'Cairo', sans-serif; transition: all 0.3s ease;">
                                <div style="width: 40px; height: 30px; background: linear-gradient(to bottom, #012169, white, #C8102E); border-radius: 4px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🇬🇧</div>
                                <div style="flex: 1; text-align: ${currentLanguage === 'ar' ? 'right' : 'left'};">
                                    <div style="font-weight: 600; color: #333;">English</div>
                                    <div style="font-size: 14px; color: #666;">الإنجليزية</div>
                                </div>
                                ${currentLanguage === 'en' ? '<div style="color: #0ea5e9;">✓</div>' : ''}
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closeLanguageModal() {
            const modal = document.getElementById('languageModal');
            if (modal) {
                modal.remove();
            }
        }

        // تطبيق اللغة والتحقق من الجلسة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 تحميل الصفحة - اللغة الحالية:', currentLanguage);
            document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
            document.documentElement.lang = currentLanguage;

            // تأخير تطبيق الترجمات للتأكد من تحميل جميع العناصر
            setTimeout(() => {
                applyTranslations();
            }, 500);

            // التحقق من الجلسة المحفوظة
            checkSavedSession();
        });

        // دالة للتحقق من الجلسة المحفوظة
        function checkSavedSession() {
            console.log('🔍 فحص الجلسة المحفوظة...');

            const savedSession = localStorage.getItem('tvOfficeSession');
            const rememberMe = localStorage.getItem('tvOfficeRememberMe');
            const sessionExpiry = localStorage.getItem('tvOfficeSessionExpiry');
            const currentUser = localStorage.getItem('currentUser');

            console.log('💾 جلسة محفوظة:', savedSession);
            console.log('🔒 تذكرني:', rememberMe);
            console.log('⏰ انتهاء الجلسة:', sessionExpiry);
            console.log('👤 المستخدم:', currentUser);

            if (savedSession === 'active' && rememberMe === 'true' && sessionExpiry && currentUser) {
                const now = new Date().getTime();
                const expiryTime = parseInt(sessionExpiry);

                if (now < expiryTime) {
                    console.log('✅ جلسة صالحة - تسجيل دخول تلقائي');

                    // إخفاء شاشة تسجيل الدخول وإظهار الصفحة الرئيسية
                    const loginScreen = document.getElementById('loginScreen');
                    const mainContent = document.getElementById('mainContent');

                    if (loginScreen) loginScreen.style.display = 'none';
                    if (mainContent) mainContent.style.display = 'block';

                    // تحديث اسم المستخدم في الهيدر
                    updateHeaderUserName();

                    return true;
                } else {
                    console.log('⏰ انتهت صلاحية الجلسة');
                    clearSavedSession();
                }
            }

            console.log('❌ لا توجد جلسة صالحة - عرض شاشة تسجيل الدخول');
            return false;
        }

        // متغيرات للإعدادات (سيتم تحديثها من البيانات الموجودة)

        // نظام إدارة أنواع المستخدمين
        let currentUserType = localStorage.getItem('userType') || null;
        let userPermissions = {};

        // تعريف الصلاحيات لكل نوع
        const permissionSets = {
            admin: {
                name: 'فرع A - الإدارة العامة',
                icon: '👑',
                color: 'branch-admin',
                permissions: {
                    search: true,
                    publish: true,
                    delete: true,
                    displays: true,
                    settings: true,
                    users: true,
                    fullscreen: true,
                    managePermissions: true
                }
            },
            sales: {
                name: 'فرع B - المبيعات',
                icon: '🏢',
                color: 'branch-sales',
                permissions: {
                    search: true,
                    publish: true,
                    delete: false,
                    displays: false,
                    settings: false,
                    users: false,
                    fullscreen: false,
                    managePermissions: false
                }
            },
            quality: {
                name: 'فرع C - الجودة',
                icon: '🎯',
                color: 'branch-quality',
                permissions: {
                    search: true,
                    publish: true,
                    delete: true,
                    displays: true,
                    settings: true,
                    users: false,
                    fullscreen: false,
                    managePermissions: false
                }
            },
            display: {
                name: 'فرع D - العرض',
                icon: '📺',
                color: 'branch-display',
                permissions: {
                    search: false,
                    publish: false,
                    delete: false,
                    displays: false,
                    settings: false,
                    users: false,
                    fullscreen: true,
                    managePermissions: false
                }
            }
        };



        // تطبيق واجهة المستخدم حسب النوع
        function applyUserInterface() {
            const userConfig = permissionSets[currentUserType];

            // تحديث عنوان النظام
            const systemTitle = document.getElementById('systemTitle');
            if (systemTitle) {
                systemTitle.textContent = userConfig.name;
            }

            // إخفاء/إظهار الكروت حسب الصلاحيات
            hideUnauthorizedCards();

            // تطبيق التخصيصات حسب النوع
            if (currentUserType === 'display') {
                applyDisplayMode();
            }
        }

        // إخفاء الكروت غير المسموحة
        function hideUnauthorizedCards() {
            const cards = document.querySelectorAll('.home-card');

            cards.forEach(card => {
                const cardType = card.getAttribute('data-type');

                if (cardType === 'search' && !userPermissions.search) {
                    card.style.display = 'none';
                } else if (cardType === 'publish' && !userPermissions.publish) {
                    card.style.display = 'none';
                } else if (cardType === 'delete' && !userPermissions.delete) {
                    card.style.display = 'none';
                } else if (cardType === 'displays' && !userPermissions.displays) {
                    card.style.display = 'none';
                }
            });

            // إخفاء كرت الإعدادات إذا لم تكن مسموحة
            if (!userPermissions.settings) {
                const settingsCard = document.querySelector('.home-card[onclick*="الإعدادات"]');
                if (settingsCard) settingsCard.style.display = 'none';

                const settingsButtons = document.querySelectorAll('[onclick*="showSettingsWindow"]');
                settingsButtons.forEach(btn => btn.style.display = 'none');
            }

            // إخفاء زر العرض الكامل
            if (!userPermissions.fullscreen) {
                const fullscreenBtn = document.querySelector('.fullscreen-btn');
                if (fullscreenBtn) fullscreenBtn.style.display = 'none';
            }


        }




        // تطبيق وضع العرض للتلفزيونات
        function applyDisplayMode() {
            // إخفاء جميع الكروت عدا العرض
            const cardsGrid = document.querySelector('.cards-grid');
            cardsGrid.innerHTML = `
                <div class="home-card gray-border" style="grid-column: 1 / -1; min-height: 400px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                    <div style="font-size: 80px; margin-bottom: 20px;">📺</div>
                    <h2 style="color: #4b5563; margin-bottom: 15px;">شاشة العرض - فرع D</h2>
                    <p style="color: #718096; text-align: center; margin-bottom: 30px;">
                        في انتظار محتوى جديد للعرض...<br>
                        سيتم عرض المحتوى تلقائياً عند النشر من الفروع الأخرى
                    </p>
                    <button onclick="toggleFullscreen()" class="btn" style="background: #3b82f6; color: white; padding: 15px 30px; border-radius: 8px; border: none; cursor: pointer; font-size: 16px;">
                        🖥️ العرض الكامل
                    </button>
                </div>
            `;

            // مراقبة المحتوى الجديد
            monitorContentForDisplay();
        }

        // مراقبة المحتوى للعرض
        function monitorContentForDisplay() {
            setInterval(() => {
                const sharedContent = localStorage.getItem('sharedContent');
                if (sharedContent) {
                    const content = JSON.parse(sharedContent);
                    displayContentOnScreen(content);
                }
            }, 1000);
        }

        // عرض المحتوى على الشاشة
        function displayContentOnScreen(content) {
            const cardsGrid = document.querySelector('.cards-grid');
            cardsGrid.innerHTML = `
                <div class="home-card gray-border" style="grid-column: 1 / -1; min-height: 500px; display: flex; flex-direction: column; align-items: center; justify-content: center; position: relative;">
                    <div style="position: absolute; top: 20px; right: 20px; background: #10b981; color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: bold; animation: bounce 1s ease-in-out 3;">
                        محتوى جديد!
                    </div>
                    <div style="font-size: 120px; margin-bottom: 20px;">${content.icon || '📖'}</div>
                    <h2 style="color: #2d3748; margin-bottom: 15px; text-align: center; font-size: 28px;">${content.title}</h2>
                    <p style="color: #718096; text-align: center; margin-bottom: 10px; font-size: 18px;">
                        بواسطة: ${content.author}
                    </p>
                    <p style="color: #a0aec0; text-align: center; margin-bottom: 30px;">
                        من: فرع ${content.branch} • ${content.date}
                    </p>
                    <button onclick="toggleFullscreen()" class="btn" style="background: #3b82f6; color: white; padding: 15px 30px; border-radius: 8px; border: none; cursor: pointer; font-size: 16px;">
                        🖥️ العرض الكامل
                    </button>
                </div>
            `;
        }



        // تغيير نوع المستخدم
        function changeUserType() {
            localStorage.removeItem('userType');
            location.reload();
        }

        // إضافة خيار تغيير نوع المستخدم للإعدادات
        function addUserTypeOption() {
            const settingsContent = document.querySelector('#settingsModal .modal-content');
            if (settingsContent && currentUserType) {
                const userConfig = permissionSets[currentUserType];
                const userTypeSection = document.createElement('div');
                userTypeSection.innerHTML = `
                    <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 20px;">
                        <h3 style="color: #4b5563; margin-bottom: 15px;">نوع الفرع الحالي</h3>
                        <div style="display: flex; align-items: center; justify-content: space-between; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span style="font-size: 24px;">${userConfig.icon}</span>
                                <span style="font-weight: 600; color: #2d3748;">${userConfig.name}</span>
                            </div>
                            <button onclick="changeUserType()" style="background: #6366f1; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                تغيير النوع
                            </button>
                        </div>
                    </div>
                `;
                settingsContent.appendChild(userTypeSection);
            }
        }

        // تحقق من نوع المستخدم عند التحميل
        if (currentUserType) {
            selectUserType(currentUserType);
        }

        // إضافة مراقب لتحديث النشر
        function updatePublishFunction() {
            const originalPublishFile = window.publishFile;
            window.publishFile = function() {
                if (!userPermissions.publish) {
                    alert('🚫 ليس لديك صلاحية للنشر!\n\nنوع الفرع الحالي لا يسمح بالنشر. يرجى التواصل مع الإدارة.');
                    return;
                }
                return originalPublishFile.apply(this, arguments);
            };
        }

        // دوال تسجيل الدخول
        function handleLogin(event) {
            event.preventDefault();
            console.log('🚀 بدء عملية تسجيل الدخول...');

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const branch = document.getElementById('branchType').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // تحقق سريع من البيانات
            console.log('🔍 بيانات تسجيل الدخول:', { username, password, branch, rememberMe });

            if (!username || !password || !branch) {
                alert('❌ ' + getTranslation('fillAllFields'));
                console.log('❌ حقول فارغة');
                return;
            }

            console.log('✅ جميع الحقول مملوءة، جاري التحقق...');

            try {
                // التأكد من وجود البيانات قبل التحقق
                const usersCheck = localStorage.getItem('systemUsers');
                if (!usersCheck) {
                    console.log('⚠️ لا توجد بيانات مستخدمين، جاري إعادة التهيئة...');
                    authSystem.initializeUsers();
                }

                const result = authSystem.validateLogin(username, password, branch);
                console.log('🔍 نتيجة التحقق:', result);

                if (result.success) {
                    console.log('✅ تسجيل الدخول ناجح');

                    // التحقق من ضرورة تغيير كلمة المرور
                    if (result.user.mustChangePassword) {
                        const newPassword = prompt(`مرحباً ${result.user.firstName} ${result.user.lastName}!\n\nهذا أول تسجيل دخول لك في النظام.\nيجب تغيير كلمة المرور المؤقتة.\n\nأدخل كلمة المرور الجديدة (6 أحرف على الأقل):`);

                        if (!newPassword || newPassword.length < 6) {
                            alert('❌ يجب إدخال كلمة مرور جديدة (6 أحرف على الأقل)');
                            return;
                        }

                        const confirmPassword = prompt('تأكيد كلمة المرور الجديدة:');
                        if (newPassword !== confirmPassword) {
                            alert('❌ كلمات المرور غير متطابقة');
                            return;
                        }

                        // تحديث كلمة المرور
                        const users = JSON.parse(localStorage.getItem('systemUsers'));
                        users[username].password = newPassword;
                        users[username].mustChangePassword = false;
                        users[username].passwordChangedDate = new Date().toISOString();
                        localStorage.setItem('systemUsers', JSON.stringify(users));

                        alert('✅ تم تغيير كلمة المرور بنجاح!\nيمكنك الآن استخدام كلمة المرور الجديدة.');
                    }

                    authSystem.loginUser(username, branch, rememberMe);

                    // رسالة ترحيب
                    setTimeout(() => {
                        const fullName = `${result.user.firstName} ${result.user.lastName}`;
                        const welcomeMsg = `${getTranslation('welcome')} ${fullName}!\n\n${getTranslation('loginSuccess')} ${getBranchName(branch)}`;
                        alert(welcomeMsg);
                    }, 500);
                } else {
                    console.log('❌ فشل تسجيل الدخول:', result.message);
                    alert(`❌ فشل تسجيل الدخول\n\n${result.message}`);
                }
            } catch (error) {
                console.error('❌ خطأ في تسجيل الدخول:', error);
                alert('❌ حدث خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى.');
            }
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password svg');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.innerHTML = `
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                `;
            } else {
                passwordInput.type = 'password';
                toggleBtn.innerHTML = `
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                `;
            }
        }

        function showPasswordReset() {
            console.log('showPasswordReset called'); // للتشخيص
            const modal = document.getElementById('passwordResetModal');
            if (modal) {
                modal.style.display = 'flex';
                console.log('Modal displayed'); // للتشخيص
            } else {
                console.error('passwordResetModal not found'); // للتشخيص
            }
        }

        function closePasswordReset() {
            const modal = document.getElementById('passwordResetModal');
            if (modal) {
                modal.style.display = 'none';
                document.getElementById('resetUsername').value = '';
                document.getElementById('resetBranch').value = '';
            }
        }

        async function submitPasswordReset() {
            const email = document.getElementById('resetUsername').value;
            const branch = document.getElementById('resetBranch').value;

            if (!email || !branch) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            // التحقق من صحة الإيميل
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('❌ يرجى إدخال بريد إلكتروني صحيح');
                return;
            }

            // التحقق من وجود المستخدم
            const users = JSON.parse(localStorage.getItem('systemUsers'));
            const user = users[email];

            if (!user) {
                alert('❌ البريد الإلكتروني غير مسجل في النظام');
                return;
            }

            if (user.branch !== branch) {
                alert('❌ نوع الفرع غير صحيح لهذا المستخدم');
                return;
            }

            // إنشاء رمز إعادة التعيين
            const resetCode = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            const requestId = authSystem.submitPasswordResetRequest(email, branch, resetCode);

            // إرسال إيميل حقيقي
            const userName = `${user.firstName} ${user.lastName}`;

            try {
                // عرض رسالة التحميل
                const loadingAlert = alert('⏳ جاري إرسال الإيميل...');

                const emailResult = await emailService.sendPasswordResetEmail(email, resetCode, userName);

                closePasswordReset();

                if (emailResult.success) {
                    alert(`✅ تم إرسال طلب إعادة تعيين كلمة المرور بنجاح!\n\n📧 تم إرسال إيميل إلى: ${email}\n🔢 رقم الطلب: ${requestId}\n\n⚠️ يرجى فحص صندوق الوارد (وصندوق الرسائل المزعجة) للحصول على رابط إعادة التعيين.\n\nإذا لم تستلم الإيميل خلال 5 دقائق، يرجى التواصل مع الإدارة.`);
                } else {
                    // في حالة فشل الإرسال، نعرض رقم الطلب للمراجعة اليدوية
                    alert(`⚠️ تم إنشاء طلب إعادة تعيين كلمة المرور\n\n🔢 رقم الطلب: ${requestId}\n\n❌ فشل في إرسال الإيميل تلقائياً\nيرجى التواصل مع الإدارة مع ذكر رقم الطلب\n\n📧 للتواصل: <EMAIL>`);
                }
            } catch (error) {
                closePasswordReset();
                alert(`❌ حدث خطأ في إرسال الإيميل\n\n🔢 رقم الطلب: ${requestId}\n\nيرجى التواصل مع الإدارة مع ذكر رقم الطلب\n\n📧 للتواصل: <EMAIL>`);
            }
        }

        function getBranchName(branch) {
            const names = {
                'A': 'الفرع الرئيسي - الإدارة العامة',
                'B': 'فرع المبيعات',
                'C': 'فرع الجودة',
                'D': 'فرع العرض'
            };
            return names[branch] || 'فرع غير معروف';
        }

        // دوال النوافذ المنبثقة الجديدة
        function showRegister() {
            document.getElementById('registerModal').style.display = 'flex';
        }

        function closeRegister() {
            document.getElementById('registerModal').style.display = 'none';
            document.querySelector('.register-form').reset();
        }

        function showProfile() {
            const currentUserEmail = localStorage.getItem('currentUser');
            const users = JSON.parse(localStorage.getItem('systemUsers'));

            console.log('🔍 فتح البروفايل للمستخدم:', currentUserEmail);

            if (currentUserEmail && users && users[currentUserEmail]) {
                const currentUser = users[currentUserEmail];
                console.log('👤 بيانات المستخدم الحالي:', currentUser);

                // حذف النافذة القديمة إذا كانت موجودة
                const oldModal = document.getElementById('profileModal');
                if (oldModal) {
                    oldModal.remove();
                }

                // إنشاء نافذة البروفايل الجديدة
                createProfileModal();

                // تعبئة بيانات البروفايل
                setTimeout(() => {
                    try {
                        document.getElementById('profileFirstName').value = currentUser.firstName || '';
                        document.getElementById('profileLastName').value = currentUser.lastName || '';
                        document.getElementById('profileEmail').value = currentUser.email || '';
                        document.getElementById('profilePosition').value = currentUser.position || '';
                        document.getElementById('profileDepartment').value = currentUser.department || '';
                        document.getElementById('profilePhone').value = currentUser.phone || '';
                        document.getElementById('profileBranch').value = getBranchName(currentUser.branch);
                        document.getElementById('profileJoinDate').value = currentUser.joinDate || '';

                        // إظهار النافذة
                        document.getElementById('profileModal').style.display = 'flex';
                        console.log('✅ تم فتح نافذة البروفايل بنجاح');

                        // تحديث اسم المستخدم في الهيدر
                        updateHeaderUserName();
                    } catch (error) {
                        console.error('❌ خطأ في تعبئة بيانات البروفايل:', error);
                        alert('❌ حدث خطأ في تحميل البروفايل');
                    }
                }, 200);
            } else {
                console.log('❌ لا يمكن تحميل بيانات البروفايل');
                alert('❌ لا يمكن تحميل بيانات البروفايل\nتأكد من تسجيل الدخول أولاً');
            }
        }

        // إنشاء نافذة البروفايل بتصميم مشابه لنافذة تسجيل الدخول
        function createProfileModal() {
            const modalHTML = `
                <div id="profileModal" class="modal" style="display: none;">
                    <div class="login-container" style="max-width: 500px; margin: 50px auto; padding: 0;">
                        <!-- الخلفية المتحركة -->
                        <div class="animated-bg">
                            <div class="shape shape-1"></div>
                            <div class="shape shape-2"></div>
                            <div class="shape shape-3"></div>
                        </div>

                        <!-- محتوى النافذة -->
                        <div class="login-content" style="position: relative; z-index: 10;">
                            <!-- الشعار والعنوان -->
                            <div class="login-header" style="text-align: center; margin-bottom: 30px;">
                                <div class="logo-container" style="margin-bottom: 20px;">
                                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin: 0 auto; box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);">
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="white">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </div>
                                <h2 style="color: #2d3748; font-size: 28px; font-weight: 700; margin: 0 0 8px 0;">البروفايل الشخصي</h2>
                                <p style="color: #718096; font-size: 16px; margin: 0;">معلوماتك الشخصية في النظام</p>
                                <button onclick="closeProfile()" style="position: absolute; top: 20px; left: 20px; background: rgba(255,255,255,0.9); border: none; width: 40px; height: 40px; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 18px; color: #666; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">✕</button>
                            </div>

                            <!-- نموذج البروفايل -->
                            <div class="login-form-container">
                                <form class="login-form" onsubmit="updateProfile(event)" style="gap: 20px;">
                                    <!-- الاسم الكامل -->
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <div class="input-group">
                                            <label for="profileFirstName">الاسم الأول</label>
                                            <div class="input-wrapper">
                                                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                    <circle cx="12" cy="7" r="4"/>
                                                </svg>
                                                <input type="text" id="profileFirstName" readonly style="background: #f7fafc;">
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <label for="profileLastName">الاسم الأخير</label>
                                            <div class="input-wrapper">
                                                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                    <circle cx="12" cy="7" r="4"/>
                                                </svg>
                                                <input type="text" id="profileLastName" readonly style="background: #f7fafc;">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- البريد الإلكتروني -->
                                    <div class="input-group">
                                        <label for="profileEmail">البريد الإلكتروني</label>
                                        <div class="input-wrapper">
                                            <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                                <polyline points="22,6 12,13 2,6"/>
                                            </svg>
                                            <input type="email" id="profileEmail" readonly style="background: #f7fafc;">
                                        </div>
                                    </div>

                                    <!-- المنصب والقسم -->
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <div class="input-group">
                                            <label for="profilePosition">المنصب</label>
                                            <div class="input-wrapper">
                                                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                                    <line x1="8" y1="21" x2="16" y2="21"/>
                                                    <line x1="12" y1="17" x2="12" y2="21"/>
                                                </svg>
                                                <input type="text" id="profilePosition" readonly style="background: #f7fafc;">
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <label for="profileDepartment">القسم</label>
                                            <div class="input-wrapper">
                                                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                                </svg>
                                                <input type="text" id="profileDepartment" readonly style="background: #f7fafc;">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- رقم الهاتف ونوع الفرع -->
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <div class="input-group">
                                            <label for="profilePhone">رقم الهاتف</label>
                                            <div class="input-wrapper">
                                                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                                </svg>
                                                <input type="tel" id="profilePhone" placeholder="يمكنك تعديل رقم الهاتف">
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <label for="profileBranch">نوع الفرع</label>
                                            <div class="input-wrapper">
                                                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                                </svg>
                                                <input type="text" id="profileBranch" readonly style="background: #f7fafc;">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- تاريخ الانضمام -->
                                    <div class="input-group">
                                        <label for="profileJoinDate">تاريخ الانضمام</label>
                                        <div class="input-wrapper">
                                            <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                                                <line x1="16" y1="2" x2="16" y2="6"/>
                                                <line x1="8" y1="2" x2="8" y2="6"/>
                                                <line x1="3" y1="10" x2="21" y2="10"/>
                                            </svg>
                                            <input type="text" id="profileJoinDate" readonly style="background: #f7fafc;">
                                        </div>
                                    </div>

                                    <!-- ملاحظة -->
                                    <div style="background: linear-gradient(135deg, #e0f2fe, #f0f9ff); border: 1px solid #0ea5e9; border-radius: 12px; padding: 16px; margin: 10px 0;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <svg style="width: 20px; height: 20px; color: #0ea5e9;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <circle cx="12" cy="12" r="10"/>
                                                <path d="M12 16v-4"/>
                                                <path d="M12 8h.01"/>
                                            </svg>
                                            <p style="margin: 0; font-size: 14px; color: #0c4a6e; font-weight: 500;">
                                                <strong>ملاحظة:</strong> يمكنك تعديل رقم الهاتف فقط. باقي المعلومات يتم تحديثها من قبل الفرع الرئيسي.
                                            </p>
                                        </div>
                                    </div>

                                    <!-- أزرار العمليات -->
                                    <div style="display: flex; gap: 12px; justify-content: center; margin-top: 20px;">
                                        <button type="button" onclick="showChangePasswordModal()" class="secondary-btn" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; border: none; padding: 12px 24px; border-radius: 10px; font-size: 14px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;">
                                            <svg style="width: 16px; height: 16px; margin-left: 8px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                                <circle cx="12" cy="16" r="1"/>
                                                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                                            </svg>
                                            تغيير كلمة المرور
                                        </button>
                                        <button type="submit" class="login-btn" style="background: linear-gradient(135deg, #10b981, #059669); padding: 12px 24px;">
                                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                                                <polyline points="17,21 17,13 7,13 7,21"/>
                                                <polyline points="7,3 7,8 15,8"/>
                                            </svg>
                                            حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closeProfile() {
            const modal = document.getElementById('profileModal');
            if (modal) {
                modal.style.display = 'none';
                // حذف النافذة من DOM لتجنب التداخل
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // تحديث بيانات البروفايل
        function updateProfile(event) {
            event.preventDefault();

            const currentUserEmail = localStorage.getItem('currentUser');
            const users = JSON.parse(localStorage.getItem('systemUsers'));

            if (currentUserEmail && users && users[currentUserEmail]) {
                // تحديث رقم الهاتف فقط (الحقل الوحيد القابل للتعديل)
                const newPhone = document.getElementById('profilePhone').value;

                if (newPhone.trim() === '') {
                    alert('❌ يرجى إدخال رقم هاتف صحيح');
                    return;
                }

                users[currentUserEmail].phone = newPhone;
                users[currentUserEmail].lastUpdated = new Date().toISOString();

                // حفظ التغييرات
                localStorage.setItem('systemUsers', JSON.stringify(users));

                console.log('✅ تم تحديث رقم الهاتف:', newPhone);
                alert('✅ تم حفظ التغييرات بنجاح!');
                closeProfile();
            } else {
                alert('❌ حدث خطأ في حفظ التغييرات');
            }
        }

        // إظهار نافذة تغيير كلمة المرور
        function showChangePasswordModal() {
            const modalHTML = `
                <div id="changePasswordModal" class="modal" style="display: flex;">
                    <div class="modal-content" style="max-width: 400px; background: rgba(255, 255, 255, 0.98); backdrop-filter: blur(20px); border-radius: 20px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15); padding: 25px;">
                        <div class="modal-header" style="text-align: center; margin-bottom: 20px;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                    <circle cx="12" cy="16" r="1"/>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                                </svg>
                            </div>
                            <h3 style="color: #333; margin: 0 0 5px 0; font-size: 18px;">تغيير كلمة المرور</h3>
                            <p style="color: #666; font-size: 14px; margin: 0;">أدخل كلمة المرور الجديدة</p>
                            <button onclick="closeChangePasswordModal()" class="close-btn" style="position: absolute; top: 15px; left: 15px; background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">✕</button>
                        </div>

                        <form onsubmit="changePassword(event)" style="display: grid; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151; font-size: 14px;">كلمة المرور الحالية</label>
                                <input type="password" id="currentPassword" required style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; font-family: 'Cairo', sans-serif;">
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151; font-size: 14px;">كلمة المرور الجديدة</label>
                                <input type="password" id="newPassword" required minlength="6" style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; font-family: 'Cairo', sans-serif;">
                                <small style="color: #6b7280; font-size: 12px;">6 أحرف على الأقل</small>
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151; font-size: 14px;">تأكيد كلمة المرور الجديدة</label>
                                <input type="password" id="confirmNewPassword" required minlength="6" style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; font-family: 'Cairo', sans-serif;">
                            </div>

                            <div style="display: flex; gap: 10px; justify-content: flex-end; padding-top: 15px; border-top: 1px solid #e5e7eb;">
                                <button type="button" onclick="closeChangePasswordModal()" style="padding: 10px 20px; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-family: 'Cairo', sans-serif;">إلغاء</button>
                                <button type="submit" style="padding: 10px 20px; background: #f59e0b; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600; font-family: 'Cairo', sans-serif;">تغيير كلمة المرور</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closeChangePasswordModal() {
            const modal = document.getElementById('changePasswordModal');
            if (modal) {
                modal.remove();
            }
        }

        // تغيير كلمة المرور
        function changePassword(event) {
            event.preventDefault();

            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmNewPassword').value;

            if (newPassword !== confirmPassword) {
                alert('❌ كلمات المرور الجديدة غير متطابقة');
                return;
            }

            const currentUserEmail = localStorage.getItem('currentUser');
            const users = JSON.parse(localStorage.getItem('systemUsers'));

            if (currentUserEmail && users && users[currentUserEmail]) {
                if (users[currentUserEmail].password !== currentPassword) {
                    alert('❌ كلمة المرور الحالية غير صحيحة');
                    return;
                }

                // تحديث كلمة المرور
                users[currentUserEmail].password = newPassword;
                users[currentUserEmail].passwordChangedDate = new Date().toISOString();

                // حفظ التغييرات
                localStorage.setItem('systemUsers', JSON.stringify(users));

                alert('✅ تم تغيير كلمة المرور بنجاح!');
                closeChangePasswordModal();
            } else {
                alert('❌ حدث خطأ في تغيير كلمة المرور');
            }
        }

        function updateHeaderUserName() {
            const currentUserEmail = localStorage.getItem('currentUser');
            const users = JSON.parse(localStorage.getItem('systemUsers'));

            if (currentUserEmail && users && users[currentUserEmail]) {
                const currentUser = users[currentUserEmail];
                const userNameElement = document.querySelector('.user-info span');
                if (userNameElement) {
                    userNameElement.textContent = `${currentUser.firstName} ${currentUser.lastName}`;
                }
            }
        }

        function handleRegister(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const userData = {
                email: formData.get('email'),
                password: formData.get('password'),
                confirmPassword: formData.get('confirmPassword'),
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                position: formData.get('position'),
                department: formData.get('department'),
                phone: formData.get('phone'),
                branchType: formData.get('branchType'),
                agreeTerms: formData.get('agreeTerms')
            };

            // التحقق من صحة البيانات
            if (userData.password !== userData.confirmPassword) {
                alert('❌ كلمات المرور غير متطابقة');
                return;
            }

            if (userData.password.length < 6) {
                alert('❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            if (!userData.agreeTerms) {
                alert('❌ يجب الموافقة على شروط الاستخدام');
                return;
            }

            // التحقق من عدم وجود الإيميل مسبقاً
            const users = JSON.parse(localStorage.getItem('systemUsers'));
            if (users[userData.email]) {
                alert('❌ هذا البريد الإلكتروني مسجل مسبقاً');
                return;
            }

            // إنشاء المستخدم الجديد
            const newUser = {
                email: userData.email,
                password: userData.password,
                firstName: userData.firstName,
                lastName: userData.lastName,
                position: userData.position,
                department: userData.department,
                phone: userData.phone,
                role: userData.branchType === 'A' ? 'admin' : 'user',
                branch: userData.branchType,
                joinDate: new Date().toLocaleDateString('ar-SA'),
                avatar: null,
                isActive: userData.branchType === 'A' ? true : false // المدير يحتاج موافقة للمستخدمين الجدد
            };

            users[userData.email] = newUser;
            localStorage.setItem('systemUsers', JSON.stringify(users));

            if (userData.branchType === 'A') {
                alert('✅ تم إنشاء الحساب بنجاح!\nيمكنك تسجيل الدخول الآن.');
            } else {
                alert('✅ تم إرسال طلب إنشاء الحساب!\nسيتم تفعيل الحساب بعد موافقة الإدارة.');
            }

            closeRegister();
        }

        // تحديث دالة تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                // حذف جميع بيانات الجلسة
                clearSavedSession();

                // استدعاء دالة تسجيل الخروج في النظام
                authSystem.logout();

                console.log('👋 تم تسجيل الخروج وحذف الجلسة المحفوظة');
            }
        }

        // إدارة المستخدمين (للفرع الرئيسي فقط)
        function showUserManagement() {
            if (currentUserType !== 'admin') {
                alert('🚫 هذه الخاصية متاحة للفرع الرئيسي فقط');
                return;
            }

            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            const users = JSON.parse(localStorage.getItem('systemUsers'));

            modalContent.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h2 style="color: #4b5563; font-size: 24px; font-weight: bold;">👥 إدارة المستخدمين</h2>
                    <button onclick="closeModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">✕</button>
                </div>

                <div style="margin-bottom: 30px;">
                    <button onclick="showAddUserForm()" class="btn btn-primary" style="margin-bottom: 20px;">
                        ➕ إضافة مستخدم جديد
                    </button>
                </div>

                <div style="background: #f8f9fa; border-radius: 12px; padding: 20px;">
                    <h3 style="color: #374151; margin-bottom: 20px;">المستخدمين الحاليين</h3>
                    <div id="usersList">
                        ${Object.entries(users).map(([username, user]) => `
                            <div style="background: white; border-radius: 8px; padding: 20px; margin-bottom: 15px; border: 1px solid #e5e7eb;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <h4 style="color: #2d3748; margin-bottom: 5px;">${user.name}</h4>
                                        <p style="color: #718096; font-size: 14px; margin: 0;">
                                            اسم المستخدم: ${username} | الفرع: ${user.branch} | الدور: ${user.role}
                                        </p>
                                    </div>
                                    <div style="display: flex; gap: 10px;">
                                        <button onclick="resetUserPassword('${username}')" class="btn btn-warning" style="padding: 8px 15px; font-size: 14px;">
                                            🔑 إعادة تعيين كلمة المرور
                                        </button>
                                        ${username !== 'admin' ? `
                                            <button onclick="deleteUser('${username}')" class="btn btn-danger" style="padding: 8px 15px; font-size: 14px;">
                                                🗑️ حذف
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
        }

        // عرض طلبات إعادة تعيين كلمة المرور
        function showPasswordResetRequests() {
            if (currentUserType !== 'admin') {
                alert('🚫 هذه الخاصية متاحة للفرع الرئيسي فقط');
                return;
            }

            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            const requests = authSystem.getPasswordResetRequests();
            const pendingRequests = requests.filter(r => r.status === 'pending');

            modalContent.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h2 style="color: #4b5563; font-size: 24px; font-weight: bold;">🔑 طلبات إعادة تعيين كلمة المرور</h2>
                    <button onclick="closeModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">✕</button>
                </div>

                ${pendingRequests.length === 0 ? `
                    <div style="text-align: center; padding: 40px; color: #718096;">
                        <div style="font-size: 48px; margin-bottom: 15px;">📭</div>
                        <h3>لا توجد طلبات معلقة</h3>
                        <p>جميع طلبات إعادة تعيين كلمة المرور تم التعامل معها</p>
                    </div>
                ` : `
                    <div style="background: #f8f9fa; border-radius: 12px; padding: 20px;">
                        <h3 style="color: #374151; margin-bottom: 20px;">الطلبات المعلقة (${pendingRequests.length})</h3>
                        ${pendingRequests.map(request => `
                            <div style="background: white; border-radius: 8px; padding: 20px; margin-bottom: 15px; border: 1px solid #e5e7eb;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <div>
                                        <h4 style="color: #2d3748; margin-bottom: 5px;">طلب رقم: ${request.id}</h4>
                                        <p style="color: #718096; font-size: 14px; margin: 0;">
                                            المستخدم: ${request.username} | الفرع: ${request.branch} |
                                            التاريخ: ${new Date(request.timestamp).toLocaleDateString('ar-SA')}
                                        </p>
                                    </div>
                                    <span style="background: #fef3c7; color: #92400e; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                                        معلق
                                    </span>
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <button onclick="approvePasswordReset(${request.id})" class="btn btn-success" style="padding: 8px 15px; font-size: 14px;">
                                        ✅ موافقة
                                    </button>
                                    <button onclick="rejectPasswordReset(${request.id})" class="btn btn-danger" style="padding: 8px 15px; font-size: 14px;">
                                        ❌ رفض
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `}

                <div style="margin-top: 30px; padding: 20px; background: #e0f2fe; border-radius: 8px;">
                    <h4 style="color: #0369a1; margin-bottom: 10px;">📋 إحصائيات الطلبات</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; text-align: center;">
                        <div>
                            <div style="font-size: 24px; font-weight: bold; color: #0369a1;">${requests.filter(r => r.status === 'pending').length}</div>
                            <div style="font-size: 12px; color: #0369a1;">معلقة</div>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: bold; color: #059669;">${requests.filter(r => r.status === 'approved').length}</div>
                            <div style="font-size: 12px; color: #059669;">موافق عليها</div>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: bold; color: #dc2626;">${requests.filter(r => r.status === 'rejected').length}</div>
                            <div style="font-size: 12px; color: #dc2626;">مرفوضة</div>
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
        }

        // الموافقة على طلب إعادة تعيين كلمة المرور
        function approvePasswordReset(requestId) {
            const newPassword = prompt('أدخل كلمة المرور الجديدة:');
            if (newPassword && newPassword.length >= 6) {
                authSystem.approvePasswordReset(requestId, newPassword);
                alert(`✅ تم الموافقة على الطلب وتعيين كلمة المرور الجديدة: ${newPassword}`);
                showPasswordResetRequests(); // إعادة تحميل القائمة
            } else {
                alert('يرجى إدخال كلمة مرور صحيحة (6 أحرف على الأقل)');
            }
        }

        // رفض طلب إعادة تعيين كلمة المرور
        function rejectPasswordReset(requestId) {
            const reason = prompt('أدخل سبب الرفض (اختياري):') || 'لم يتم تحديد السبب';
            authSystem.rejectPasswordReset(requestId, reason);
            alert(`❌ تم رفض الطلب\nالسبب: ${reason}`);
            showPasswordResetRequests(); // إعادة تحميل القائمة
        }

        // إعادة تعيين كلمة مرور مستخدم
        function resetUserPassword(username) {
            const newPassword = prompt(`أدخل كلمة المرور الجديدة للمستخدم: ${username}`);
            if (newPassword && newPassword.length >= 6) {
                authSystem.updatePassword(username, newPassword);
                alert(`✅ تم تعيين كلمة المرور الجديدة للمستخدم ${username}: ${newPassword}`);
            } else {
                alert('يرجى إدخال كلمة مرور صحيحة (6 أحرف على الأقل)');
            }
        }

        // حذف مستخدم
        function deleteUser(username) {
            if (confirm(`هل تريد حذف المستخدم: ${username}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                delete users[username];
                localStorage.setItem('systemUsers', JSON.stringify(users));
                alert(`✅ تم حذف المستخدم: ${username}`);
                showUserManagement(); // إعادة تحميل القائمة
            }
        }



        // إجبار إظهار شاشة تسجيل الدخول عند التحميل
        window.addEventListener('load', function() {
            // مسح جميع بيانات تسجيل الدخول المحفوظة
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('userType');
            localStorage.removeItem('loginTime');
            localStorage.removeItem('rememberMe');
            localStorage.removeItem('loginExpiry');

            // مسح بيانات المستخدمين القديمة لإجبار إعادة التحميل
            localStorage.removeItem('systemUsers');

            // إظهار شاشة تسجيل الدخول وإخفاء باقي المحتوى
            document.getElementById('loginScreen').style.display = 'flex';
            document.getElementById('mainHeader').style.display = 'none';

            const mainContent = document.querySelector('.container');
            if (mainContent) {
                mainContent.style.display = 'none';
            }
        });

        // تطبيق التحديثات عند التحميل
        setTimeout(() => {
            if (currentUserType) {
                updatePublishFunction();
            }
        }, 1000);

        // دالة العرض الكامل للتلفاز
        let isFullscreen = false;

        function toggleFullscreen() {
            const body = document.body;
            const fullscreenBtn = document.querySelector('.fullscreen-btn');

            if (!isFullscreen) {
                // تفعيل العرض الكامل
                body.classList.add('fullscreen-mode');
                fullscreenBtn.innerHTML = `
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"/>
                    </svg>
                    خروج من العرض الكامل
                `;

                // طلب العرض الكامل من المتصفح
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }

                isFullscreen = true;

                // إخفاء مؤشر الماوس بعد 3 ثوان
                let mouseTimer;
                const hideMouseCursor = () => {
                    body.style.cursor = 'none';
                };

                const showMouseCursor = () => {
                    body.style.cursor = 'default';
                    clearTimeout(mouseTimer);
                    mouseTimer = setTimeout(hideMouseCursor, 3000);
                };

                document.addEventListener('mousemove', showMouseCursor);
                mouseTimer = setTimeout(hideMouseCursor, 3000);

            } else {
                // إلغاء العرض الكامل
                body.classList.remove('fullscreen-mode');
                body.style.cursor = 'default';
                fullscreenBtn.innerHTML = `
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                    </svg>
                    عرض كامل
                `;

                // إلغاء العرض الكامل من المتصفح
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }

                isFullscreen = false;
            }
        }

        // التعامل مع تغيير حالة العرض الكامل من المتصفح
        document.addEventListener('fullscreenchange', function() {
            if (!document.fullscreenElement && isFullscreen) {
                toggleFullscreen();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // F11 للعرض الكامل
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }

            // ESC للخروج من العرض الكامل
            if (e.key === 'Escape' && isFullscreen) {
                toggleFullscreen();
            }
        });
        let passwordRequests = [];
        let createdPasswords = [];

        function showArchiveWindow() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            modalContent.innerHTML = `
                <div style="height: 85vh; background: white; border-radius: 20px; padding: 0; overflow: hidden;">
                    <!-- الشريط العلوي -->
                    <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <svg style="width: 24px; height: 24px; color: #4b5563;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
                            </svg>
                            <h2 style="color: #4b5563; font-size: 20px; font-weight: bold; margin: 0;">الحافظة</h2>
                        </div>
                        <button onclick="closeModal()" style="background: none; border: none; color: #4b5563; font-size: 24px; cursor: pointer; padding: 8px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='none'">✕</button>
                    </div>

                    <!-- محتوى النافذة -->
                    <div style="padding: 25px;">
                        <!-- شريط البحث -->
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 30px;">
                            <div style="flex: 1; position: relative;">
                                <input type="text" id="archiveSearch" placeholder="البحث في الملفات..."
                                       style="width: 100%; padding: 12px 16px; border: 2px solid #d1d5db; border-radius: 12px; font-size: 14px; outline: none;"
                                       onkeyup="handleArchiveSearch()" disabled>
                            </div>
                            <button onclick="performArchiveSearch()" style="background: none; border: none; cursor: pointer; padding: 12px;" disabled id="searchBtn">
                                <svg style="width: 24px; height: 24px; color: #3b82f6;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/>
                                    <path d="M21 21l-4.35-4.35"/>
                                </svg>
                            </button>
                        </div>

                        <!-- اختيار نوع الملفات - مقسم إلى قسمين -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 30px;">
                            <!-- القسم الأيمن - الملفات المرسلة -->
                            <div style="text-align: center;">
                                <div onclick="selectFileType('sent')" id="sentTab" style="cursor: pointer; display: inline-flex; align-items: center; gap: 8px; padding-bottom: 8px; border-bottom: 3px solid #d1d5db; color: #6b7280; transition: all 0.3s;">
                                    <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                        <polyline points="17,8 12,3 7,8"/>
                                        <line x1="12" y1="3" x2="12" y2="15"/>
                                    </svg>
                                    <span style="font-weight: 600;">الملفات المرسلة</span>
                                </div>
                            </div>

                            <!-- القسم الأيسر - الملفات المستلمة -->
                            <div style="text-align: center;">
                                <div onclick="selectFileType('received')" id="receivedTab" style="cursor: pointer; display: inline-flex; align-items: center; gap: 8px; padding-bottom: 8px; border-bottom: 3px solid #d1d5db; color: #6b7280; transition: all 0.3s;">
                                    <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                        <polyline points="7,10 12,15 17,10"/>
                                        <line x1="12" y1="15" x2="12" y2="3"/>
                                    </svg>
                                    <span style="font-weight: 600;">الملفات المستلمة</span>
                                </div>
                            </div>
                        </div>

                        <!-- رسالة التنبيه -->
                        <div id="selectTypeMessage" style="text-align: center; color: #6b7280; font-size: 16px; padding: 40px; background: #f9fafb; border-radius: 12px; border: 2px dashed #d1d5db;">
                            يرجى اختيار نوع الملفات أولاً (المستلمة أو المرسلة) لتتمكن من البحث
                        </div>

                        <!-- جدول النتائج -->
                        <div id="resultsTable" style="display: none;">
                            <div style="background: #f8f9fa; padding: 12px 16px; border-radius: 15px; border: 1px solid #d1d5db; display: grid; grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1fr 0.5fr; gap: 16px; font-weight: bold; color: #374151; font-size: 14px; margin-bottom: 8px;">
                                <div>اسم المستخدم</div>
                                <div>القسم</div>
                                <div>نوع الكتاب</div>
                                <div>التاريخ</div>
                                <div>الوقت</div>
                                <div></div>
                            </div>
                            <div id="tableBody" style="display: flex; flex-direction: column; gap: 8px;">
                                <!-- سيتم إدراج النتائج هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
        }

        function showSettingsWindow() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            modalContent.innerHTML = `
                <div style="height: 90vh; background: white; border-radius: 20px; padding: 0; overflow: hidden; display: flex; flex-direction: column;">
                    <!-- الشريط العلوي -->
                    <div style="background: #f8f9fa; padding: 12px 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <svg style="width: 24px; height: 24px; color: #4b5563;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                            <h2 style="color: #4b5563; font-size: 20px; font-weight: bold; margin: 0;">الإعدادات</h2>
                        </div>
                        <button onclick="closeModal()" style="background: none; border: none; color: #4b5563; font-size: 24px; cursor: pointer; padding: 8px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='none'">✕</button>
                    </div>

                    <!-- محتوى النافذة القابل للتمرير -->
                    <div style="flex: 1; overflow-y: auto; padding: 25px;">
                        <!-- التبويبات الثلاثة -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 30px; margin-bottom: 35px;">
                            <!-- إضافة معلومات المستخدمين -->
                            <div style="text-align: center;">
                                <div onclick="selectSettingsTab('users')" id="usersTab" style="cursor: pointer; display: inline-flex; align-items: center; gap: 8px; padding-bottom: 8px; border-bottom: 3px solid #3b82f6; color: #3b82f6; transition: all 0.3s;">
                                    <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                        <circle cx="9" cy="7" r="4"/>
                                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                    </svg>
                                    <span style="font-weight: 600;">إضافة معلومات المستخدمين</span>
                                </div>
                            </div>

                            <!-- الصلاحيات -->
                            <div style="text-align: center;">
                                <div onclick="selectSettingsTab('permissions')" id="permissionsTab" style="cursor: pointer; display: inline-flex; align-items: center; gap: 8px; padding-bottom: 8px; border-bottom: 3px solid #d1d5db; color: #6b7280; transition: all 0.3s;">
                                    <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    <span style="font-weight: 600;">الصلاحيات</span>
                                </div>
                            </div>

                            <!-- إعادة تعيين كلمة المرور -->
                            <div style="text-align: center;">
                                <div onclick="selectSettingsTab('password')" id="passwordTab" style="cursor: pointer; display: inline-flex; align-items: center; gap: 8px; padding-bottom: 8px; border-bottom: 3px solid #d1d5db; color: #6b7280; transition: all 0.3s;">
                                    <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                        <circle cx="12" cy="16" r="1"/>
                                        <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                                    </svg>
                                    <span style="font-weight: 600;">إعادة تعيين كلمة المرور</span>
                                </div>
                            </div>
                        </div>

                        <!-- محتوى التبويبات -->
                        <div id="settingsContent">
                            <!-- سيتم إدراج المحتوى هنا -->
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
            selectSettingsTab('users'); // عرض تبويب المستخدمين افتراضياً
        }

        function selectSettingsTab(tab) {
            currentSettingsTab = tab;

            // إعادة تعيين ألوان التبويبات
            document.getElementById('usersTab').style.borderBottomColor = '#d1d5db';
            document.getElementById('usersTab').style.color = '#6b7280';
            document.getElementById('permissionsTab').style.borderBottomColor = '#d1d5db';
            document.getElementById('permissionsTab').style.color = '#6b7280';
            document.getElementById('passwordTab').style.borderBottomColor = '#d1d5db';
            document.getElementById('passwordTab').style.color = '#6b7280';

            // تفعيل التبويب المختار
            document.getElementById(tab + 'Tab').style.borderBottomColor = '#3b82f6';
            document.getElementById(tab + 'Tab').style.color = '#3b82f6';

            // عرض المحتوى المناسب
            const content = document.getElementById('settingsContent');
            if (tab === 'users') {
                showUsersContent(content);
            } else if (tab === 'permissions') {
                showPermissionsContent(content);
            } else if (tab === 'password') {
                showPasswordContent(content);
            }
        }

        function showUsersContent(container) {
            container.innerHTML = `
                <div>
                    <!-- الأزرار الثلاثة -->
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">إدخال المعلومات</h3>
                        <div style="display: flex; gap: 12px; justify-content: center;">
                            <button onclick="selectAction('add')" id="addBtn" style="background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s; transform: scale(1);" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">إضافة</button>
                            <button onclick="selectAction('edit')" id="editBtn" style="background: #f97316; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s; transform: scale(1);" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">تعديل</button>
                            <button onclick="selectAction('delete')" id="deleteBtn" style="background: #ef4444; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s; transform: scale(1);" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">حذف</button>
                        </div>
                    </div>

                    <!-- رسالة اختيار العملية -->
                    <div id="selectActionMessage" style="text-align: center; color: #6b7280; font-size: 16px; padding: 40px; background: #f9fafb; border-radius: 12px; border: 2px dashed #d1d5db; margin-bottom: 30px;">
                        يرجى اختيار العملية أولاً (إضافة، تعديل، أو حذف)
                    </div>

                    <!-- مستطيل الاختيارات المركزي -->
                    <div id="fieldsContainer" style="display: none;">
                        <div id="centralSelectContainer" style="text-align: center; margin-bottom: 25px; display: none;">
                            <h4 style="color: #374151; font-size: 16px; font-weight: bold; margin-bottom: 15px;">الاختيارات</h4>
                            <select id="centralUserSelect" style="padding: 12px 20px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px; background: white; color: #374151; min-width: 300px;" onchange="selectFromCentral()">
                                <option value="">اختر المستخدم</option>
                                ${users.map(user => `<option value="${user}">${user}</option>`).join('')}
                            </select>
                        </div>

                        <!-- الحقول الستة -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px; align-items: center;">
                            <div style="position: relative;">
                                <input type="text" id="userName" placeholder="اسم المستخدم" style="padding: 12px 40px 12px 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" onchange="handleUserNameChange()">
                                <button id="deleteUserBtn" onclick="deleteUser()" style="display: none; position: absolute; left: 8px; top: 50%; transform: translateY(-50%); background: #ef4444; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">🗑️</button>
                            </div>
                            <select id="userSelect" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;" onchange="selectExistingUser()">
                                <option value="">اختر المستخدم</option>
                                ${users.map(user => `<option value="${user}">${user}</option>`).join('')}
                            </select>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px; align-items: center;">
                            <div style="position: relative;">
                                <input type="text" id="userDepartment" placeholder="القسم" style="padding: 12px 40px 12px 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;">
                                <button id="deleteDeptBtn" onclick="deleteUser()" style="display: none; position: absolute; left: 8px; top: 50%; transform: translateY(-50%); background: #ef4444; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">🗑️</button>
                            </div>
                            <select id="departmentSelect" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;" onchange="fillDepartment()">
                                <option value="">اختر القسم</option>
                                <option value="المبيعات">المبيعات</option>
                                <option value="الموارد البشرية">الموارد البشرية</option>
                                <option value="المحاسبة">المحاسبة</option>
                                <option value="التسويق">التسويق</option>
                                <option value="التطوير">التطوير</option>
                            </select>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 40px; align-items: center;">
                            <div style="position: relative;">
                                <input type="text" id="userJobTitle" placeholder="المسمى الوظيفي" style="padding: 12px 40px 12px 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;">
                                <button id="deleteJobBtn" onclick="deleteUser()" style="display: none; position: absolute; left: 8px; top: 50%; transform: translateY(-50%); background: #ef4444; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">🗑️</button>
                            </div>
                            <select id="jobTitleSelect" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;" onchange="fillJobTitle()">
                                <option value="">اختر المسمى الوظيفي</option>
                                <option value="مدير عام">مدير عام</option>
                                <option value="مدير قسم">مدير قسم</option>
                                <option value="مشرف">مشرف</option>
                                <option value="موظف">موظف</option>
                                <option value="محاسب">محاسب</option>
                                <option value="سكرتير">سكرتير</option>
                                <option value="مطور">مطور</option>
                                <option value="مصمم">مصمم</option>
                            </select>
                        </div>

                        <!-- زر الحفظ مع مسافة أكبر -->
                        <div style="text-align: center;">
                            <button onclick="saveUserData()" style="background: #f3f4f6; color: #3b82f6; border: 2px solid #6b7280; padding: 12px 80px; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3); transition: all 0.3s;" onmouseover="this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.boxShadow='0 2px 8px rgba(59, 130, 246, 0.3)'">حفظ</button>
                        </div>
                    </div>
                </div>


            `;
        }

        function selectAction(action) {
            selectedAction = action;

            // إعادة تعيين ألوان الأزرار
            document.getElementById('addBtn').style.opacity = '0.5';
            document.getElementById('editBtn').style.opacity = '0.5';
            document.getElementById('deleteBtn').style.opacity = '0.5';

            // تفعيل الزر المختار
            document.getElementById(action + 'Btn').style.opacity = '1';

            // إظهار الحقول وإخفاء الرسالة
            document.getElementById('fieldsContainer').style.display = 'block';
            document.getElementById('selectActionMessage').style.display = 'none';

            // إخفاء أيقونات الحذف افتراضياً
            document.getElementById('deleteUserBtn').style.display = 'none';
            document.getElementById('deleteDeptBtn').style.display = 'none';
            document.getElementById('deleteJobBtn').style.display = 'none';

            // إظهار/إخفاء مستطيل الاختيارات حسب العملية
            const centralContainer = document.getElementById('centralSelectContainer');
            if (action === 'add') {
                // في الإضافة: إخفاء مستطيل الاختيارات ومسح الحقول
                centralContainer.style.display = 'none';
                clearFields();
            } else if (action === 'edit' || action === 'delete') {
                // في التعديل والحذف: إظهار مستطيل الاختيارات
                centralContainer.style.display = 'block';
                clearFields();
            }
        }

        function saveUserData() {
            if (!selectedAction) {
                alert('يرجى اختيار العملية أولاً (إضافة، تعديل، أو حذف)');
                return;
            }

            const userName = document.getElementById('userName').value;
            const userDepartment = document.getElementById('userDepartment').value;
            const userJobTitle = document.getElementById('userJobTitle').value;

            if (!userName || !userDepartment || !userJobTitle) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            if (selectedAction === 'add') {
                // إضافة مستخدم جديد
                users.push(userName);
                savedUsersData[userName] = {
                    department: userDepartment,
                    jobTitle: userJobTitle
                };
                alert('تم إضافة المستخدم بنجاح');
                clearFields(); // تفريغ الحقول للإضافة التالية

            } else if (selectedAction === 'edit') {
                // تعديل مستخدم موجود
                savedUsersData[userName] = {
                    department: userDepartment,
                    jobTitle: userJobTitle
                };
                alert('تم تعديل المستخدم بنجاح');

            } else if (selectedAction === 'delete') {
                // حذف مستخدم
                const index = users.indexOf(userName);
                if (index > -1) {
                    users.splice(index, 1);
                    delete savedUsersData[userName];
                    alert('تم حذف المستخدم بنجاح');
                    clearFields();
                } else {
                    alert('المستخدم غير موجود');
                }
            }

            // تحديث قائمة المستخدمين في جميع الاختيارات
            updateUserSelects();
        }

        // بيانات المستخدمين المحفوظة
        let savedUsersData = {};

        function clearFields() {
            document.getElementById('userName').value = '';
            document.getElementById('userDepartment').value = '';
            document.getElementById('userJobTitle').value = '';
            document.getElementById('userSelect').value = '';
            document.getElementById('departmentSelect').value = '';
            document.getElementById('jobTitleSelect').value = '';
            document.getElementById('centralUserSelect').value = '';
        }

        function updateUserSelects() {
            const userSelect = document.getElementById('userSelect');
            const centralUserSelect = document.getElementById('centralUserSelect');

            const optionsHTML = '<option value="">اختر المستخدم</option>' +
                users.map(user => `<option value="${user}">${user}</option>`).join('');

            if (userSelect) {
                userSelect.innerHTML = optionsHTML;
            }
            if (centralUserSelect) {
                centralUserSelect.innerHTML = optionsHTML;
            }
        }

        function selectFromCentral() {
            const centralSelect = document.getElementById('centralUserSelect');
            const userName = centralSelect.value;

            if (userName) {
                document.getElementById('userName').value = userName;
                document.getElementById('userSelect').value = userName;
                handleUserNameChange();
            }
        }

        function handleUserNameChange() {
            const userName = document.getElementById('userName').value;

            if (selectedAction === 'edit' || selectedAction === 'delete') {
                if (savedUsersData[userName]) {
                    // ملء الحقول بالبيانات المحفوظة
                    document.getElementById('userDepartment').value = savedUsersData[userName].department;
                    document.getElementById('userJobTitle').value = savedUsersData[userName].jobTitle;

                    // إظهار أيقونات الحذف في وضع الحذف
                    if (selectedAction === 'delete') {
                        document.getElementById('deleteUserBtn').style.display = 'block';
                        document.getElementById('deleteDeptBtn').style.display = 'block';
                        document.getElementById('deleteJobBtn').style.display = 'block';
                    }
                }
            }
        }

        function selectExistingUser() {
            const userSelect = document.getElementById('userSelect');
            const userName = userSelect.value;

            if (userName) {
                document.getElementById('userName').value = userName;
                handleUserNameChange();
            }
        }

        function fillDepartment() {
            const departmentSelect = document.getElementById('departmentSelect');
            const department = departmentSelect.value;

            if (department) {
                document.getElementById('userDepartment').value = department;
            }
        }

        function fillJobTitle() {
            const jobTitleSelect = document.getElementById('jobTitleSelect');
            const jobTitle = jobTitleSelect.value;

            if (jobTitle) {
                document.getElementById('userJobTitle').value = jobTitle;
            }
        }

        function deleteUser() {
            if (selectedAction === 'delete') {
                const userName = document.getElementById('userName').value;

                if (userName && savedUsersData[userName]) {
                    // حذف المستخدم من البيانات
                    const index = users.indexOf(userName);
                    if (index > -1) {
                        users.splice(index, 1);
                        delete savedUsersData[userName];
                    }

                    alert('تم حذف المستخدم بنجاح');
                    clearFields();
                    updateUserSelects();

                    // إخفاء أيقونات الحذف
                    document.getElementById('deleteUserBtn').style.display = 'none';
                    document.getElementById('deleteDeptBtn').style.display = 'none';
                    document.getElementById('deleteJobBtn').style.display = 'none';
                } else {
                    alert('لا يوجد مستخدم للحذف');
                }
            }
        }

        function showPermissionsContent(container) {
            container.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px;">
                    <!-- الجانب الأيمن - اختيار المستخدم ونوع البرنامج -->
                    <div>
                        <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">اختيار المستخدم والبرنامج</h3>

                        <div style="display: flex; flex-direction: column; gap: 20px;">
                            <select id="permissionUser" onchange="updatePermissions()" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">اختر المستخدم</option>
                                ${users.map(user => `<option value="${user}">${user}</option>`).join('')}
                            </select>

                            <select id="programType" onchange="showPermissionOptions()" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">اختر نوع البرنامج</option>
                                <option value="A">البرنامج الرئيسي - نوع A (كل الصلاحيات)</option>
                                <option value="B">برنامج فرع B (الحافظة، البحث، المحذوفات، النشر)</option>
                                <option value="C">برنامج فرع C (الحافظة، البحث، المحذوفات)</option>
                                <option value="D">برنامج فرع D (الحافظة، البحث، المحذوفات، شاشات العرض)</option>
                            </select>
                        </div>
                    </div>

                    <!-- الجانب الأيسر - خيارات الصلاحيات -->
                    <div>
                        <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">الصلاحيات المتاحة</h3>

                        <div id="selectProgramMessage" style="text-align: center; color: #6b7280; font-size: 16px; padding: 40px; background: #f9fafb; border-radius: 12px; border: 2px dashed #d1d5db; margin-bottom: 20px;">
                            يرجى اختيار نوع البرنامج لعرض الصلاحيات المتاحة
                        </div>

                        <div id="permissionOptions" style="display: none;">
                            <div style="display: flex; flex-direction: column; gap: 15px;">
                                <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                    <input type="checkbox" id="perm_archive" style="width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 4px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);">
                                    <span style="color: #374151; font-weight: 500;">الحافظة</span>
                                </label>

                                <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                    <input type="checkbox" id="perm_search" style="width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 4px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);">
                                    <span style="color: #374151; font-weight: 500;">البحث</span>
                                </label>

                                <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                    <input type="checkbox" id="perm_deleted" style="width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 4px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);">
                                    <span style="color: #374151; font-weight: 500;">المحذوفات</span>
                                </label>

                                <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                    <input type="checkbox" id="perm_publish" style="width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 4px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);">
                                    <span style="color: #374151; font-weight: 500;">النشر</span>
                                </label>

                                <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                    <input type="checkbox" id="perm_displays" style="width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 4px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);">
                                    <span style="color: #374151; font-weight: 500;">شاشات العرض</span>
                                </label>

                                <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                    <input type="checkbox" id="perm_settings" style="width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 4px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);">
                                    <span style="color: #374151; font-weight: 500;">الإعدادات</span>
                                </label>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- زر الحفظ -->
                <div style="text-align: center; margin-top: 40px;">
                    <button onclick="savePermissions()" style="background: #f3f4f6; color: #3b82f6; border: 2px solid #6b7280; padding: 12px 80px; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3); transition: all 0.3s;" onmouseover="this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.boxShadow='0 2px 8px rgba(59, 130, 246, 0.3)'">حفظ</button>
                </div>

                    </div>
                </div>
            `;
        }

        function showPermissionOptions() {
            const programType = document.getElementById('programType').value;
            const optionsDiv = document.getElementById('permissionOptions');
            const messageDiv = document.getElementById('selectProgramMessage');

            if (!programType) {
                optionsDiv.style.display = 'none';
                messageDiv.style.display = 'block';
                return;
            }

            optionsDiv.style.display = 'block';
            messageDiv.style.display = 'none';

            // إعادة تعيين جميع الصلاحيات
            document.getElementById('perm_archive').checked = false;
            document.getElementById('perm_search').checked = false;
            document.getElementById('perm_deleted').checked = false;
            document.getElementById('perm_publish').checked = false;
            document.getElementById('perm_displays').checked = false;
            document.getElementById('perm_settings').checked = false;

            // تعيين الصلاحيات حسب نوع البرنامج
            if (programType === 'A') {
                // كل الصلاحيات
                document.getElementById('perm_archive').checked = true;
                document.getElementById('perm_search').checked = true;
                document.getElementById('perm_deleted').checked = true;
                document.getElementById('perm_publish').checked = true;
                document.getElementById('perm_displays').checked = true;
                document.getElementById('perm_settings').checked = true;
            } else if (programType === 'B') {
                // الحافظة، البحث، المحذوفات، النشر
                document.getElementById('perm_archive').checked = true;
                document.getElementById('perm_search').checked = true;
                document.getElementById('perm_deleted').checked = true;
                document.getElementById('perm_publish').checked = true;
            } else if (programType === 'C') {
                // الحافظة، البحث، المحذوفات
                document.getElementById('perm_archive').checked = true;
                document.getElementById('perm_search').checked = true;
                document.getElementById('perm_deleted').checked = true;
            } else if (programType === 'D') {
                // الحافظة، البحث، المحذوفات، شاشات العرض
                document.getElementById('perm_archive').checked = true;
                document.getElementById('perm_search').checked = true;
                document.getElementById('perm_deleted').checked = true;
                document.getElementById('perm_displays').checked = true;
            }

            // تطبيق الألوان على المربعات المحددة
            updateCheckboxStyles();
        }

        function updateCheckboxStyles() {
            const checkboxes = ['perm_archive', 'perm_search', 'perm_deleted', 'perm_publish', 'perm_displays', 'perm_settings'];
            checkboxes.forEach(id => {
                const checkbox = document.getElementById(id);
                if (checkbox.checked) {
                    checkbox.style.backgroundColor = '#3b82f6';
                    checkbox.style.borderColor = '#3b82f6';
                } else {
                    checkbox.style.backgroundColor = 'white';
                    checkbox.style.borderColor = '#d1d5db';
                }
            });
        }

        function savePermissions() {
            const user = document.getElementById('permissionUser').value;
            const programType = document.getElementById('programType').value;

            if (!user || !programType) {
                alert('يرجى اختيار المستخدم ونوع البرنامج');
                return;
            }

            alert('تم حفظ الصلاحيات بنجاح للمستخدم: ' + user);
        }

        function showPasswordContent(container) {
            // تحديث قائمة طلبات إعادة تعيين كلمة المرور من نظام المصادقة
            const authRequests = authSystem.getPasswordResetRequests();
            passwordRequests = authRequests.filter(req => req.status === 'pending').map(req => ({
                user: req.username,
                date: new Date(req.timestamp).toLocaleDateString('ar-SA'),
                status: req.status
            }));

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px;">
                    <!-- الجانب الأيمن - كتابة كلمة مرور جديدة -->
                    <div>
                        <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">كتابة كلمة مرور جديدة</h3>

                        <div style="display: flex; flex-direction: column; gap: 20px;">
                            <select id="passwordUserSelect" onchange="showPasswordFields()" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">اختر الاسم</option>
                                ${users.map(user => `<option value="${user}">${user}</option>`).join('')}
                            </select>

                            <div id="passwordFields" style="display: none; flex-direction: column; gap: 15px;">
                                <input type="text" id="selectedUserName" placeholder="اسم المستخدم" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;" readonly>
                                <input type="password" id="newPassword" placeholder="كلمة المرور الجديدة" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <button onclick="saveNewPassword()" style="background: #f3f4f6; color: #3b82f6; border: 2px solid #6b7280; padding: 12px 40px; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3); transition: all 0.3s; margin-top: 10px;" onmouseover="this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.boxShadow='0 2px 8px rgba(59, 130, 246, 0.3)'">حفظ</button>
                            </div>
                        </div>
                    </div>

                    <!-- الجانب الأيسر - جدول طلبات إعادة التعيين -->
                    <div>
                        <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">طلبات إعادة تعيين كلمة المرور</h3>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <!-- رأس الجدول -->
                            <div style="background: #f8f9fa; height: 38px; padding: 0 12px; border-radius: 15px; border: 1px solid #d1d5db; display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 16px; font-weight: bold; color: #374151; font-size: 14px; align-items: center;">
                                <div>اسم المستخدم</div>
                                <div>التاريخ</div>
                                <div>الحالة</div>
                                <div>الإجراء</div>
                            </div>

                            <!-- صفوف الجدول مع التمرير -->
                            <div style="max-height: 192px; overflow-y: auto; border-radius: 15px;">
                                ${passwordRequests.map((request, index) => `
                                    <div style="height: 38px; padding: 0 12px; border: 1px solid #e5e7eb; border-radius: 15px; display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 16px; align-items: center; font-size: 14px; transition: all 0.3s; background: #f9fafb; margin-bottom: 8px;"
                                         onmouseover="this.style.borderColor='#3b82f6'; this.style.background='#eff6ff';"
                                         onmouseout="this.style.borderColor='#e5e7eb'; this.style.background='#f9fafb';">
                                        <div style="color: #374151; font-weight: 500;">${request.user}</div>
                                        <div style="color: #6b7280;">${request.date}</div>
                                        <div style="color: #f59e0b; font-weight: 500;">في الانتظار</div>
                                        <div style="display: flex; gap: 8px;">
                                            <button onclick="approvePasswordRequest('${request.user}')" style="background: #10b981; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px; font-weight: 500;">موافقة</button>
                                            <button onclick="rejectPasswordRequest('${request.user}')" style="background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px; font-weight: 500;">رفض</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم كلمات المرور المنشأة -->
                <div style="margin-bottom: 20px; margin-top: 30px;">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <button onclick="togglePasswordsList()" id="passwordsToggleBtn" style="background: none; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 12px; padding: 8px; transition: all 0.3s;">
                            <svg id="keyIcon" style="width: 24px; height: 24px; color: #6b7280; transition: color 0.3s;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"/>
                            </svg>
                            <span id="passwordsText" style="color: #6b7280; font-size: 18px; font-weight: bold; transition: color 0.3s;">كلمات المرور المنشأة</span>
                        </button>
                    </div>

                    <div id="passwordsListContainer" style="display: none;">
                        <div style="display: flex; flex-direction: column; gap: 15px;">
                            <!-- رأس الجدول -->
                            <div style="background: #f3f4f6; height: 38px; padding: 0 16px; border-radius: 15px; border: 1px solid #9ca3af; display: grid; grid-template-columns: 2fr 2fr 1fr 1fr; gap: 16px; font-weight: bold; color: #374151; font-size: 14px; align-items: center;">
                                <div>اسم المستخدم</div>
                                <div>كلمة المرور</div>
                                <div>التاريخ</div>
                                <div>الوقت</div>
                            </div>

                            <!-- صفوف الجدول مع التمرير -->
                            <div style="max-height: 192px; overflow-y: auto; border-radius: 15px;">
                                <div id="createdPasswordsList">
                                    ${createdPasswords.map((item, index) => `
                                        <div style="height: 38px; padding: 0 16px; border: 1px solid #9ca3af; border-radius: 15px; display: grid; grid-template-columns: 2fr 2fr 1fr 1fr; gap: 16px; align-items: center; font-size: 14px; transition: all 0.3s; background: #f3f4f6; margin-bottom: 8px;"
                                             onmouseover="this.style.borderColor='#3b82f6';"
                                             onmouseout="this.style.borderColor='#9ca3af';">
                                            <div style="color: #374151; font-weight: 500;">${item.user}</div>
                                            <div style="color: #374151; font-family: monospace; font-weight: 600; background: #e5e7eb; padding: 4px 8px; border-radius: 6px; display: inline-block;">${item.password}</div>
                                            <div style="color: #6b7280;">${item.date}</div>
                                            <div style="color: #6b7280;">${item.time}</div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            `;
        }

        function showPasswordFields() {
            const selectedUser = document.getElementById('passwordUserSelect').value;
            const fieldsDiv = document.getElementById('passwordFields');

            if (selectedUser) {
                fieldsDiv.style.display = 'flex';
                document.getElementById('selectedUserName').value = selectedUser;
            } else {
                fieldsDiv.style.display = 'none';
            }
        }

        function saveNewPassword() {
            const selectedUser = document.getElementById('passwordUserSelect').value;
            const newPassword = document.getElementById('newPassword').value;

            if (!selectedUser || !newPassword) {
                alert('يرجى اختيار المستخدم وإدخال كلمة المرور الجديدة');
                return;
            }

            if (newPassword.length < 6) {
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            // تحديث كلمة المرور في نظام المصادقة
            if (authSystem.updatePassword(selectedUser, newPassword)) {
                // إضافة كلمة المرور الجديدة للقائمة
                const currentDate = new Date();
                const dateStr = currentDate.toLocaleDateString('ar-SA');
                const timeStr = currentDate.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });

                createdPasswords.unshift({
                    user: selectedUser,
                    password: newPassword,
                    date: dateStr,
                    time: timeStr
                });

                alert(`✅ تم حفظ كلمة المرور الجديدة للمستخدم: ${selectedUser}\nكلمة المرور: ${newPassword}`);

                // إعادة تعيين الحقول
                document.getElementById('passwordUserSelect').value = '';
                document.getElementById('newPassword').value = '';
                document.getElementById('passwordFields').style.display = 'none';

                // إعادة تحديث القائمة فقط
                updatePasswordsList();
            } else {
                alert('❌ فشل في تحديث كلمة المرور. تأكد من صحة اسم المستخدم.');
            }
        }

        function togglePasswordsList() {
            const container = document.getElementById('passwordsListContainer');
            const text = document.getElementById('passwordsText');
            const icon = document.getElementById('keyIcon');

            if (container.style.display === 'none') {
                container.style.display = 'block';
                text.style.color = '#3b82f6';
                icon.style.color = '#3b82f6';
            } else {
                container.style.display = 'none';
                text.style.color = '#6b7280';
                icon.style.color = '#6b7280';
            }
        }

        function updatePasswordsList() {
            const listContainer = document.getElementById('createdPasswordsList');
            if (listContainer) {
                listContainer.innerHTML = createdPasswords.map((item, index) => `
                    <div style="height: 38px; padding: 0 16px; border: 1px solid #9ca3af; border-radius: 15px; display: grid; grid-template-columns: 2fr 2fr 1fr 1fr; gap: 16px; align-items: center; font-size: 14px; transition: all 0.3s; background: #f3f4f6; margin-bottom: 8px;"
                         onmouseover="this.style.borderColor='#3b82f6';"
                         onmouseout="this.style.borderColor='#9ca3af';">
                        <div style="color: #374151; font-weight: 500;">${item.user}</div>
                        <div style="color: #374151; font-family: monospace; font-weight: 600; background: #e5e7eb; padding: 4px 8px; border-radius: 6px; display: inline-block;">${item.password}</div>
                        <div style="color: #6b7280;">${item.date}</div>
                        <div style="color: #6b7280;">${item.time}</div>
                    </div>
                `).join('');
            }
        }

        function copyPassword(password) {
            // نسخ كلمة المرور للحافظة
            navigator.clipboard.writeText(password).then(function() {
                alert('تم نسخ كلمة المرور إلى الحافظة');
            }).catch(function() {
                // في حالة عدم دعم المتصفح لـ clipboard API
                const textArea = document.createElement('textarea');
                textArea.value = password;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ كلمة المرور إلى الحافظة');
            });
        }

        function approvePasswordRequest(userName) {
            const newPassword = prompt('أدخل كلمة المرور الجديدة للمستخدم: ' + userName);
            if (newPassword && newPassword.length >= 6) {
                // البحث عن الطلب في نظام المصادقة
                const authRequests = authSystem.getPasswordResetRequests();
                const request = authRequests.find(req => req.username === userName && req.status === 'pending');

                if (request) {
                    authSystem.approvePasswordReset(request.id, newPassword);
                    alert(`✅ تم الموافقة على الطلب وتعيين كلمة المرور الجديدة: ${newPassword}`);

                    // إضافة إلى قائمة كلمات المرور المنشأة
                    const now = new Date();
                    createdPasswords.push({
                        user: userName,
                        password: newPassword,
                        date: now.toLocaleDateString('ar-SA'),
                        time: now.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })
                    });

                    showPasswordContent(document.getElementById('settingsContent'));
                } else {
                    alert('❌ لم يتم العثور على الطلب');
                }
            } else {
                alert('يرجى إدخال كلمة مرور صحيحة (6 أحرف على الأقل)');
            }
        }

        function rejectPasswordRequest(userName) {
            const reason = prompt('أدخل سبب الرفض (اختياري):') || 'لم يتم تحديد السبب';
            if (confirm('هل تريد رفض طلب إعادة تعيين كلمة المرور للمستخدم: ' + userName + '؟')) {
                // البحث عن الطلب في نظام المصادقة
                const authRequests = authSystem.getPasswordResetRequests();
                const request = authRequests.find(req => req.username === userName && req.status === 'pending');

                if (request) {
                    authSystem.rejectPasswordReset(request.id, reason);
                    alert(`❌ تم رفض الطلب\nالسبب: ${reason}`);
                    showPasswordContent(document.getElementById('settingsContent'));
                } else {
                    alert('❌ لم يتم العثور على الطلب');
                }
            }
        }

        function selectFileType(type) {
            selectedFileType = type;

            // إعادة تعيين ألوان التبويبات
            document.getElementById('receivedTab').style.borderBottomColor = '#d1d5db';
            document.getElementById('receivedTab').style.color = '#6b7280';
            document.getElementById('sentTab').style.borderBottomColor = '#d1d5db';
            document.getElementById('sentTab').style.color = '#6b7280';

            // تفعيل التبويب المختار
            const selectedTab = type === 'received' ? 'receivedTab' : 'sentTab';
            document.getElementById(selectedTab).style.borderBottomColor = '#3b82f6';
            document.getElementById(selectedTab).style.color = '#3b82f6';

            // تفعيل البحث
            document.getElementById('archiveSearch').disabled = false;
            document.getElementById('searchBtn').disabled = false;
            document.getElementById('archiveSearch').style.borderColor = '#d1d5db';
            document.getElementById('searchBtn').style.opacity = '1';

            // إخفاء رسالة التنبيه
            document.getElementById('selectTypeMessage').style.display = 'none';

            // إظهار جدول فارغ
            document.getElementById('resultsTable').style.display = 'block';
            document.getElementById('tableBody').innerHTML = '<div style="text-align: center; padding: 40px; color: #6b7280;">استخدم البحث لعرض النتائج</div>';
        }

        function handleArchiveSearch() {
            archiveSearchQuery = document.getElementById('archiveSearch').value;
        }

        function performArchiveSearch() {
            if (!selectedFileType) {
                alert('يرجى اختيار نوع الملفات أولاً');
                return;
            }

            // بيانات تجريبية
            const sampleData = {
                received: [
                    { user: 'أحمد محمد', department: 'المبيعات', fileType: 'تقرير شهري', date: '2024-01-15', time: '10:30' },
                    { user: 'سارة أحمد', department: 'الموارد البشرية', fileType: 'عقد عمل', date: '2024-01-14', time: '14:20' },
                    { user: 'محمد علي', department: 'المحاسبة', fileType: 'فاتورة', date: '2024-01-13', time: '09:15' }
                ],
                sent: [
                    { user: 'فاطمة حسن', department: 'الإدارة', fileType: 'مذكرة داخلية', date: '2024-01-16', time: '11:45' },
                    { user: 'خالد أحمد', department: 'التسويق', fileType: 'خطة تسويقية', date: '2024-01-15', time: '16:30' },
                    { user: 'نور محمد', department: 'التطوير', fileType: 'تقرير تقني', date: '2024-01-14', time: '13:10' }
                ]
            };

            let data = sampleData[selectedFileType] || [];

            // تطبيق البحث إذا كان هناك نص
            if (archiveSearchQuery.trim()) {
                data = data.filter(item =>
                    item.user.includes(archiveSearchQuery) ||
                    item.department.includes(archiveSearchQuery) ||
                    item.fileType.includes(archiveSearchQuery)
                );
            }

            // عرض النتائج
            const tableBody = document.getElementById('tableBody');
            if (data.length === 0) {
                tableBody.innerHTML = '<div style="text-align: center; padding: 40px; color: #6b7280; background: #f9fafb; border-radius: 15px; border: 1px solid #e5e7eb;">لا توجد نتائج</div>';
            } else {
                tableBody.innerHTML = data.map((item, index) => `
                    <div style="padding: 12px 16px; border: 1px solid #e5e7eb; border-radius: 15px; display: grid; grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1fr 0.5fr; gap: 16px; align-items: center; font-size: 14px; transition: all 0.3s; background: #f9fafb;"
                         onmouseover="this.style.borderColor='#3b82f6'; this.style.background='#eff6ff';"
                         onmouseout="this.style.borderColor='#e5e7eb'; this.style.background='#f9fafb';">
                        <div style="color: #374151; font-weight: 500;">${item.user}</div>
                        <div style="color: #6b7280;">${item.department}</div>
                        <div style="color: #6b7280;">${item.fileType}</div>
                        <div style="color: #6b7280;">${item.date}</div>
                        <div style="color: #6b7280;">${item.time}</div>
                        <div style="text-align: center;">
                            <button onclick="deleteFile('${item.user}', this)" style="background: none; border: none; cursor: pointer; padding: 6px; border-radius: 8px; transition: all 0.3s;"
                                    onmouseover="this.style.background='#fee2e2'; this.querySelector('svg').style.color='#dc2626';"
                                    onmouseout="this.style.background='none'; this.querySelector('svg').style.color='#9ca3af';">
                                <svg style="width: 18px; height: 18px; color: #9ca3af; transition: color 0.3s;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="3,6 5,6 21,6"/>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                                    <line x1="10" y1="11" x2="10" y2="17"/>
                                    <line x1="14" y1="11" x2="14" y2="17"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                `).join('');
            }
        }

        function deleteFile(userName, buttonElement) {
            // تغيير لون الأيقونة للأحمر الغامق عند النقر
            const svg = buttonElement.querySelector('svg');
            svg.style.color = '#991b1b';
            buttonElement.style.background = '#fecaca';

            if (confirm('هل تريد حذف هذا الملف؟')) {
                alert('تم حذف الملف بنجاح');
                performArchiveSearch(); // إعادة تحديث النتائج
            } else {
                // إعادة اللون الأصلي إذا ألغى المستخدم
                svg.style.color = '#9ca3af';
                buttonElement.style.background = 'none';
            }
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            // إعادة تعيين المحتوى للحالة الأصلية
            modalContent.innerHTML = `
                <h3 class="modal-title" id="modalTitle">عنوان النافذة</h3>
                <p class="modal-text" id="modalText">هذه النافذة قيد التطوير...</p>
                <button class="modal-btn" onclick="closeModal()">إغلاق</button>
            `;

            // إخفاء النافذة
            modal.style.display = 'none';

            // إعادة تعيين المتغيرات
            selectedFileType = '';
            archiveSearchQuery = '';
        }

        function toggleNotification() {
            const bell = document.querySelector('.notification-bell');
            const dot = document.getElementById('notificationDot');

            if (hasNotification) {
                bell.classList.remove('has-notification');
                dot.style.display = 'none';
                hasNotification = false;
                showModal('تم عرض جميع التنبيهات');
            }
        }

        // دالة البحث
        function showSearchWindow() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            modalContent.innerHTML = `
                <div style="height: 85vh; background: white; border-radius: 20px; padding: 0; overflow: hidden;">
                    <!-- الشريط العلوي -->
                    <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <svg style="width: 24px; height: 24px; color: #10b981;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="M21 21l-4.35-4.35"/>
                            </svg>
                            <h2 style="color: #4b5563; font-size: 20px; font-weight: bold; margin: 0;">البحث في الملفات</h2>
                        </div>
                        <button onclick="closeModal()" style="background: none; border: none; color: #4b5563; font-size: 24px; cursor: pointer; padding: 8px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='none'">✕</button>
                    </div>

                    <!-- محتوى النافذة -->
                    <div style="padding: 25px;">
                        <!-- شريط البحث الرئيسي -->
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 30px; justify-content: center;">
                            <input type="text" id="mainSearchInput" placeholder="ابحث عن الملفات، المستخدمين، الأقسام..."
                                   style="width: 600px; padding: 10px 20px; border: 2px solid #9ca3af; border-radius: 25px; font-size: 16px; outline: none;"
                                   onkeyup="performMainSearch()" onkeypress="if(event.key==='Enter') performMainSearch()">
                            <button onclick="performMainSearch()" style="background: none; border: none; cursor: pointer; display: flex; align-items: center; justify-content: center; padding: 8px;">
                                <svg style="width: 32px; height: 32px; color: #10b981;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/>
                                    <path d="M21 21l-4.35-4.35"/>
                                </svg>
                            </button>
                        </div>

                        <!-- فلاتر البحث -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                            <select id="searchFileType" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;" onchange="performMainSearch()">
                                <option value="">جميع أنواع الملفات</option>
                                <option value="pdf">ملفات PDF</option>
                                <option value="doc">ملفات Word</option>
                                <option value="image">الصور</option>
                                <option value="excel">ملفات Excel</option>
                            </select>
                            <select id="searchDepartment" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;" onchange="performMainSearch()">
                                <option value="">جميع الأقسام</option>
                                <option value="المبيعات">المبيعات</option>
                                <option value="الموارد البشرية">الموارد البشرية</option>
                                <option value="المحاسبة">المحاسبة</option>
                                <option value="التسويق">التسويق</option>
                                <option value="التطوير">التطوير</option>
                            </select>
                            <select id="searchDateRange" style="padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;" onchange="performMainSearch()">
                                <option value="">جميع التواريخ</option>
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="year">هذا العام</option>
                            </select>
                        </div>

                        <!-- نتائج البحث -->
                        <div id="searchResults" style="display: none;">
                            <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">نتائج البحث</h3>
                            <div style="background: #f8f9fa; padding: 12px 16px; border-radius: 15px; border: 1px solid #d1d5db; display: grid; grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 0.5fr; gap: 16px; font-weight: bold; color: #374151; font-size: 14px; margin-bottom: 8px;">
                                <div>اسم الملف</div>
                                <div>المستخدم</div>
                                <div>القسم</div>
                                <div>النوع</div>
                                <div>التاريخ</div>
                                <div>إجراءات</div>
                            </div>
                            <div id="searchResultsBody" style="display: flex; flex-direction: column; gap: 8px; max-height: 400px; overflow-y: auto;">
                                <!-- سيتم إدراج النتائج هنا -->
                            </div>
                        </div>

                        <!-- رسالة عدم وجود نتائج -->
                        <div id="noSearchResults" style="display: none; text-align: center; color: #6b7280; font-size: 16px; padding: 40px; background: #f9fafb; border-radius: 12px; border: 2px dashed #d1d5db;">
                            لم يتم العثور على نتائج مطابقة لبحثك
                        </div>

                        <!-- رسالة البداية -->
                        <div id="searchStartMessage" style="text-align: center; color: #6b7280; font-size: 16px; padding: 40px; background: #f9fafb; border-radius: 12px; border: 2px dashed #d1d5db;">
                            ابدأ بكتابة كلمة البحث أو استخدم الفلاتر للعثور على الملفات
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
        }

        // دالة تنفيذ البحث
        function performMainSearch() {
            const searchTerm = document.getElementById('mainSearchInput').value.trim();
            const fileType = document.getElementById('searchFileType').value;
            const department = document.getElementById('searchDepartment').value;
            const dateRange = document.getElementById('searchDateRange').value;

            // الحصول على الملفات من النظام
            const data = storage.getData();
            const allFiles = data.files;

            // تطبيق الفلاتر
            let filteredFiles = allFiles.filter(file => {
                let matches = true;

                if (searchTerm && !file.name.includes(searchTerm) && !file.user.includes(searchTerm)) {
                    matches = false;
                }

                if (fileType && file.type !== fileType) {
                    matches = false;
                }

                if (department && file.department !== department) {
                    matches = false;
                }

                // فلتر التاريخ
                if (dateRange) {
                    const fileDate = new Date(file.date);
                    const now = new Date();
                    let dateMatches = false;

                    switch(dateRange) {
                        case 'today':
                            dateMatches = fileDate.toDateString() === now.toDateString();
                            break;
                        case 'week':
                            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                            dateMatches = fileDate >= weekAgo;
                            break;
                        case 'month':
                            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                            dateMatches = fileDate >= monthAgo;
                            break;
                        case 'year':
                            const yearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                            dateMatches = fileDate >= yearAgo;
                            break;
                        default:
                            dateMatches = true;
                    }

                    if (!dateMatches) {
                        matches = false;
                    }
                }

                return matches;
            });

            // عرض النتائج
            const resultsContainer = document.getElementById('searchResults');
            const noResultsContainer = document.getElementById('noSearchResults');
            const startMessageContainer = document.getElementById('searchStartMessage');
            const resultsBody = document.getElementById('searchResultsBody');

            if (searchTerm || fileType || department || dateRange) {
                startMessageContainer.style.display = 'none';

                if (filteredFiles.length > 0) {
                    noResultsContainer.style.display = 'none';
                    resultsContainer.style.display = 'block';

                    resultsBody.innerHTML = filteredFiles.map(file => `
                        <div style="background: white; padding: 12px 16px; border-radius: 12px; border: 1px solid #e5e7eb; display: grid; grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 0.5fr; gap: 16px; align-items: center; font-size: 14px; transition: all 0.2s;" onmouseover="this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.boxShadow='none'">
                            <div style="font-weight: 500; color: #374151;">${file.name}</div>
                            <div style="color: #6b7280;">${file.user}</div>
                            <div style="color: #6b7280;">${file.department}</div>
                            <div style="color: #6b7280;">${file.type.toUpperCase()}</div>
                            <div style="color: #6b7280;">${file.date}</div>
                            <div>
                                <button onclick="downloadFile('${file.name}')" style="background: #3b82f6; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px;" title="تحميل">📥</button>
                            </div>
                        </div>
                    `).join('');
                } else {
                    resultsContainer.style.display = 'none';
                    noResultsContainer.style.display = 'block';
                }
            } else {
                resultsContainer.style.display = 'none';
                noResultsContainer.style.display = 'none';
                startMessageContainer.style.display = 'block';
            }
        }

        // دالة تحميل الملف
        function downloadFile(fileName) {
            alert('تم بدء تحميل الملف: ' + fileName);
        }

        // دالة النشر
        function showPublishWindow() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            modalContent.innerHTML = `
                <div style="height: 90vh; background: white; border-radius: 20px; padding: 0; overflow: hidden; display: flex; flex-direction: column;">
                    <!-- الشريط العلوي -->
                    <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <svg style="width: 24px; height: 24px; color: #8b5cf6;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/>
                                <polyline points="12,13 12,7"/>
                                <polyline points="9,10 12,7 15,10"/>
                            </svg>
                            <h2 style="color: #4b5563; font-size: 20px; font-weight: bold; margin: 0;">نشر ملف جديد</h2>
                        </div>
                        <button onclick="closeModal()" style="background: none; border: none; color: #4b5563; font-size: 24px; cursor: pointer; padding: 8px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='none'">✕</button>
                    </div>

                    <!-- محتوى النافذة القابل للتمرير -->
                    <div id="publishContent" style="flex: 1; overflow-y: auto; padding: 25px;">
                        <!-- منطقة رفع الملف -->
                        <div style="margin-bottom: 30px;">
                            <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 15px;">اختيار الملف</h3>
                            <div id="fileDropZone" style="border: 3px dashed #8b5cf6; border-radius: 12px; padding: 40px; text-align: center; background: #faf5ff; cursor: pointer; transition: all 0.3s;" onclick="document.getElementById('fileInput').click()" ondragover="handleDragOver(event)" ondrop="handleFileDrop(event)">
                                <svg style="width: 48px; height: 48px; color: #8b5cf6; margin: 0 auto 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/>
                                    <polyline points="12,13 12,7"/>
                                    <polyline points="9,10 12,7 15,10"/>
                                </svg>
                                <p style="color: #8b5cf6; font-size: 16px; font-weight: 600; margin-bottom: 8px;">اسحب الملف هنا أو انقر للاختيار</p>
                                <p style="color: #6b7280; font-size: 14px;">يدعم: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (حد أقصى 10MB)</p>
                                <input type="file" id="fileInput" style="display: none;" accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" onchange="handleFileSelect(event)">
                            </div>
                            <div id="selectedFileInfo" style="display: none; margin-top: 15px; padding: 15px; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px;">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <svg style="width: 20px; height: 20px; color: #16a34a;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                    </svg>
                                    <div>
                                        <div id="fileName" style="font-weight: 600; color: #374151;"></div>
                                        <div id="fileSize" style="font-size: 12px; color: #6b7280;"></div>
                                    </div>
                                    <button onclick="removeSelectedFile()" style="margin-right: auto; background: #ef4444; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">إزالة</button>
                                </div>
                            </div>
                        </div>

                        <!-- معاينة الملف -->
                        <div id="filePreview" style="display: none; margin-bottom: 30px;">
                            <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 15px;">معاينة الملف</h3>
                            <div id="previewContainer" style="border: 2px solid #e5e7eb; border-radius: 12px; padding: 20px; background: #f9fafb; text-align: center; max-height: 500px; overflow-y: auto;">
                                <!-- سيتم إدراج المعاينة هنا -->
                            </div>
                        </div>

                        <!-- معلومات النشر -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                            <div>
                                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">عنوان الملف</label>
                                <input type="text" id="fileTitle" placeholder="أدخل عنوان الملف" style="width: 100%; padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                            </div>
                            <div>
                                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">القسم المستهدف</label>
                                <select id="targetDepartment" style="width: 100%; padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                    <option value="">اختر القسم</option>
                                    <option value="all">جميع الأقسام</option>
                                    <option value="المبيعات">المبيعات</option>
                                    <option value="الموارد البشرية">الموارد البشرية</option>
                                    <option value="المحاسبة">المحاسبة</option>
                                    <option value="التسويق">التسويق</option>
                                    <option value="التطوير">التطوير</option>
                                </select>
                            </div>
                        </div>

                        <!-- وصف الملف -->
                        <div style="margin-bottom: 30px;">
                            <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">وصف الملف (اختياري)</label>
                            <textarea id="fileDescription" placeholder="أدخل وصف مختصر للملف..." style="width: 100%; padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical; min-height: 80px;"></textarea>
                        </div>

                        <!-- نوع النشر -->
                        <div style="margin-bottom: 30px;">
                            <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 15px;">نوع النشر</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div style="border: 2px solid #d1d5db; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s;" onclick="selectPublishType('immediate')" id="immediatePublish">
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 10px;">
                                        <input type="radio" name="publishType" value="immediate" id="immediateRadio" style="width: 18px; height: 18px;">
                                        <label for="immediateRadio" style="font-weight: 600; color: #374151; cursor: pointer;">نشر فوري</label>
                                    </div>
                                    <p style="color: #6b7280; font-size: 14px; margin: 0;">سيتم نشر الملف فوراً بعد الضغط على زر النشر</p>
                                </div>
                                <div style="border: 2px solid #d1d5db; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s;" onclick="selectPublishType('scheduled')" id="scheduledPublish">
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 10px;">
                                        <input type="radio" name="publishType" value="scheduled" id="scheduledRadio" style="width: 18px; height: 18px;">
                                        <label for="scheduledRadio" style="font-weight: 600; color: #374151; cursor: pointer;">نشر مجدول</label>
                                    </div>
                                    <p style="color: #6b7280; font-size: 14px; margin: 0;">اختر تاريخ ووقت محدد للنشر</p>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات النشر المجدول -->
                        <div id="scheduleSettings" style="display: none; margin-bottom: 30px;">
                            <h4 style="color: #374151; font-size: 16px; font-weight: bold; margin-bottom: 15px;">إعدادات النشر المجدول</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div>
                                    <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">التاريخ</label>
                                    <input type="date" id="publishDate" style="width: 100%; padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                </div>
                                <div>
                                    <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">الوقت</label>
                                    <input type="time" id="publishTime" style="width: 100%; padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                </div>
                            </div>
                        </div>

                        <!-- أزرار العمل -->
                        <div style="display: flex; gap: 12px; justify-content: center; padding: 20px 0;">
                            <button onclick="publishFile()" id="publishBtn" style="background: #8b5cf6; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 16px; opacity: 0.5;" disabled>نشر الملف</button>
                            <button onclick="closeModal()" style="background: #6b7280; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 16px;">إلغاء</button>
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';

            // تعيين التاريخ والوقت الافتراضي
            const now = new Date();
            const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
            document.getElementById('publishDate').value = tomorrow.toISOString().split('T')[0];
            document.getElementById('publishTime').value = '09:00';

            // تحديد النشر الفوري افتراضياً
            selectPublishType('immediate');
        }

        // دوال التعامل مع الملفات في النشر
        let selectedFile = null;

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.borderColor = '#7c3aed';
            event.currentTarget.style.background = '#f3e8ff';
        }

        function handleFileDrop(event) {
            event.preventDefault();
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                processSelectedFile(files[0]);
            }
            event.currentTarget.style.borderColor = '#8b5cf6';
            event.currentTarget.style.background = '#faf5ff';
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processSelectedFile(file);
            }
        }

        function processSelectedFile(file) {
            // التحقق من نوع الملف
            const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'image/jpeg', 'image/jpg', 'image/png'];

            if (!allowedTypes.includes(file.type)) {
                alert('نوع الملف غير مدعوم. يرجى اختيار ملف PDF, DOC, DOCX, XLS, XLSX, JPG, أو PNG');
                return;
            }

            // التحقق من حجم الملف (10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى هو 10MB');
                return;
            }

            selectedFile = file;

            // عرض معلومات الملف
            document.getElementById('selectedFileInfo').style.display = 'block';
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);

            // تفعيل زر النشر
            const publishBtn = document.getElementById('publishBtn');
            publishBtn.style.opacity = '1';
            publishBtn.disabled = false;

            // ملء عنوان الملف تلقائياً
            if (!document.getElementById('fileTitle').value) {
                document.getElementById('fileTitle').value = file.name.split('.')[0];
            }

            // عرض معاينة الملف
            showFilePreview(file);
        }

        // دالة معاينة الملف
        function showFilePreview(file) {
            const previewDiv = document.getElementById('filePreview');
            const previewContainer = document.getElementById('previewContainer');

            previewDiv.style.display = 'block';

            if (file.type.startsWith('image/')) {
                // معاينة الصور
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewContainer.innerHTML = `
                        <img src="${e.target.result}" style="max-width: 100%; max-height: 400px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" alt="معاينة الصورة">
                        <p style="margin-top: 10px; color: #6b7280; font-size: 14px;">معاينة الصورة</p>
                    `;
                };
                reader.readAsDataURL(file);
            } else if (file.type === 'application/pdf') {
                // معاينة PDF
                previewContainer.innerHTML = `
                    <div style="background: #f3f4f6; padding: 40px; border-radius: 8px;">
                        <svg style="width: 64px; height: 64px; color: #ef4444; margin: 0 auto 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                        <h4 style="color: #374151; margin-bottom: 8px;">ملف PDF</h4>
                        <p style="color: #6b7280; font-size: 14px; margin-bottom: 16px;">سيتم عرض محتوى الملف بعد النشر</p>
                        <div style="background: white; padding: 20px; border-radius: 8px; margin: 16px 0; border: 1px solid #e5e7eb;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 600; color: #374151;">صفحة 1 من ${Math.floor(Math.random() * 20) + 1}</span>
                                <div style="display: flex; gap: 8px;">
                                    <button style="background: #6b7280; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px;">السابق</button>
                                    <button style="background: #6b7280; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px;">التالي</button>
                                </div>
                            </div>
                            <div style="background: #f9fafb; height: 300px; border: 1px solid #e5e7eb; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #6b7280;">
                                محتوى الصفحة الأولى من ملف PDF<br>
                                <small>سيتم عرض المحتوى الفعلي بعد النشر</small>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // معاينة الملفات الأخرى
                const fileIcon = getFileIcon(file.type);
                previewContainer.innerHTML = `
                    <div style="background: #f3f4f6; padding: 40px; border-radius: 8px;">
                        ${fileIcon}
                        <h4 style="color: #374151; margin-bottom: 8px;">${getFileTypeName(file.type)}</h4>
                        <p style="color: #6b7280; font-size: 14px;">سيتم عرض محتوى الملف بعد النشر</p>
                        <div style="background: white; padding: 20px; border-radius: 8px; margin: 16px 0; border: 1px solid #e5e7eb;">
                            <p style="color: #374151; font-weight: 600; margin-bottom: 8px;">معلومات الملف:</p>
                            <p style="color: #6b7280; font-size: 14px; margin: 4px 0;">الاسم: ${file.name}</p>
                            <p style="color: #6b7280; font-size: 14px; margin: 4px 0;">الحجم: ${formatFileSize(file.size)}</p>
                            <p style="color: #6b7280; font-size: 14px; margin: 4px 0;">النوع: ${getFileTypeName(file.type)}</p>
                        </div>
                    </div>
                `;
            }
        }

        // دوال مساعدة للمعاينة
        function getFileIcon(fileType) {
            if (fileType.includes('word')) {
                return '<svg style="width: 64px; height: 64px; color: #2563eb; margin: 0 auto 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/></svg>';
            } else if (fileType.includes('excel') || fileType.includes('sheet')) {
                return '<svg style="width: 64px; height: 64px; color: #16a34a; margin: 0 auto 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="8" y1="13" x2="16" y2="13"/><line x1="8" y1="17" x2="16" y2="17"/><line x1="8" y1="9" x2="16" y2="9"/></svg>';
            } else {
                return '<svg style="width: 64px; height: 64px; color: #6b7280; margin: 0 auto 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/></svg>';
            }
        }

        function getFileTypeName(fileType) {
            if (fileType.includes('pdf')) return 'ملف PDF';
            if (fileType.includes('word')) return 'مستند Word';
            if (fileType.includes('excel') || fileType.includes('sheet')) return 'جدول Excel';
            if (fileType.includes('image')) return 'صورة';
            return 'ملف';
        }

        function removeSelectedFile() {
            selectedFile = null;
            document.getElementById('selectedFileInfo').style.display = 'none';
            document.getElementById('fileInput').value = '';

            // تعطيل زر النشر
            const publishBtn = document.getElementById('publishBtn');
            publishBtn.style.opacity = '0.5';
            publishBtn.disabled = true;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // دوال التمرير
        function scrollToTop() {
            document.getElementById('publishContent').scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        function scrollToBottom() {
            const content = document.getElementById('publishContent');
            content.scrollTo({
                top: content.scrollHeight,
                behavior: 'smooth'
            });
        }

        // دالة اختيار نوع النشر
        function selectPublishType(type) {
            const immediateDiv = document.getElementById('immediatePublish');
            const scheduledDiv = document.getElementById('scheduledPublish');
            const scheduleSettings = document.getElementById('scheduleSettings');
            const immediateRadio = document.getElementById('immediateRadio');
            const scheduledRadio = document.getElementById('scheduledRadio');

            // إعادة تعيين الألوان
            immediateDiv.style.borderColor = '#d1d5db';
            immediateDiv.style.background = 'white';
            scheduledDiv.style.borderColor = '#d1d5db';
            scheduledDiv.style.background = 'white';

            if (type === 'immediate') {
                immediateDiv.style.borderColor = '#8b5cf6';
                immediateDiv.style.background = '#faf5ff';
                immediateRadio.checked = true;
                scheduleSettings.style.display = 'none';
            } else {
                scheduledDiv.style.borderColor = '#8b5cf6';
                scheduledDiv.style.background = '#faf5ff';
                scheduledRadio.checked = true;
                scheduleSettings.style.display = 'block';
            }
        }

        function publishFile() {
            if (!selectedFile) {
                alert('يرجى اختيار ملف أولاً');
                return;
            }

            const title = document.getElementById('fileTitle').value.trim();
            const department = document.getElementById('targetDepartment').value;
            const description = document.getElementById('fileDescription').value.trim();
            const publishType = document.querySelector('input[name="publishType"]:checked')?.value;

            if (!title) {
                alert('يرجى إدخال عنوان الملف');
                return;
            }

            if (!department) {
                alert('يرجى اختيار القسم المستهدف');
                return;
            }

            if (!publishType) {
                alert('يرجى اختيار نوع النشر (فوري أو مجدول)');
                return;
            }

            // إنشاء كائن الملف الجديد
            const newFile = {
                name: title,
                user: 'المستخدم الحالي', // يمكن تحديثه لاحقاً
                department: department,
                type: selectedFile.type.includes('pdf') ? 'pdf' :
                      selectedFile.type.includes('word') ? 'doc' :
                      selectedFile.type.includes('excel') || selectedFile.type.includes('sheet') ? 'excel' :
                      selectedFile.type.includes('image') ? 'image' : 'other',
                size: formatFileSize(selectedFile.size),
                description: description,
                publishType: publishType
            };

            if (publishType === 'scheduled') {
                const publishDate = document.getElementById('publishDate').value;
                const publishTime = document.getElementById('publishTime').value;

                if (!publishDate || !publishTime) {
                    alert('يرجى تحديد تاريخ ووقت النشر المجدول');
                    return;
                }

                const scheduledDateTime = new Date(publishDate + 'T' + publishTime);
                const now = new Date();

                if (scheduledDateTime <= now) {
                    alert('يجب أن يكون تاريخ ووقت النشر في المستقبل');
                    return;
                }

                newFile.scheduledDate = publishDate;
                newFile.scheduledTime = publishTime;
            }

            // حفظ الملف في النظام
            const publishBtn = document.getElementById('publishBtn');
            publishBtn.textContent = publishType === 'immediate' ? 'جاري النشر...' : 'جاري الجدولة...';
            publishBtn.disabled = true;

            setTimeout(() => {
                try {
                    const savedFile = storage.addFile(newFile);

                    let publishMessage = `تم نشر الملف بنجاح!

📄 اسم الملف: ${title}
🏢 القسم: ${department}
📊 الحجم: ${formatFileSize(selectedFile.size)}
🆔 معرف الملف: ${savedFile.id}
📅 تاريخ النشر: ${savedFile.date}
⏰ وقت النشر: ${savedFile.time}`;

                    if (publishType === 'scheduled') {
                        publishMessage += `
📋 نوع النشر: مجدول
📅 تاريخ النشر المجدول: ${newFile.scheduledDate}
⏰ وقت النشر المجدول: ${newFile.scheduledTime}`;
                    } else {
                        publishMessage += `
📋 نوع النشر: فوري`;
                    }

                    alert(publishMessage);

                    // إعادة تعيين النموذج
                    selectedFile = null;
                    closeModal();

                } catch (error) {
                    alert('حدث خطأ أثناء نشر الملف: ' + error.message);
                    publishBtn.textContent = 'نشر الملف';
                    publishBtn.disabled = false;
                }
            }, 2000);
        }

        // دالة المحذوفات
        function showDeletedFilesWindow() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            modalContent.innerHTML = `
                <div style="height: 85vh; background: white; border-radius: 20px; padding: 0; overflow: hidden; display: flex; flex-direction: column;">
                    <!-- الشريط العلوي -->
                    <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <svg style="width: 24px; height: 24px; color: #ef4444;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"/>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                                <line x1="10" y1="11" x2="10" y2="17"/>
                                <line x1="14" y1="11" x2="14" y2="17"/>
                            </svg>
                            <h2 style="color: #4b5563; font-size: 20px; font-weight: bold; margin: 0;">الملفات المحذوفة</h2>
                        </div>
                        <button onclick="closeModal()" style="background: none; border: none; color: #4b5563; font-size: 24px; cursor: pointer; padding: 8px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='none'">✕</button>
                    </div>

                    <!-- محتوى النافذة القابل للتمرير -->
                    <div style="flex: 1; overflow-y: auto; padding: 25px;">
                        <!-- ملاحظة الصلاحية -->
                        <div style="text-align: center; margin-bottom: 25px;">
                            <div style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 12px;">
                                <svg style="width: 24px; height: 24px; color: #3b82f6;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                                <p style="color: #3b82f6; font-weight: 600; margin: 0; font-size: 16px;">ملاحظة مهمة</p>
                            </div>
                            <p style="color: #3b82f6; margin: 0; font-size: 14px; line-height: 1.2; max-width: 800px; margin: 0 auto; white-space: nowrap;">
                                الملفات المحذوفة لها صلاحية <strong>30 يوماً</strong> فقط للاستعادة. بعد انتهاء هذه المدة سيتم حذفها نهائياً بشكل تلقائي من النظام.
                            </p>
                        </div>

                        <!-- شريط البحث -->
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 30px; justify-content: center;">
                            <input type="text" id="deletedSearchInput" placeholder="البحث في الملفات المحذوفة..."
                                   style="width: 600px; padding: 10px 20px; border: 2px solid #9ca3af; border-radius: 25px; font-size: 16px; outline: none;"
                                   onkeyup="searchDeletedFiles()">
                            <button onclick="searchDeletedFiles()" style="background: none; border: none; cursor: pointer; display: flex; align-items: center; justify-content: center; padding: 8px;">
                                <svg style="width: 32px; height: 32px; color: #ef4444;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/>
                                    <path d="M21 21l-4.35-4.35"/>
                                </svg>
                            </button>
                        </div>

                        <!-- جدول الملفات المحذوفة -->
                        <div id="deletedFilesContainer">
                            <div style="background: #f8f9fa; padding: 12px 16px; border-radius: 15px; border: 1px solid #d1d5db; display: grid; grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 1fr; gap: 16px; font-weight: bold; color: #374151; font-size: 14px; margin-bottom: 8px;">
                                <div>اسم الملف</div>
                                <div>المستخدم</div>
                                <div>القسم</div>
                                <div>تاريخ الحذف</div>
                                <div>الحجم</div>
                                <div>إجراءات</div>
                            </div>
                            <div id="deletedFilesBody" style="display: flex; flex-direction: column; gap: 8px; max-height: 500px; overflow-y: auto;">
                                <!-- سيتم إدراج الملفات المحذوفة هنا -->
                            </div>
                        </div>

                        <!-- رسالة عدم وجود ملفات محذوفة -->
                        <div id="noDeletedFiles" style="display: none; text-align: center; color: #6b7280; font-size: 16px; padding: 40px; background: #f9fafb; border-radius: 12px; border: 2px dashed #d1d5db;">
                            لا توجد ملفات محذوفة
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
            loadDeletedFiles();
        }

        // تحميل الملفات المحذوفة
        function loadDeletedFiles() {
            const data = storage.getData();
            const deletedFiles = data.deletedFiles;

            const container = document.getElementById('deletedFilesBody');
            const noFilesMsg = document.getElementById('noDeletedFiles');

            if (deletedFiles.length === 0) {
                document.getElementById('deletedFilesContainer').style.display = 'none';
                noFilesMsg.style.display = 'block';
            } else {
                document.getElementById('deletedFilesContainer').style.display = 'block';
                noFilesMsg.style.display = 'none';

                container.innerHTML = deletedFiles.map(file => `
                    <div id="deletedFile_${file.id}" style="background: white; padding: 12px 16px; border-radius: 12px; border: 2px solid #9ca3af; display: grid; grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 1fr; gap: 16px; align-items: center; font-size: 14px; transition: all 0.2s;" onmouseover="this.style.borderColor='#ef4444'; this.style.boxShadow='0 2px 8px rgba(239,68,68,0.2)'" onmouseout="this.style.borderColor='#9ca3af'; this.style.boxShadow='none'">
                        <div style="font-weight: 500; color: #374151;">${file.name}</div>
                        <div style="color: #6b7280;">${file.user}</div>
                        <div style="color: #6b7280;">${file.department}</div>
                        <div style="color: #6b7280;">${file.deleteDate}</div>
                        <div style="color: #6b7280;">${file.size}</div>
                        <div style="display: flex; gap: 8px;">
                            <button onclick="restoreFile(${file.id}, '${file.name}')" style="background: none; border: none; cursor: pointer; padding: 4px;" title="استعادة">
                                <svg style="width: 24px; height: 24px; color: #3b82f6;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="1,4 1,10 7,10"/>
                                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                                </svg>
                            </button>
                            <button onclick="permanentDelete(${file.id}, '${file.name}')" style="background: none; border: none; cursor: pointer; padding: 4px;" title="حذف نهائي">
                                <svg style="width: 24px; height: 24px; color: #6b7280;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="3,6 5,6 21,6"/>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                                    <line x1="10" y1="11" x2="10" y2="17"/>
                                    <line x1="14" y1="11" x2="14" y2="17"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                `).join('');
            }
        }

        // دوال التعامل مع الملفات المحذوفة
        function searchDeletedFiles() {
            const searchTerm = document.getElementById('deletedSearchInput').value.toLowerCase();
            const fileRows = document.querySelectorAll('[id^="deletedFile_"]');

            fileRows.forEach(row => {
                const fileName = row.querySelector('div').textContent.toLowerCase();
                if (fileName.includes(searchTerm)) {
                    row.style.display = 'grid';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function restoreFile(fileId, fileName) {
            if (confirm('هل تريد استعادة الملف: ' + fileName + '؟')) {
                try {
                    const restoredFile = storage.restoreFile(fileId);

                    if (restoredFile) {
                        const fileElement = document.getElementById('deletedFile_' + fileId);
                        fileElement.style.background = '#f0fdf4';
                        fileElement.style.borderColor = '#bbf7d0';

                        setTimeout(() => {
                            fileElement.remove();
                            alert(`✅ تم استعادة الملف بنجاح!

📄 اسم الملف: ${fileName}
🆔 معرف الملف: ${fileId}
📅 تاريخ الاستعادة: ${new Date().toLocaleDateString('ar-SA')}
⏰ وقت الاستعادة: ${new Date().toLocaleTimeString('ar-SA')}

الملف متاح الآن في قائمة الملفات الرئيسية.`);

                            // التحقق من وجود ملفات أخرى
                            const remainingFiles = document.querySelectorAll('[id^="deletedFile_"]');
                            if (remainingFiles.length === 0) {
                                document.getElementById('deletedFilesContainer').style.display = 'none';
                                document.getElementById('noDeletedFiles').style.display = 'block';
                            }
                        }, 500);
                    } else {
                        alert('حدث خطأ أثناء استعادة الملف');
                    }
                } catch (error) {
                    alert('حدث خطأ أثناء استعادة الملف: ' + error.message);
                }
            }
        }

        function permanentDelete(fileId, fileName) {
            if (confirm('تحذير: سيتم حذف الملف نهائياً ولن يمكن استعادته!\\n\\nهل تريد المتابعة؟\\n\\nالملف: ' + fileName)) {
                const fileElement = document.getElementById('deletedFile_' + fileId);
                fileElement.style.background = '#fef2f2';
                fileElement.style.borderColor = '#fecaca';
                fileElement.style.opacity = '0.5';

                setTimeout(() => {
                    fileElement.remove();
                    alert('تم حذف الملف نهائياً: ' + fileName);

                    // التحقق من وجود ملفات أخرى
                    const remainingFiles = document.querySelectorAll('[id^="deletedFile_"]');
                    if (remainingFiles.length === 0) {
                        document.getElementById('deletedFilesContainer').style.display = 'none';
                        document.getElementById('noDeletedFiles').style.display = 'block';
                    }
                }, 500);
            }
        }



        // دالة شاشات العرض
        function showDisplaysWindow() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            modalContent.innerHTML = `
                <div style="height: 90vh; background: white; border-radius: 20px; padding: 0; overflow: hidden; display: flex; flex-direction: column;">
                    <!-- الشريط العلوي -->
                    <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <svg style="width: 24px; height: 24px; color: #6366f1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                <line x1="8" y1="21" x2="16" y2="21"/>
                                <line x1="12" y1="17" x2="12" y2="21"/>
                            </svg>
                            <h2 style="color: #4b5563; font-size: 20px; font-weight: bold; margin: 0;">إدارة شاشات العرض</h2>
                        </div>
                        <button onclick="closeModal()" style="background: none; border: none; color: #4b5563; font-size: 24px; cursor: pointer; padding: 8px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='none'">✕</button>
                    </div>

                    <!-- محتوى النافذة القابل للتمرير -->
                    <div style="flex: 1; overflow-y: auto; padding: 25px;">
                        <!-- إحصائيات سريعة -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                            <div style="background: rgba(16, 185, 129, 0.15); color: #059669; border: 2px solid #10b981; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3); transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(59, 130, 246, 0.3)'">
                                <div style="font-size: 24px; font-weight: bold;">3</div>
                                <div style="font-size: 14px; opacity: 0.8;">شاشات متصلة</div>
                            </div>
                            <div style="background: rgba(107, 114, 128, 0.15); color: #4b5563; border: 2px solid #6b7280; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3); transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(59, 130, 246, 0.3)'">
                                <div style="font-size: 24px; font-weight: bold;">1</div>
                                <div style="font-size: 14px; opacity: 0.8;">شاشات غير متصلة</div>
                            </div>
                            <div style="background: rgba(239, 68, 68, 0.15); color: #dc2626; border: 2px solid #ef4444; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3); transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(59, 130, 246, 0.3)'">
                                <div style="font-size: 24px; font-weight: bold;">1</div>
                                <div style="font-size: 14px; opacity: 0.8;">شاشات منقطعة</div>
                            </div>
                            <div style="background: rgba(99, 102, 241, 0.15); color: #4f46e5; border: 2px solid #6366f1; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3); transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(59, 130, 246, 0.3)'">
                                <div style="font-size: 24px; font-weight: bold;">5</div>
                                <div style="font-size: 14px; opacity: 0.8;">إجمالي الشاشات</div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div style="display: flex; gap: 12px; margin-bottom: 30px;">
                            <button onclick="showAddDisplayModal()" style="background: #6366f1; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                                <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="12" y1="5" x2="12" y2="19"/>
                                    <line x1="5" y1="12" x2="19" y2="12"/>
                                </svg>
                                إضافة شاشة جديدة
                            </button>
                            <button onclick="refreshDisplays()" style="background: #10b981; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                                <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23,4 23,10 17,10"/>
                                    <polyline points="1,20 1,14 7,14"/>
                                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                                </svg>
                                تحديث الحالة
                            </button>
                        </div>

                        <!-- جدول الشاشات -->
                        <div style="background: #f8f9fa; padding: 12px 16px; border-radius: 15px; border: 1px solid #d1d5db; display: grid; grid-template-columns: 2fr 1.5fr 1.5fr 1fr 2fr; gap: 16px; font-weight: bold; color: #374151; font-size: 14px; margin-bottom: 8px;">
                            <div>اسم الشاشة</div>
                            <div>الموقع</div>
                            <div>الحالة</div>
                            <div>آخر اتصال</div>
                            <div>تفاصيل الحالة</div>
                        </div>
                        <div id="displaysBody" style="display: flex; flex-direction: column; gap: 8px;">
                            <!-- سيتم إدراج الشاشات هنا -->
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
            loadDisplays();
        }

        // تحميل الشاشات
        function loadDisplays() {
            const data = storage.getData();
            const displays = data.displays;

            const container = document.getElementById('displaysBody');
            container.innerHTML = displays.map(display => {
                let borderStyle = '';
                let statusColor = '';
                let statusText = '';

                if (display.status === 'connected') {
                    borderStyle = '1px solid rgba(16, 185, 129, 0.6)';
                    statusColor = 'background: rgba(16, 185, 129, 0.15); color: #059669;';
                    statusText = '🟢 متصلة';
                } else if (display.status === 'disconnected') {
                    borderStyle = '1px solid rgba(107, 114, 128, 0.6)';
                    statusColor = 'background: rgba(107, 114, 128, 0.15); color: #4b5563;';
                    statusText = '⚫ غير متصلة';
                } else if (display.status === 'error') {
                    borderStyle = '1px solid rgba(239, 68, 68, 0.6)';
                    statusColor = 'background: rgba(239, 68, 68, 0.15); color: #dc2626;';
                    statusText = '🔴 منقطعة';
                }

                return `
                    <div id="display_${display.id}" style="background: rgba(255, 255, 255, 0.7); padding: 16px; border-radius: 12px; border: ${borderStyle}; display: grid; grid-template-columns: 2fr 1.5fr 1.5fr 1fr 2fr; gap: 16px; align-items: center; font-size: 14px; transition: all 0.2s; margin-bottom: 12px;">
                        <div style="font-weight: 600; color: #374151;">${display.name}</div>
                        <div style="color: #6b7280;">${display.location}</div>
                        <div>
                            <span style="padding: 6px 12px; border-radius: 12px; font-size: 12px; font-weight: 600; ${statusColor}">
                                ${statusText}
                            </span>
                        </div>
                        <div style="color: #6b7280; font-size: 12px;">${display.lastConnection}</div>
                        <div style="color: #374151; font-size: 13px;">
                            <div style="font-weight: 500; margin-bottom: 2px;">${display.details}</div>
                            ${display.reason ? `<div style="color: #ef4444; font-size: 11px; font-style: italic;">السبب: ${display.reason}</div>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // دوال التعامل مع شاشات العرض
        function showAddDisplayModal() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            modalContent.innerHTML = `
                <div style="height: 90vh; background: white; border-radius: 20px; padding: 0; overflow: hidden; display: flex; flex-direction: column;">
                    <!-- الشريط العلوي -->
                    <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <svg style="width: 24px; height: 24px; color: #6366f1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                <line x1="8" y1="21" x2="16" y2="21"/>
                                <line x1="12" y1="17" x2="12" y2="21"/>
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                            <h2 style="color: #4b5563; font-size: 20px; font-weight: bold; margin: 0;">إضافة شاشة عرض جديدة</h2>
                        </div>
                        <button onclick="closeModal()" style="background: none; border: none; color: #4b5563; font-size: 24px; cursor: pointer; padding: 8px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='none'">✕</button>
                    </div>

                    <!-- محتوى النافذة القابل للتمرير -->
                    <div style="flex: 1; overflow-y: auto; padding: 25px;">
                        <!-- معلومات الشاشة -->
                        <div style="margin-bottom: 30px;">
                            <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">معلومات الشاشة</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                                <div>
                                    <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">اسم الشاشة</label>
                                    <input type="text" id="displayName" placeholder="مثال: شاشة قسم المبيعات" style="width: 100%; padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                </div>
                                <div>
                                    <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">الموقع</label>
                                    <input type="text" id="displayLocation" placeholder="مثال: الطابق الثاني - مكتب 205" style="width: 100%; padding: 12px; border: 2px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                </div>
                            </div>
                        </div>

                        <!-- طرق الربط -->
                        <div style="margin-bottom: 30px;">
                            <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">طريقة الربط</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <!-- الربط عبر WiFi -->
                                <div onclick="selectConnectionType('wifi')" id="wifiOption" style="border: 3px solid #3b82f6; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s; background: #eff6ff;">
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                                        <input type="radio" name="connectionType" value="wifi" checked style="width: 18px; height: 18px;">
                                        <h4 style="color: #1e40af; font-weight: 600; margin: 0;">الربط عبر WiFi</h4>
                                    </div>
                                    <p style="color: #6b7280; font-size: 13px; margin: 0;">للشاشات الذكية الحديثة التي تدعم الاتصال اللاسلكي</p>
                                </div>

                                <!-- الربط عبر كابل -->
                                <div onclick="selectConnectionType('cable')" id="cableOption" style="border: 2px solid #d1d5db; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s; background: white;">
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                                        <input type="radio" name="connectionType" value="cable" style="width: 18px; height: 18px;">
                                        <h4 style="color: #374151; font-weight: 600; margin: 0;">الربط عبر كابل</h4>
                                    </div>
                                    <p style="color: #6b7280; font-size: 13px; margin: 0;">للشاشات التقليدية باستخدام كابل HDMI أو USB</p>
                                </div>
                            </div>
                        </div>

                        <!-- خطوات الربط -->
                        <div style="margin-bottom: 30px;">
                            <h3 style="color: #374151; font-size: 18px; font-weight: bold; margin-bottom: 20px;">خطوات الربط</h3>
                            <div id="connectionSteps" style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                <!-- خطوات WiFi (افتراضية) -->
                                <div id="wifiSteps">
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                                        <div style="background: #3b82f6; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">1</div>
                                        <span style="color: #374151; font-weight: 500;">تأكد من أن الشاشة متصلة بنفس شبكة WiFi</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                                        <div style="background: #3b82f6; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">2</div>
                                        <span style="color: #374151; font-weight: 500;">افتح تطبيق العرض على الشاشة الذكية</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                                        <div style="background: #3b82f6; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">3</div>
                                        <span style="color: #374151; font-weight: 500;">أدخل كود الربط: <strong style="background: #fef3c7; padding: 2px 6px; border-radius: 4px; color: #92400e;">TV-${Math.floor(Math.random() * 10000)}</strong></span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div style="background: #3b82f6; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">4</div>
                                        <span style="color: #374151; font-weight: 500;">انقر على "ربط الشاشة" أدناه لإكمال العملية</span>
                                    </div>
                                </div>

                                <!-- خطوات الكابل (مخفية) -->
                                <div id="cableSteps" style="display: none;">
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                                        <div style="background: #6b7280; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">1</div>
                                        <span style="color: #374151; font-weight: 500;">وصل الشاشة بالكمبيوتر عبر كابل HDMI</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                                        <div style="background: #6b7280; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">2</div>
                                        <span style="color: #374151; font-weight: 500;">تأكد من تشغيل الشاشة وضبطها على المدخل الصحيح</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                                        <div style="background: #6b7280; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">3</div>
                                        <span style="color: #374151; font-weight: 500;">انقر على "ربط الشاشة" أدناه للكشف التلقائي</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار العمل -->
                        <div style="display: flex; gap: 12px; justify-content: center; padding: 20px 0;">
                            <button onclick="connectDisplay()" style="background: #3b82f6; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 16px;">ربط الشاشة</button>
                            <button onclick="closeModal()" style="background: #6b7280; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 16px;">إلغاء</button>
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
        }

        // دالة اختيار نوع الاتصال
        function selectConnectionType(type) {
            const wifiOption = document.getElementById('wifiOption');
            const cableOption = document.getElementById('cableOption');
            const wifiSteps = document.getElementById('wifiSteps');
            const cableSteps = document.getElementById('cableSteps');
            const wifiRadio = document.querySelector('input[value="wifi"]');
            const cableRadio = document.querySelector('input[value="cable"]');

            // إعادة تعيين الألوان
            wifiOption.style.borderColor = '#d1d5db';
            wifiOption.style.background = 'white';
            cableOption.style.borderColor = '#d1d5db';
            cableOption.style.background = 'white';

            if (type === 'wifi') {
                wifiOption.style.borderColor = '#3b82f6';
                wifiOption.style.background = '#eff6ff';
                wifiRadio.checked = true;
                wifiSteps.style.display = 'block';
                cableSteps.style.display = 'none';
            } else {
                cableOption.style.borderColor = '#6b7280';
                cableOption.style.background = '#f9fafb';
                cableRadio.checked = true;
                wifiSteps.style.display = 'none';
                cableSteps.style.display = 'block';
            }
        }

        // دالة ربط الشاشة
        function connectDisplay() {
            const displayName = document.getElementById('displayName').value.trim();
            const displayLocation = document.getElementById('displayLocation').value.trim();
            const connectionType = document.querySelector('input[name="connectionType"]:checked').value;

            if (!displayName) {
                alert('يرجى إدخال اسم الشاشة');
                return;
            }

            if (!displayLocation) {
                alert('يرجى إدخال موقع الشاشة');
                return;
            }

            // إنشاء كائن الشاشة الجديدة
            const newDisplay = {
                name: displayName,
                location: displayLocation,
                connectionType: connectionType
            };

            // عملية الربط الحقيقية
            const connectBtn = event.target;
            connectBtn.textContent = 'جاري الربط...';
            connectBtn.disabled = true;

            setTimeout(() => {
                try {
                    const savedDisplay = storage.addDisplay(newDisplay);

                    const connectionMethod = connectionType === 'wifi' ? 'WiFi' : 'كابل HDMI';
                    const connectionCode = connectionType === 'wifi' ? `TV-${Math.floor(Math.random() * 10000)}` : 'تم الكشف تلقائياً';

                    alert(`✅ تم ربط الشاشة بنجاح!

📺 اسم الشاشة: ${displayName}
📍 الموقع: ${displayLocation}
🔗 طريقة الربط: ${connectionMethod}
🆔 معرف الشاشة: ${savedDisplay.id}
📅 تاريخ الربط: ${savedDisplay.lastConnection}
${connectionType === 'wifi' ? `🔑 كود الربط: ${connectionCode}` : ''}

الشاشة الآن متصلة وجاهزة للاستخدام.
يمكنك إرسال الملفات إليها من قائمة إدارة الشاشات.`);

                    closeModal();

                    // إعادة تحميل قائمة الشاشات إذا كانت مفتوحة
                    setTimeout(() => {
                        if (document.getElementById('displaysBody')) {
                            loadDisplays();
                        }
                    }, 500);

                } catch (error) {
                    alert('حدث خطأ أثناء ربط الشاشة: ' + error.message);
                    connectBtn.textContent = 'ربط الشاشة';
                    connectBtn.disabled = false;
                }
            }, 3000);
        }

        function refreshDisplays() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<svg style="width: 16px; height: 16px; animation: spin 1s linear infinite;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="23,4 23,10 17,10"/><polyline points="1,20 1,14 7,14"/><path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/></svg> جاري التحديث...';

            setTimeout(() => {
                button.innerHTML = originalText;
                loadDisplays();
                alert('تم تحديث حالة الشاشات\\n\\n• تم فحص جميع الاتصالات\\n• تم تحديث حالة الشاشات\\n• تم التحقق من أسباب الانقطاع');
            }, 2000);
        }

        // إضافة CSS للأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // دوال التحكم في القائمة المنسدلة
        function toggleDropdown() {
            const dropdown = document.getElementById('dropdownContent');
            if (dropdown.style.display === 'block') {
                dropdown.style.display = 'none';
            } else {
                dropdown.style.display = 'block';
            }
        }

        function closeDropdown() {
            document.getElementById('dropdownContent').style.display = 'none';
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('mainDropdown');
            if (!dropdown.contains(event.target)) {
                closeDropdown();
            }
        });

        // دالة نافذة اللغة (محذوفة - استخدم الدالة الجديدة)
        function showLanguageModalOld() {
            const modal = document.getElementById('modal');
            const modalContent = modal.querySelector('.modal-content');

            modalContent.innerHTML = `
                <div style="height: auto; background: white; border-radius: 20px; padding: 0; overflow: hidden; max-width: 400px; margin: auto;">
                    <!-- الشريط العلوي -->
                    <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <svg style="width: 24px; height: 24px; color: #6366f1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="2" y1="12" x2="22" y2="12"/>
                                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                            </svg>
                            <h2 style="color: #4b5563; font-size: 20px; font-weight: bold; margin: 0;">اختيار اللغة</h2>
                        </div>
                        <button onclick="closeModal()" style="background: none; border: none; color: #4b5563; font-size: 24px; cursor: pointer; padding: 8px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='none'">✕</button>
                    </div>

                    <!-- محتوى النافذة -->
                    <div style="padding: 30px;">
                        <div style="display: flex; flex-direction: column; gap: 16px;">
                            <!-- اللغة العربية -->
                            <div onclick="selectLanguage('ar')" style="border: 3px solid #3b82f6; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s; background: #eff6ff; display: flex; align-items: center; gap: 16px;">
                                <input type="radio" name="language" value="ar" checked style="width: 20px; height: 20px;">
                                <div style="flex: 1;">
                                    <div style="font-size: 18px; font-weight: 600; color: #1e40af; margin-bottom: 4px;">العربية</div>
                                    <div style="font-size: 14px; color: #6b7280;">Arabic</div>
                                </div>
                                <div style="font-size: 24px;">🇸🇦</div>
                            </div>

                            <!-- اللغة الإنجليزية -->
                            <div onclick="selectLanguage('en')" style="border: 2px solid #d1d5db; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s; background: white; display: flex; align-items: center; gap: 16px;">
                                <input type="radio" name="language" value="en" style="width: 20px; height: 20px;">
                                <div style="flex: 1;">
                                    <div style="font-size: 18px; font-weight: 600; color: #374151; margin-bottom: 4px;">English</div>
                                    <div style="font-size: 14px; color: #6b7280;">الإنجليزية</div>
                                </div>
                                <div style="font-size: 24px;">🇺🇸</div>
                            </div>
                        </div>

                        <!-- زر التطبيق -->
                        <div style="text-align: center; margin-top: 30px;">
                            <button onclick="applyLanguage()" style="background: #3b82f6; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 16px; transition: all 0.3s;" onmouseover="this.style.background='#2563eb'" onmouseout="this.style.background='#3b82f6'">
                                تطبيق التغيير
                            </button>
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
        }

        // دالة اختيار اللغة
        function selectLanguage(lang) {
            // إعادة تعيين جميع الخيارات
            const allOptions = document.querySelectorAll('[onclick^="selectLanguage"]');
            allOptions.forEach(option => {
                option.style.borderColor = '#d1d5db';
                option.style.background = 'white';
                const radio = option.querySelector('input[type="radio"]');
                radio.checked = false;
            });

            // تفعيل الخيار المختار
            const selectedOption = document.querySelector(`[onclick="selectLanguage('${lang}')"]`);
            selectedOption.style.borderColor = '#3b82f6';
            selectedOption.style.background = '#eff6ff';
            const radio = selectedOption.querySelector('input[type="radio"]');
            radio.checked = true;
        }

        // دالة تطبيق اللغة
        function applyLanguage() {
            const selectedLang = document.querySelector('input[name="language"]:checked').value;
            if (selectedLang === 'ar') {
                alert('تم تطبيق اللغة العربية بنجاح!');
            } else {
                alert('English language applied successfully!');
            }
            closeModal();
        }

        // Close modal when clicking outside
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // ===== نظام إدارة المستخدمين المحسن =====

        // إضافة مستخدم جديد محسن
        function showAddUserForm() {
            const modal = document.getElementById('addUserModal');
            if (!modal) {
                // إنشاء نافذة إضافة مستخدم جديد
                const modalHTML = `
                    <div id="addUserModal" class="modal" style="display: none;">
                        <div class="modal-content" style="max-width: 600px;">
                            <div class="modal-header">
                                <h3>🆕 إضافة مستخدم جديد</h3>
                                <button onclick="closeAddUserModal()" class="close-btn">✕</button>
                            </div>
                            <div class="modal-body">
                                <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                                    <h4 style="margin: 0 0 10px 0; color: #0c4a6e;">📋 تعليمات إضافة مستخدم جديد:</h4>
                                    <ul style="margin: 0; padding-right: 20px; color: #0c4a6e; font-size: 14px;">
                                        <li>أدخل بيانات المستخدم بدقة</li>
                                        <li>سيتم إرسال بيانات تسجيل الدخول لإيميل المستخدم</li>
                                        <li>سيُطلب من المستخدم تغيير كلمة المرور في أول دخول</li>
                                        <li>اختر نوع الفرع حسب صلاحيات المستخدم</li>
                                    </ul>
                                </div>

                                <form onsubmit="addNewUser(event)" style="display: grid; gap: 20px;">
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">الاسم الأول *</label>
                                            <input type="text" id="newUserFirstName" required style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px;" placeholder="مثال: عامر">
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">الاسم الأخير *</label>
                                            <input type="text" id="newUserLastName" required style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px;" placeholder="مثال: سالم">
                                        </div>
                                    </div>

                                    <div>
                                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">البريد الإلكتروني *</label>
                                        <input type="email" id="newUserEmail" required style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px;" placeholder="<EMAIL>">
                                        <small style="color: #6b7280; font-size: 12px;">سيتم إرسال بيانات تسجيل الدخول لهذا الإيميل</small>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">المنصب *</label>
                                            <input type="text" id="newUserPosition" required style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px;" placeholder="مدير الميكانيك">
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">القسم *</label>
                                            <select id="newUserDepartment" required style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px;">
                                                <option value="">اختر القسم</option>
                                                <option value="الإدارة العامة">الإدارة العامة</option>
                                                <option value="قسم المبيعات">قسم المبيعات</option>
                                                <option value="قسم الجودة">قسم الجودة</option>
                                                <option value="قسم الميكانيك">قسم الميكانيك</option>
                                                <option value="قسم الكهرباء">قسم الكهرباء</option>
                                                <option value="قسم الأمان">قسم الأمان</option>
                                                <option value="قسم الموارد البشرية">قسم الموارد البشرية</option>
                                                <option value="قسم المحاسبة">قسم المحاسبة</option>
                                                <option value="قسم تقنية المعلومات">قسم تقنية المعلومات</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">رقم الهاتف</label>
                                            <input type="tel" id="newUserPhone" style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px;" placeholder="+964 xxx xxxx">
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">نوع الفرع *</label>
                                            <select id="newUserBranch" required style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px;">
                                                <option value="">اختر نوع الفرع</option>
                                                <option value="A">A - الإدارة العامة (صلاحيات كاملة)</option>
                                                <option value="B">B - فرع المبيعات</option>
                                                <option value="C">C - فرع الجودة</option>
                                                <option value="D">D - شاشة العرض (عرض فقط)</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div>
                                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">كلمة المرور المؤقتة *</label>
                                        <div style="display: flex; gap: 10px; align-items: center;">
                                            <input type="text" id="newUserPassword" required style="flex: 1; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px;" placeholder="كلمة مرور مؤقتة">
                                            <button type="button" onclick="generateRandomPassword()" style="padding: 12px 15px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px;">توليد تلقائي</button>
                                        </div>
                                        <small style="color: #ef4444; font-size: 12px;">⚠️ سيُطلب من المستخدم تغيير كلمة المرور في أول تسجيل دخول</small>
                                    </div>

                                    <div style="display: flex; align-items: center; gap: 10px; padding: 15px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px;">
                                        <input type="checkbox" id="sendEmailNotification" checked>
                                        <label for="sendEmailNotification" style="margin: 0; font-size: 14px; color: #0c4a6e;">📧 إرسال بيانات تسجيل الدخول عبر الإيميل</label>
                                    </div>

                                    <div style="display: flex; gap: 10px; justify-content: flex-end; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                                        <button type="button" onclick="closeAddUserModal()" style="padding: 12px 24px; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px;">إلغاء</button>
                                        <button type="submit" style="padding: 12px 24px; background: #10b981; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600;">✅ إضافة المستخدم</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', modalHTML);
            }

            document.getElementById('addUserModal').style.display = 'flex';
        }

        // إغلاق نافذة إضافة مستخدم
        function closeAddUserModal() {
            document.getElementById('addUserModal').style.display = 'none';
        }

        // توليد كلمة مرور عشوائية
        function generateRandomPassword() {
            const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
            let password = '';
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('newUserPassword').value = password;
        }

        // إضافة مستخدم جديد
        async function addNewUser(event) {
            event.preventDefault();

            const firstName = document.getElementById('newUserFirstName').value;
            const lastName = document.getElementById('newUserLastName').value;
            const email = document.getElementById('newUserEmail').value;
            const position = document.getElementById('newUserPosition').value;
            const department = document.getElementById('newUserDepartment').value;
            const phone = document.getElementById('newUserPhone').value || '+964 xxx xxxx';
            const branch = document.getElementById('newUserBranch').value;
            const tempPassword = document.getElementById('newUserPassword').value;
            const sendEmail = document.getElementById('sendEmailNotification').checked;

            // التحقق من البيانات
            if (!firstName || !lastName || !email || !position || !department || !branch || !tempPassword) {
                alert('❌ يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // التحقق من عدم وجود الإيميل مسبقاً
            const users = JSON.parse(localStorage.getItem('systemUsers'));
            if (users[email]) {
                alert('❌ هذا البريد الإلكتروني مسجل مسبقاً في النظام');
                return;
            }

            // إنشاء بيانات المستخدم الجديد
            const newUser = {
                email: email,
                password: tempPassword,
                firstName: firstName,
                lastName: lastName,
                position: position,
                department: department,
                phone: phone,
                role: branch === 'A' ? 'admin' : 'user',
                branch: branch,
                joinDate: new Date().toLocaleDateString('ar-SA'),
                avatar: null,
                isActive: true,
                mustChangePassword: true, // إجباري تغيير كلمة المرور
                createdBy: localStorage.getItem('currentUser'),
                createdDate: new Date().toISOString()
            };

            // إضافة المستخدم لقاعدة البيانات
            users[email] = newUser;
            localStorage.setItem('systemUsers', JSON.stringify(users));

            // إرسال إيميل إذا كان مطلوباً
            if (sendEmail) {
                try {
                    const fullName = `${firstName} ${lastName}`;
                    const emailResult = await emailService.sendWelcomeEmail(email, fullName, tempPassword);

                    if (emailResult.success) {
                        alert(`✅ تم إضافة المستخدم بنجاح!\n\n👤 المستخدم: ${fullName}\n📧 الإيميل: ${email}\n🔑 كلمة المرور: ${tempPassword}\n\n📨 تم إرسال بيانات تسجيل الدخول للإيميل المحدد.`);
                    } else {
                        alert(`✅ تم إضافة المستخدم بنجاح!\n\n👤 المستخدم: ${fullName}\n📧 الإيميل: ${email}\n🔑 كلمة المرور: ${tempPassword}\n\n⚠️ فشل إرسال الإيميل - يرجى إرسال البيانات يدوياً للمستخدم.`);
                    }
                } catch (error) {
                    alert(`✅ تم إضافة المستخدم بنجاح!\n\n👤 المستخدم: ${fullName}\n📧 الإيميل: ${email}\n🔑 كلمة المرور: ${tempPassword}\n\n⚠️ فشل إرسال الإيميل - يرجى إرسال البيانات يدوياً للمستخدم.`);
                }
            } else {
                const fullName = `${firstName} ${lastName}`;
                alert(`✅ تم إضافة المستخدم بنجاح!\n\n👤 المستخدم: ${fullName}\n📧 الإيميل: ${email}\n🔑 كلمة المرور: ${tempPassword}\n\n📝 يرجى إرسال هذه البيانات للمستخدم ليتمكن من تسجيل الدخول.`);
            }

            // إغلاق النافذة وتحديث قائمة المستخدمين
            closeAddUserModal();
            showUserManagement(); // إعادة تحميل قائمة المستخدمين
        }
    </script>
</body>
</html>