<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TV-Office - نظام إدارة التلفزيونات المكتبية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9fafb;
            direction: rtl;
            overflow-x: hidden;
        }

        /* تحسينات سطح المكتب */
        @media (min-width: 1024px) {
            .container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 0 20px;
            }

            .home-cards {
                grid-template-columns: repeat(4, 1fr) !important;
                gap: 30px !important;
                padding: 40px 20px !important;
            }

            .home-card {
                min-height: 200px;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .home-card:hover {
                transform: translateY(-8px);
                box-shadow: 0 12px 30px rgba(0,0,0,0.15);
            }

            .modal-content {
                max-width: 1200px !important;
                margin: 0 auto;
            }
        }

        /* تحسينات التابلت */
        @media (min-width: 768px) and (max-width: 1023px) {
            .home-cards {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 25px !important;
                padding: 30px 20px !important;
            }

            .home-card {
                min-height: 180px;
                padding: 25px !important;
            }

            .card-icon {
                width: 70px !important;
                height: 70px !important;
            }

            .icon-lg {
                width: 35px !important;
                height: 35px !important;
            }

            .card-title {
                font-size: 20px !important;
            }

            .modal-content {
                max-width: 90% !important;
                margin: 20px;
            }

            /* تحسين الأزرار للتابلت */
            button {
                min-height: 44px;
                font-size: 16px;
            }

            input, select, textarea {
                min-height: 44px;
                font-size: 16px;
            }
        }

        /* تحسينات الهاتف */
        @media (max-width: 767px) {
            .home-cards {
                grid-template-columns: 1fr !important;
                gap: 20px !important;
                padding: 20px 15px !important;
            }

            .header {
                padding: 15px 20px !important;
            }

            .modal-content {
                max-width: 95% !important;
                margin: 10px;
            }
        }

        .redirect-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
            margin: 0 auto;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .logo-icon svg {
            width: 40px;
            height: 40px;
        }

        h1 {
            color: #2d3748;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        p {
            color: #718096;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .redirect-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .redirect-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .countdown {
            color: #667eea;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                <line x1="8" y1="21" x2="16" y2="21"/>
                <line x1="12" y1="17" x2="12" y2="21"/>
                <circle cx="12" cy="10" r="3"/>
            </svg>
        </div>

        <h1>TV-Office</h1>
        <p>مرحباً بك في نظام إدارة التلفزيونات المكتبية!<br>
        سيتم توجيهك تلقائياً إلى النظام خلال ثوانٍ...</p>

        <a href="demo.html" class="redirect-btn">
            🚀 الدخول إلى النظام الآن
        </a>

        <div class="countdown" id="countdown">
            سيتم التوجيه التلقائي خلال <span id="timer">5</span> ثوانٍ
        </div>
    </div>

    <script>
        // العد التنازلي والتوجيه التلقائي
        let timeLeft = 5;
        const timerElement = document.getElementById('timer');

        const countdown = setInterval(() => {
            timeLeft--;
            timerElement.textContent = timeLeft;

            if (timeLeft <= 0) {
                clearInterval(countdown);
                window.location.href = 'demo.html';
            }
        }, 1000);
    </script>
</body>
</html>
