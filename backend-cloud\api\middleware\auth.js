const jwt = require('jsonwebtoken');
const { supabase } = require('../config/supabase');

const JWT_SECRET = process.env.JWT_SECRET || 'tv-office-super-secret-jwt-key-2024';

// Middleware للتحقق من صحة الرمز المميز
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ message: 'رمز الوصول مطلوب' });
    }

    // التحقق من صحة الرمز المميز
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // جلب بيانات المستخدم من قاعدة البيانات
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', decoded.userId)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      return res.status(401).json({ message: 'رمز الوصول غير صالح' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({ message: 'رمز الوصول غير صالح' });
  }
};

// Middleware للتحقق من الصلاحيات
const authorizeRoles = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: 'يجب تسجيل الدخول أولاً' });
    }

    if (!roles.includes(req.user.user_type)) {
      return res.status(403).json({ message: 'ليس لديك صلاحية للوصول لهذه الخدمة' });
    }

    next();
  };
};

module.exports = {
  authenticateToken,
  authorizeRoles
};
