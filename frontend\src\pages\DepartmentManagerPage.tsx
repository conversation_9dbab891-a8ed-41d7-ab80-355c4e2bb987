import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Trash2,
  LogOut,
  User,
  Bell,
  FileText,
  Download,
  Eye
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import socketService from '@/services/socket';
import toast from 'react-hot-toast';

const DepartmentManagerPage: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [newFileNotification, setNewFileNotification] = useState<any>(null);
  const [showFileViewer, setShowFileViewer] = useState(false);

  useEffect(() => {
    // Connect to socket for receiving files
    const socket = socketService.connect();
    
    if (socket) {
      // Listen for new files
      socket.on('newFilePublished', (data) => {
        setNewFileNotification(data);
        setShowFileViewer(true);
        
        // Play notification sound
        const audio = new Audio('/notification.mp3');
        audio.play().catch(() => {
          // Fallback if audio fails
          console.log('New file notification received');
        });
        
        toast.success('تم استلام ملف جديد!', {
          duration: 5000,
          icon: '📄'
        });
      });
    }

    return () => {
      if (socket) {
        socket.off('newFilePublished');
      }
    };
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('تم تسجيل الخروج بنجاح');
    } catch (error) {
      toast.error('خطأ في تسجيل الخروج');
    }
  };

  const handleCloseFile = () => {
    setShowFileViewer(false);
    // Auto-save to clipboard
    if (newFileNotification) {
      toast.success('تم حفظ الملف في الحافظة تلقائياً');
    }
  };

  const deptManagerCards = [
    {
      id: 'search',
      title: 'البحث',
      icon: Search,
      color: 'bg-green-500',
      description: 'البحث في الملفات'
    },
    {
      id: 'deleted',
      title: 'المحذوفات',
      icon: Trash2,
      color: 'bg-red-500',
      description: 'الملفات المحذوفة'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Title */}
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                لوحة مدير القسم
              </h1>
              <p className="text-sm text-gray-500">استلام ومراجعة الملفات</p>
            </div>

            {/* User Info and Actions */}
            <div className="flex items-center space-x-4 space-x-reverse">
              {/* Notifications */}
              <button className="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Bell className="h-6 w-6" />
                {newFileNotification && (
                  <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full animate-pulse"></span>
                )}
              </button>

              {/* User Info */}
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user?.fullName}</p>
                  <p className="text-xs text-gray-500">{user?.jobTitle}</p>
                </div>
                <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-600" />
                </div>
              </div>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="btn btn-secondary flex items-center space-x-2 space-x-reverse"
              >
                <LogOut className="h-4 w-4" />
                <span>خروج</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً، {user?.fullName}
          </h2>
          <p className="text-gray-600">
            ستظهر الملفات المنشورة تلقائياً على شاشتك
          </p>
        </div>

        {/* Department Manager Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {deptManagerCards.map((card) => {
            const IconComponent = card.icon;
            return (
              <div
                key={card.id}
                onClick={() => setActiveModal(card.id)}
                className="home-card group"
              >
                <div className={`home-card-icon ${card.color}`}>
                  <IconComponent className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2 text-center">
                  {card.title}
                </h3>
                <p className="text-gray-600 text-center text-sm">
                  {card.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Status Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">حالة النظام</h3>
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-green-600 font-medium">متصل - جاهز لاستلام الملفات</span>
          </div>
        </div>
      </main>

      {/* File Viewer Modal */}
      {showFileViewer && newFileNotification && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="fixed inset-0 bg-black bg-opacity-75" />
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="relative w-full max-w-4xl bg-white rounded-xl shadow-xl">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    ملف جديد من: {newFileNotification.publisher?.fullName}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {newFileNotification.file?.fileName}
                  </p>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button className="btn btn-secondary flex items-center space-x-2 space-x-reverse">
                    <Download className="h-4 w-4" />
                    <span>تحميل</span>
                  </button>
                  <button
                    onClick={handleCloseFile}
                    className="btn btn-primary"
                  >
                    إغلاق وحفظ
                  </button>
                </div>
              </div>
              
              {/* File Content */}
              <div className="p-6">
                <div className="bg-gray-100 rounded-lg p-8 text-center">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    عارض الملفات قيد التطوير
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    اسم الملف: {newFileNotification.file?.originalName}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Other Modals - Temporary placeholders */}
      {activeModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setActiveModal(null)} />
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="relative w-full max-w-2xl bg-white rounded-xl shadow-xl p-6">
              <h3 className="text-lg font-semibold mb-4">
                {deptManagerCards.find(card => card.id === activeModal)?.title}
              </h3>
              <p className="text-gray-600 mb-4">
                هذه النافذة قيد التطوير...
              </p>
              <button
                onClick={() => setActiveModal(null)}
                className="btn btn-secondary"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DepartmentManagerPage;
