# 🧪 دليل تجربة النظام - الفروع الأربعة

## 🎯 **الهدف من التجربة:**
اختبار آلية النشر والعرض التلقائي بين الفروع الأربعة

---

## 📁 **الملفات المطلوبة للتجربة:**

### 🔗 **روابط الفروع:**
1. **فرع A** - `branch-a.html` - برنامج المستخدمين (أخضر)
2. **فرع B** - `branch-b.html` - برنامج المستخدمين (أحمر)  
3. **فرع C** - `branch-c.html` - برنامج المستخدمين (أزرق)
4. **فرع D** - `branch-d.html` - شاشة العرض (بنفسجي)

---

## 🚀 **خطوات التجربة:**

### **الخطوة 1: فتح جميع الفروع**
```
افتح 4 تبويبات في المتصفح:
├── تبويب 1: branch-a.html
├── تبويب 2: branch-b.html  
├── تبويب 3: branch-c.html
└── تبويب 4: branch-d.html
```

### **الخطوة 2: ترتيب النوافذ**
- **رتب النوافذ** جنباً إلى جنب لترى الأربعة معاً
- **أو استخدم Alt+Tab** للتنقل بينها

### **الخطوة 3: تجربة النشر**
1. **اذهب لفرع A** (الأخضر)
2. **انقر "نشر محتوى تجريبي"**
3. **راقب ما يحدث** في الفروع الأخرى

---

## 🔄 **ما ستراه:**

### **عند النشر من فرع A:**
```
فرع A (الناشر):
├── ✅ يظهر تأكيد النشر
└── ✅ يضاف للمحتوى المستلم

فرع B:
├── 🔔 إشعار منبثق
├── 📄 عرض المحتوى
└── ⚡ خيار الحفظ/الإغلاق

فرع C:
├── 🔔 إشعار منبثق  
├── 📄 عرض المحتوى
└── ⚡ خيار الحفظ/الإغلاق

فرع D (شاشة العرض):
├── 📺 عرض تلقائي فوري
├── 🎬 انتقال سلس
└── 🔄 يبقى حتى نشر جديد
```

---

## 🎮 **سيناريوهات التجربة:**

### **التجربة 1: النشر المتتالي**
1. **انشر من فرع A** - راقب الاستجابة
2. **انشر من فرع B** - راقب التحديث
3. **انشر من فرع C** - راقب التغيير

### **التجربة 2: اختبار فرع D**
1. **اضغط F11** في فرع D للعرض الكامل
2. **انشر محتوى** من أي فرع آخر
3. **راقب العرض** على الشاشة الكاملة

### **التجربة 3: اختبار الحفظ**
1. **انشر محتوى** من فرع A
2. **في فرع B** انقر "حفظ في المستلمة"
3. **تحقق من قسم المحتوى المستلم**

---

## 🎨 **الألوان والتمييز:**

### **فرع A** - 🟢 أخضر
- **الغرض:** مستخدمين عاديين
- **المحتوى:** دليل السلامة، تعليمات العمل

### **فرع B** - 🔴 أحمر  
- **الغرض:** مستخدمين عاديين
- **المحتوى:** تقارير المبيعات، خطط التسويق

### **فرع C** - 🔵 أزرق
- **الغرض:** مستخدمين عاديين  
- **المحتوى:** معايير الجودة، إجراءات التدقيق

### **فرع D** - 🟣 بنفسجي
- **الغرض:** شاشة العرض للتلفزيونات
- **المحتوى:** عرض تلقائي لجميع المنشورات

---

## ⚡ **المميزات المتقدمة:**

### **في فرع D (شاشة العرض):**
- **F11** - العرض الكامل
- **ESC** - الخروج من العرض الكامل
- **إخفاء المؤشر** - تلقائياً بعد 3 ثوان
- **مؤشر "محتوى جديد"** - يظهر لمدة 3 ثوان

### **في الفروع A, B, C:**
- **إشعارات فورية** - عند نشر محتوى جديد
- **خيار الحفظ** - في المحتوى المستلم
- **خيار الإغلاق** - بدون حفظ
- **قائمة المستلم** - عرض جميع المحتوى المحفوظ

---

## 🔧 **نصائح للتجربة:**

### **للحصول على أفضل تجربة:**
1. **استخدم Chrome أو Firefox**
2. **فعل JavaScript** في المتصفح
3. **لا تمسح بيانات المتصفح** أثناء التجربة
4. **اتركها تعمل** - التحديث كل ثانية

### **لاختبار فرع D على التلفاز:**
1. **وصل الكمبيوتر بالتلفاز** عبر HDMI
2. **افتح branch-d.html**
3. **اضغط F11** للعرض الكامل
4. **انشر محتوى** من الفروع الأخرى

---

## 🎯 **النتائج المتوقعة:**

### **✅ نجح الاختبار إذا:**
- **الإشعارات تظهر** في جميع الفروع عند النشر
- **فرع D يعرض المحتوى** تلقائياً وفورياً
- **المحتوى يحفظ** في قسم المستلم عند الاختيار
- **العرض الكامل يعمل** في فرع D

### **❌ هناك مشكلة إذا:**
- **لا تظهر إشعارات** في الفروع الأخرى
- **فرع D لا يحدث** المحتوى
- **الحفظ لا يعمل** في المستلم
- **العرض الكامل لا يعمل**

---

## 🎉 **مبروك!**

إذا نجحت التجربة، فقد حصلت على:
- **نظام نشر تلقائي** يعمل بين الفروع
- **شاشة عرض احترافية** للتلفزيونات  
- **إدارة محتوى ذكية** مع الحفظ والاستعادة
- **واجهات متخصصة** لكل نوع استخدام

**الآن يمكنك استخدام النظام في بيئة العمل الحقيقية!** 🚀

---

## 📞 **في حالة المشاكل:**

1. **أعد تحميل الصفحات**
2. **تأكد من تفعيل JavaScript**
3. **امسح بيانات المتصفح وأعد المحاولة**
4. **جرب متصفح آخر**
