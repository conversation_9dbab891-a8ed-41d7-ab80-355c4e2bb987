const express = require('express');
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const { body, validationResult } = require('express-validator');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// الحصول على جميع المستخدمين (للمدير الرئيسي فقط)
router.get('/', authenticateToken, authorizeRoles('MAIN_ADMIN'), async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        fullName: true,
        jobTitle: true,
        department: true,
        userType: true,
        isActive: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(users);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// إضافة مستخدم جديد (للمدير الرئيسي فقط)
router.post('/', authenticateToken, authorizeRoles('MAIN_ADMIN'), [
  body('username').notEmpty().withMessage('اسم المستخدم مطلوب'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  body('fullName').notEmpty().withMessage('الاسم الكامل مطلوب'),
  body('jobTitle').notEmpty().withMessage('المسمى الوظيفي مطلوب'),
  body('department').notEmpty().withMessage('القسم مطلوب'),
  body('userType').isIn(['MAIN_ADMIN', 'OPERATIONS_MGR', 'DEPT_MANAGER', 'DISPLAY_OP']).withMessage('نوع المستخدم غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { username, password, fullName, jobTitle, department, userType } = req.body;

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    const existingUser = await prisma.user.findUnique({
      where: { username }
    });

    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود مسبقاً' });
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 12);

    // إنشاء المستخدم الجديد
    const newUser = await prisma.user.create({
      data: {
        username,
        password: hashedPassword,
        fullName,
        jobTitle,
        department,
        userType
      },
      select: {
        id: true,
        username: true,
        fullName: true,
        jobTitle: true,
        department: true,
        userType: true,
        isActive: true,
        createdAt: true
      }
    });

    res.status(201).json({
      message: 'تم إنشاء المستخدم بنجاح',
      user: newUser
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحديث بيانات المستخدم
router.put('/:id', authenticateToken, authorizeRoles('MAIN_ADMIN'), [
  body('fullName').optional().notEmpty().withMessage('الاسم الكامل لا يمكن أن يكون فارغاً'),
  body('jobTitle').optional().notEmpty().withMessage('المسمى الوظيفي لا يمكن أن يكون فارغاً'),
  body('department').optional().notEmpty().withMessage('القسم لا يمكن أن يكون فارغاً'),
  body('userType').optional().isIn(['MAIN_ADMIN', 'OPERATIONS_MGR', 'DEPT_MANAGER', 'DISPLAY_OP']).withMessage('نوع المستخدم غير صحيح'),
  body('isActive').optional().isBoolean().withMessage('حالة النشاط يجب أن تكون true أو false')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        username: true,
        fullName: true,
        jobTitle: true,
        department: true,
        userType: true,
        isActive: true,
        updatedAt: true
      }
    });

    res.json({
      message: 'تم تحديث بيانات المستخدم بنجاح',
      user: updatedUser
    });
  } catch (error) {
    console.error('Update user error:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
