<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فرع D - شاشة العرض</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            overflow: hidden;
        }

        .display-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .header-bar {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 100;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: bold;
            color: #4a5568;
        }

        .status-info {
            display: flex;
            align-items: center;
            gap: 20px;
            color: #4a5568;
        }

        .branch-indicator {
            background: #805ad5;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #48bb78;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .content-area {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            position: relative;
        }

        .content-display {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            padding: 60px;
            text-align: center;
            max-width: 800px;
            width: 100%;
            min-height: 500px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .content-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #805ad5, #667eea);
        }

        .content-preview {
            width: 300px;
            height: 400px;
            margin: 0 auto 30px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 120px;
            position: relative;
            transition: all 0.3s ease;
        }

        .content-preview:hover {
            transform: scale(1.05);
        }

        .content-title {
            font-size: 36px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .content-author {
            font-size: 20px;
            color: #718096;
            margin-bottom: 20px;
        }

        .content-date {
            font-size: 16px;
            color: #a0aec0;
            background: #f7fafc;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
        }

        .waiting-state {
            color: #718096;
            font-size: 24px;
            text-align: center;
        }

        .waiting-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.7;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .fullscreen-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            transition: all 0.3s ease;
            z-index: 200;
        }

        .fullscreen-btn:hover {
            background: white;
            transform: scale(1.1);
        }

        /* وضع العرض الكامل */
        .fullscreen-mode {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
        }

        .fullscreen-mode .header-bar {
            display: none;
        }

        .fullscreen-mode .content-area {
            padding: 20px;
        }

        .fullscreen-mode .content-display {
            max-width: none;
            width: 100%;
            height: 100%;
            border-radius: 0;
            padding: 80px;
        }

        .fullscreen-mode .content-preview {
            width: 400px;
            height: 500px;
            font-size: 150px;
        }

        .fullscreen-mode .content-title {
            font-size: 48px;
        }

        .fullscreen-mode .content-author {
            font-size: 24px;
        }

        .fullscreen-mode .fullscreen-btn {
            display: none;
        }

        .slide-in {
            animation: slideInFromRight 0.8s ease-out;
        }

        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .new-content-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            animation: bounce 1s ease-in-out 3;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
</head>
<body>
    <div class="display-container" id="displayContainer">
        <!-- الشريط العلوي -->
        <div class="header-bar" id="headerBar">
            <div class="logo">
                📺 شاشة العرض - فرع D
            </div>
            <div class="status-info">
                <div class="branch-indicator">فرع D</div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>متصل</span>
                </div>
                <div id="currentTime"></div>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <div class="content-display" id="contentDisplay">
                <!-- حالة الانتظار -->
                <div class="waiting-state" id="waitingState">
                    <div class="waiting-icon">⏳</div>
                    <div>في انتظار محتوى جديد...</div>
                    <div style="font-size: 16px; margin-top: 10px; opacity: 0.7;">
                        سيتم عرض المحتوى تلقائياً عند النشر من أي فرع
                    </div>
                </div>

                <!-- عرض المحتوى -->
                <div id="contentView" style="display: none;">
                    <div class="new-content-indicator" id="newIndicator" style="display: none;">
                        محتوى جديد!
                    </div>
                    <div class="content-preview" id="contentPreview">
                        📖
                    </div>
                    <div class="content-title" id="contentTitle">عنوان المحتوى</div>
                    <div class="content-author" id="contentAuthor">المؤلف</div>
                    <div class="content-date" id="contentDate">تاريخ النشر</div>
                </div>
            </div>
        </div>

        <!-- زر العرض الكامل -->
        <button class="fullscreen-btn" onclick="toggleFullscreen()" title="العرض الكامل">
            ⛶
        </button>
    </div>

    <script>
        class DisplayApp {
            constructor() {
                this.isFullscreen = false;
                this.currentContent = null;
                this.lastContentTimestamp = 0;
                this.permissions = { view: true, fullscreen: true };
                this.init();
            }

            init() {
                this.updateTime();
                this.checkForContent();
                this.startContentMonitoring();
                this.setupKeyboardShortcuts();
                this.updatePermissions();

                // تحديث الوقت كل ثانية
                setInterval(() => this.updateTime(), 1000);

                // مراقبة الصلاحيات كل ثانيتين
                setInterval(() => this.updatePermissions(), 2000);
            }

            updatePermissions() {
                const branchPermissions = localStorage.getItem('branchPermissions');
                if (branchPermissions) {
                    const allPermissions = JSON.parse(branchPermissions);
                    if (allPermissions.D) {
                        this.permissions = allPermissions.D.permissions;
                        this.updateUI();
                    }
                }
            }

            updateUI() {
                const fullscreenBtn = document.querySelector('.fullscreen-btn');

                if (!this.permissions.fullscreen) {
                    fullscreenBtn.style.display = 'none';
                    if (this.isFullscreen) {
                        this.toggleFullscreen(); // إجبار الخروج من العرض الكامل
                    }
                } else {
                    fullscreenBtn.style.display = 'flex';
                }

                if (!this.permissions.view) {
                    document.getElementById('contentDisplay').innerHTML = `
                        <div style="text-align: center; color: #e53e3e; font-size: 24px;">
                            <div style="font-size: 80px; margin-bottom: 20px;">🚫</div>
                            <div>ليس لديك صلاحية لعرض المحتوى</div>
                            <div style="font-size: 16px; margin-top: 10px; opacity: 0.7;">
                                يرجى التواصل مع الإدارة
                            </div>
                        </div>
                    `;
                }
            }

            updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                document.getElementById('currentTime').textContent = timeString;
            }

            checkForContent() {
                const sharedContent = localStorage.getItem('sharedContent');
                if (sharedContent) {
                    const content = JSON.parse(sharedContent);
                    if (content.timestamp > this.lastContentTimestamp) {
                        this.displayContent(content);
                        this.lastContentTimestamp = content.timestamp;
                    }
                }
            }

            startContentMonitoring() {
                // مراقبة التغييرات في المحتوى كل ثانية
                setInterval(() => {
                    this.checkForContent();
                }, 1000);
            }

            displayContent(content) {
                this.currentContent = content;
                
                // إخفاء حالة الانتظار
                document.getElementById('waitingState').style.display = 'none';
                
                // عرض المحتوى الجديد
                const contentView = document.getElementById('contentView');
                contentView.style.display = 'block';
                contentView.classList.add('slide-in');
                
                // عرض مؤشر المحتوى الجديد
                const newIndicator = document.getElementById('newIndicator');
                newIndicator.style.display = 'block';
                setTimeout(() => {
                    newIndicator.style.display = 'none';
                }, 3000);
                
                // تحديث المحتوى
                document.getElementById('contentPreview').textContent = content.icon || '📖';
                document.getElementById('contentTitle').textContent = content.title;
                document.getElementById('contentAuthor').textContent = `بواسطة: ${content.author} - فرع ${content.branch}`;
                document.getElementById('contentDate').textContent = `تاريخ النشر: ${content.date}`;

                // إزالة الكلاس بعد انتهاء الأنيميشن
                setTimeout(() => {
                    contentView.classList.remove('slide-in');
                }, 800);

                console.log('تم عرض محتوى جديد على شاشة العرض:', content.title);
            }

            toggleFullscreen() {
                if (!this.permissions.fullscreen) {
                    alert('🚫 ليس لديك صلاحية للعرض الكامل!\n\nيرجى التواصل مع الإدارة للحصول على الصلاحيات المطلوبة.');
                    return;
                }

                const container = document.getElementById('displayContainer');

                if (!this.isFullscreen) {
                    // تفعيل العرض الكامل
                    container.classList.add('fullscreen-mode');
                    
                    // طلب العرض الكامل من المتصفح
                    if (document.documentElement.requestFullscreen) {
                        document.documentElement.requestFullscreen();
                    } else if (document.documentElement.webkitRequestFullscreen) {
                        document.documentElement.webkitRequestFullscreen();
                    } else if (document.documentElement.msRequestFullscreen) {
                        document.documentElement.msRequestFullscreen();
                    }
                    
                    this.isFullscreen = true;
                    
                    // إخفاء مؤشر الماوس بعد 3 ثوان
                    this.setupMouseHiding();
                    
                } else {
                    // إلغاء العرض الكامل
                    container.classList.remove('fullscreen-mode');
                    document.body.style.cursor = 'default';
                    
                    // إلغاء العرض الكامل من المتصفح
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                    
                    this.isFullscreen = false;
                }
            }

            setupMouseHiding() {
                let mouseTimer;
                const hideMouseCursor = () => {
                    document.body.style.cursor = 'none';
                };
                
                const showMouseCursor = () => {
                    document.body.style.cursor = 'default';
                    clearTimeout(mouseTimer);
                    mouseTimer = setTimeout(hideMouseCursor, 3000);
                };
                
                document.addEventListener('mousemove', showMouseCursor);
                mouseTimer = setTimeout(hideMouseCursor, 3000);
            }

            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    // F11 للعرض الكامل
                    if (e.key === 'F11') {
                        e.preventDefault();
                        this.toggleFullscreen();
                    }
                    
                    // ESC للخروج من العرض الكامل
                    if (e.key === 'Escape' && this.isFullscreen) {
                        this.toggleFullscreen();
                    }
                });
            }
        }

        // تشغيل التطبيق
        const displayApp = new DisplayApp();

        // دالة العرض الكامل (للاستخدام من HTML)
        function toggleFullscreen() {
            displayApp.toggleFullscreen();
        }

        // التعامل مع تغيير حالة العرض الكامل من المتصفح
        document.addEventListener('fullscreenchange', function() {
            if (!document.fullscreenElement && displayApp.isFullscreen) {
                displayApp.toggleFullscreen();
            }
        });
    </script>
</body>
</html>
