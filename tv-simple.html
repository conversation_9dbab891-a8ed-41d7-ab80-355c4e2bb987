<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شاشة التلفاز - ربط مباشر</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            height: 100vh; 
            display: flex; 
            flex-direction: column; 
            justify-content: center; 
            align-items: center; 
            text-align: center;
        }
        
        .container { 
            background: rgba(255, 255, 255, 0.1); 
            padding: 50px; 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 800px;
        }
        
        .logo { 
            font-size: 80px; 
            margin-bottom: 30px; 
        }
        
        .title { 
            font-size: 48px; 
            font-weight: bold; 
            margin-bottom: 40px; 
        }
        
        .status { 
            font-size: 32px; 
            padding: 20px 40px; 
            border-radius: 15px; 
            margin: 30px 0; 
            font-weight: bold;
            background: rgba(16, 185, 129, 0.3); 
            border: 2px solid #10b981;
        }
        
        .content-area { 
            display: none; 
            position: fixed; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 100%; 
            background: white; 
            z-index: 1000;
        }
        
        .file-header { 
            background: transparent; 
            color: black; 
            padding: 40px; 
            text-align: center; 
            border-bottom: 3px solid #e5e7eb;
        }
        
        .file-title { 
            font-size: 64px; 
            font-weight: 900; 
            margin: 0; 
            color: black;
        }
        
        .file-content { 
            flex: 1; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            padding: 20px;
        }
        
        .instructions {
            font-size: 24px;
            margin: 20px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container" id="mainContainer">
        <div class="logo">📺</div>
        <div class="title">شاشة العرض جاهزة</div>
        
        <div class="status">
            ✅ الشاشة جاهزة للربط
        </div>

        <div style="background: rgba(255, 255, 255, 0.2); padding: 30px; border-radius: 15px; margin: 30px 0; border: 2px solid white;">
            <div style="font-size: 24px; margin-bottom: 15px; font-weight: bold;">
                📍 عنوان IP هذا التلفاز:
            </div>
            <div id="tvIPDisplay" style="font-size: 48px; font-weight: 900; background: white; color: #374151; padding: 20px; border-radius: 10px; letter-spacing: 3px; font-family: 'Courier New', monospace;">
                جاري التحديد...
            </div>
            <div style="font-size: 18px; margin-top: 15px; opacity: 0.9;">
                انسخ هذا العنوان واستخدمه في البرنامج
            </div>
        </div>

        <div class="instructions">
            في البرنامج: شاشات العرض ← إضافة شاشة ← أدخل IP أعلاه
        </div>
    </div>

    <div class="content-area" id="contentArea">
        <!-- المحتوى سيظهر هنا -->
    </div>

    <script>
        console.log('🚀 شاشة التلفاز جاهزة');

        // اكتشاف IP التلفاز
        detectTVIP();

        // فحص المحتوى كل ثانية
        setInterval(checkForContent, 1000);

        function detectTVIP() {
            // طريقة 1: من URL الحالي
            var currentIP = window.location.hostname;
            if (currentIP && currentIP !== 'localhost' && currentIP !== '127.0.0.1') {
                displayTVIP(currentIP);
                return;
            }

            // طريقة 2: WebRTC للحصول على IP المحلي
            var pc = new RTCPeerConnection({
                iceServers: []
            });

            pc.createDataChannel('');
            pc.createOffer().then(offer => pc.setLocalDescription(offer));

            pc.onicecandidate = function(ice) {
                if (!ice || !ice.candidate || !ice.candidate.candidate) return;

                var ipMatch = /([0-9]{1,3}(\.[0-9]{1,3}){3})/.exec(ice.candidate.candidate);
                if (ipMatch) {
                    var detectedIP = ipMatch[1];
                    // تجاهل IP المحلي
                    if (detectedIP !== '127.0.0.1' && !detectedIP.startsWith('169.254')) {
                        displayTVIP(detectedIP);
                        pc.onicecandidate = function() {}; // إيقاف البحث
                    }
                }
            };

            // إذا فشلت كل الطرق، استخدم IP افتراضي
            setTimeout(function() {
                var displayElement = document.getElementById('tvIPDisplay');
                if (displayElement.textContent === 'جاري التحديد...') {
                    displayTVIP('192.168.1.XXX');
                    document.getElementById('tvIPDisplay').style.background = '#fef3c7';
                    document.getElementById('tvIPDisplay').style.color = '#92400e';

                    // إضافة تعليمات للعثور على IP يدوياً
                    var container = displayElement.parentElement;
                    var helpText = document.createElement('div');
                    helpText.style.cssText = 'font-size: 14px; margin-top: 10px; color: #fbbf24; text-align: center;';
                    helpText.innerHTML = '⚠️ لم يتم اكتشاف IP تلقائياً<br>اذهب لإعدادات التلفاز ← الشبكة ← معلومات الاتصال';
                    container.appendChild(helpText);
                }
            }, 3000);
        }

        function displayTVIP(ip) {
            var displayElement = document.getElementById('tvIPDisplay');
            displayElement.textContent = ip;
            console.log('تم اكتشاف IP التلفاز:', ip);

            // حفظ IP للاستخدام في الربط
            localStorage.setItem('tv_ip_address', ip);
        }
        
        function checkForContent() {
            try {
                const contentData = localStorage.getItem('tv_display_content');
                if (contentData) {
                    const data = JSON.parse(contentData);
                    if (data.timestamp > Date.now() - 5000) { // خلال آخر 5 ثوان
                        displayContent(data);
                        localStorage.removeItem('tv_display_content');
                    }
                }
            } catch (e) {
                // تجاهل الأخطاء
            }
        }
        
        function displayContent(data) {
            console.log('📺 عرض محتوى:', data);
            
            const contentArea = document.getElementById('contentArea');
            const mainContainer = document.getElementById('mainContainer');
            const { fileType, content, contentType } = data;
            
            let displayHTML = '';
            
            if (contentType === 'image' && content) {
                displayHTML = `
                    <div style="display: flex; flex-direction: column; height: 100vh;">
                        <div class="file-header">
                            <h1 class="file-title">${fileType}</h1>
                        </div>
                        <div class="file-content" style="background: black;">
                            <img src="${content}" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="${fileType}">
                        </div>
                    </div>
                `;
            } else if (contentType === 'pdf' && content) {
                displayHTML = `
                    <div style="display: flex; flex-direction: column; height: 100vh;">
                        <div class="file-header">
                            <h1 class="file-title">${fileType}</h1>
                        </div>
                        <div class="file-content">
                            <embed src="${content}" type="application/pdf" width="100%" height="100%" style="border: none;">
                        </div>
                    </div>
                `;
            } else {
                displayHTML = `
                    <div style="display: flex; flex-direction: column; height: 100vh;">
                        <div class="file-header">
                            <h1 class="file-title">${fileType}</h1>
                        </div>
                        <div class="file-content" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <div style="text-align: center; color: white;">
                                <svg width="400" height="400" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                </svg>
                                <p style="font-size: 48px; margin-top: 40px; font-weight: bold;">${fileType}</p>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            contentArea.innerHTML = displayHTML;
            contentArea.style.display = 'block';
            mainContainer.style.display = 'none';
            
            // العودة للشاشة الرئيسية بعد 20 ثانية
            setTimeout(() => {
                contentArea.style.display = 'none';
                mainContainer.style.display = 'flex';
            }, 20000);
            
            // أو بالنقر
            contentArea.onclick = () => {
                contentArea.style.display = 'none';
                mainContainer.style.display = 'flex';
                contentArea.onclick = null;
            };
        }
        
        // إشارة للبرنامج أن الشاشة جاهزة
        localStorage.setItem('tv_connection_result', JSON.stringify({
            success: true,
            message: 'الشاشة جاهزة ومتصلة',
            timestamp: Date.now()
        }));
    </script>
</body>
</html>
