const express = require('express');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const { body, validationResult } = require('express-validator');
const { supabase } = require('../config/supabase');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();

// إعداد multer للتعامل مع الملفات في الذاكرة
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('يُسمح بملفات PDF فقط'), false);
    }
  }
});

// نشر ملف جديد
router.post('/publish', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN', 'OPERATIONS_MGR'),
  upload.single('file')
], async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'الملف مطلوب' });
    }

    const { fileName } = req.body;
    if (!fileName) {
      return res.status(400).json({ message: 'اسم الملف مطلوب' });
    }

    // إنشاء اسم ملف فريد
    const fileExtension = req.file.originalname.split('.').pop();
    const uniqueFileName = `${uuidv4()}-${Date.now()}.${fileExtension}`;
    const filePath = `files/${uniqueFileName}`;

    // رفع الملف إلى Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('files')
      .upload(filePath, req.file.buffer, {
        contentType: req.file.mimetype,
        upsert: false
      });

    if (uploadError) {
      console.error('File upload error:', uploadError);
      return res.status(500).json({ message: 'خطأ في رفع الملف' });
    }

    // الحصول على رابط الملف العام
    const { data: urlData } = supabase.storage
      .from('files')
      .getPublicUrl(filePath);

    // حفظ بيانات الملف في قاعدة البيانات
    const { data: newFile, error: dbError } = await supabase
      .from('files')
      .insert({
        file_name: fileName,
        original_name: req.file.originalname,
        file_path: urlData.publicUrl,
        file_type: req.file.mimetype,
        file_size: req.file.size,
        publisher_id: req.user.id
      })
      .select(`
        *,
        publisher:users!inner(id, full_name, department)
      `)
      .single();

    if (dbError) {
      // حذف الملف من Storage إذا فشل حفظه في قاعدة البيانات
      await supabase.storage.from('files').remove([filePath]);
      throw dbError;
    }

    res.status(201).json({
      message: 'تم نشر الملف بنجاح',
      file: newFile
    });

  } catch (error) {
    console.error('Publish file error:', error);
    res.status(500).json({ message: 'خطأ في نشر الملف' });
  }
});

// جلب جميع الملفات
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { search, publisher, department, fileType } = req.query;
    
    let query = supabase
      .from('files')
      .select(`
        *,
        publisher:users!inner(id, full_name, department)
      `)
      .eq('is_deleted', false);

    // إضافة فلاتر البحث
    if (search) {
      query = query.or(`file_name.ilike.%${search}%,original_name.ilike.%${search}%`);
    }
    
    if (publisher) {
      query = query.ilike('users.full_name', `%${publisher}%`);
    }
    
    if (department) {
      query = query.ilike('users.department', `%${department}%`);
    }
    
    if (fileType) {
      query = query.eq('file_type', fileType);
    }

    const { data: files, error } = await query.order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json(files);
  } catch (error) {
    console.error('Get files error:', error);
    res.status(500).json({ message: 'خطأ في جلب الملفات' });
  }
});

// جلب ملف محدد
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const { data: file, error } = await supabase
      .from('files')
      .select(`
        *,
        publisher:users!inner(id, full_name, department)
      `)
      .eq('id', id)
      .eq('is_deleted', false)
      .single();

    if (error || !file) {
      return res.status(404).json({ message: 'الملف غير موجود' });
    }

    res.json(file);
  } catch (error) {
    console.error('Get file error:', error);
    res.status(500).json({ message: 'خطأ في جلب بيانات الملف' });
  }
});

// حذف ملف (نقل إلى سلة المهملات)
router.delete('/:id', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN', 'OPERATIONS_MGR')
], async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من وجود الملف
    const { data: file, error: fetchError } = await supabase
      .from('files')
      .select('id, file_name, publisher_id')
      .eq('id', id)
      .eq('is_deleted', false)
      .single();

    if (fetchError || !file) {
      return res.status(404).json({ message: 'الملف غير موجود' });
    }

    // التحقق من الصلاحية (المدير الرئيسي أو ناشر الملف)
    if (req.user.user_type !== 'MAIN_ADMIN' && file.publisher_id !== req.user.id) {
      return res.status(403).json({ message: 'ليس لديك صلاحية لحذف هذا الملف' });
    }

    // نقل الملف إلى سلة المهملات
    const { error } = await supabase
      .from('files')
      .update({ is_deleted: true })
      .eq('id', id);

    if (error) {
      throw error;
    }

    res.json({ message: 'تم نقل الملف إلى سلة المهملات' });

  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({ message: 'خطأ في حذف الملف' });
  }
});

// استعادة ملف من سلة المهملات
router.patch('/:id/restore', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN', 'OPERATIONS_MGR')
], async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من وجود الملف في سلة المهملات
    const { data: file, error: fetchError } = await supabase
      .from('files')
      .select('id, file_name')
      .eq('id', id)
      .eq('is_deleted', true)
      .single();

    if (fetchError || !file) {
      return res.status(404).json({ message: 'الملف غير موجود في سلة المهملات' });
    }

    // استعادة الملف
    const { error } = await supabase
      .from('files')
      .update({ is_deleted: false })
      .eq('id', id);

    if (error) {
      throw error;
    }

    res.json({ message: 'تم استعادة الملف بنجاح' });

  } catch (error) {
    console.error('Restore file error:', error);
    res.status(500).json({ message: 'خطأ في استعادة الملف' });
  }
});

// جلب الملفات المحذوفة
router.get('/deleted/list', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN', 'OPERATIONS_MGR')
], async (req, res) => {
  try {
    const { data: files, error } = await supabase
      .from('files')
      .select(`
        *,
        publisher:users!inner(id, full_name, department)
      `)
      .eq('is_deleted', true)
      .order('updated_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json(files);
  } catch (error) {
    console.error('Get deleted files error:', error);
    res.status(500).json({ message: 'خطأ في جلب الملفات المحذوفة' });
  }
});

// حذف نهائي للملف
router.delete('/:id/permanent', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN')
], async (req, res) => {
  try {
    const { id } = req.params;

    // جلب بيانات الملف
    const { data: file, error: fetchError } = await supabase
      .from('files')
      .select('id, file_path')
      .eq('id', id)
      .single();

    if (fetchError || !file) {
      return res.status(404).json({ message: 'الملف غير موجود' });
    }

    // حذف الملف من Storage
    if (file.file_path) {
      const fileName = file.file_path.split('/').pop();
      await supabase.storage.from('files').remove([`files/${fileName}`]);
    }

    // حذف الملف من قاعدة البيانات
    const { error } = await supabase
      .from('files')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    res.json({ message: 'تم حذف الملف نهائياً' });

  } catch (error) {
    console.error('Permanent delete file error:', error);
    res.status(500).json({ message: 'خطأ في الحذف النهائي للملف' });
  }
});

module.exports = router;
