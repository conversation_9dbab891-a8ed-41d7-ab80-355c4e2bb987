<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة مكتب التلفزيون</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: #667eea;
            border-radius: 12px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .main-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .clipboard { background: #CA188F; }
        .settings { background: #6b7280; }
        .search { background: #10b981; }
        .publish { background: #3b82f6; }
        .deleted { background: #ef4444; }
        .displays { background: #8b5cf6; }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            background: #667eea;
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }

        .modal-body {
            padding: 30px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 2000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        /* Settings Styles */
        .settings-options {
            display: flex;
            gap: 30px;
            justify-content: center;
            margin-bottom: 40px;
        }

        .option-btn {
            background: #1e40af;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 700;
            font-size: 18px;
            transition: all 0.2s;
        }

        .option-btn:hover {
            background: #1e3a8a;
        }

        .option-btn.inactive {
            background: #6b7280;
        }

        .option-btn.inactive:hover {
            background: #4b5563;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-column {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .input-field {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ccc;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            height: 50px;
            font-weight: 500;
        }

        .input-field:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .display-field {
            background: #f9fafb;
            border-color: #e5e7eb;
            color: #374151;
        }

        .btn {
            border: none;
            padding: 12px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 700;
            font-size: 16px;
            height: 50px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: #1e40af;
            color: white;
        }

        .btn-primary:hover {
            background: #1e3a8a;
        }

        .btn-save {
            background: #60a5fa;
            color: white;
        }

        .btn-save:hover {
            background: #3b82f6;
        }

        .btn-edit {
            background: #fbbf24;
            color: white;
        }

        .btn-edit:hover {
            background: #f59e0b;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .btn-delete:hover {
            background: #dc2626;
        }

        .search-container {
            max-width: 600px;
            margin: 0 auto 20px;
        }

        .search-box {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            max-width: 600px;
            margin: 0 auto;
        }

        .fields-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-width: 600px;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">📺</div>
            <h1>نظام إدارة مكتب التلفزيون</h1>
            <p>إدارة شاملة للمحتوى والمستخدمين وشاشات العرض</p>
        </div>

        <!-- Main Grid -->
        <div class="main-grid">
            <div class="card clipboard" onclick="openModal('clipboardModal')">
                <div class="card-icon clipboard">📋</div>
                <h3>الحافظة</h3>
                <p>عرض الملفات المرسلة والمستقبلة</p>
            </div>

            <div class="card settings" onclick="openModal('settingsModal')">
                <div class="card-icon settings">⚙️</div>
                <h3>الإعدادات</h3>
                <p>إدارة المستخدمين والصلاحيات</p>
            </div>

            <div class="card search" onclick="openModal('searchModal')">
                <div class="card-icon search">🔍</div>
                <h3>البحث</h3>
                <p>البحث في الملفات والمستندات</p>
            </div>

            <div class="card publish" onclick="openModal('publishModal')">
                <div class="card-icon publish">📤</div>
                <h3>النشر</h3>
                <p>نشر الملفات للأقسام</p>
            </div>

            <div class="card deleted" onclick="openModal('deletedModal')">
                <div class="card-icon deleted">🗑️</div>
                <h3>المحذوفات</h3>
                <p>عرض الملفات المحذوفة</p>
            </div>

            <div class="card displays" onclick="openModal('displaysModal')">
                <div class="card-icon displays">📺</div>
                <h3>شاشات العرض</h3>
                <p>إدارة شاشات التلفزيون</p>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>⚙️ الإعدادات</h2>
                <button class="close-btn" onclick="closeModal('settingsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <h2 style="text-align: center; margin-bottom: 30px; font-size: 20px; font-weight: bold;">إدارة معلومات المستخدمين</h2>
                
                <!-- Options -->
                <div class="settings-options">
                    <button class="option-btn" id="addBtn" onclick="showSection('add')">إضافة معلومات المستخدمين</button>
                    <button class="option-btn inactive" id="editBtn" onclick="showSection('edit')">تعديل المعلومات</button>
                </div>

                <!-- Add Section -->
                <div id="addSection" class="section active">
                    <div class="form-grid">
                        <!-- Right Column - Input Fields -->
                        <div class="form-column">
                            <input type="text" id="addUserName" class="input-field" placeholder="اسم المستخدم">
                            <input type="text" id="addJobTitle" class="input-field" placeholder="المسمى الوظيفي">
                            <input type="text" id="addDepartment" class="input-field" placeholder="القسم">
                            <button class="btn btn-primary" onclick="saveUser()">💾 حفظ</button>
                        </div>

                        <!-- Left Column - Display Fields -->
                        <div class="form-column">
                            <input type="text" id="displayUserName" class="input-field display-field" placeholder="اسم المستخدم المحفوظ" readonly>
                            <input type="text" id="displayJobTitle" class="input-field display-field" placeholder="المسمى الوظيفي المحفوظ" readonly>
                            <input type="text" id="displayDepartment" class="input-field display-field" placeholder="القسم المحفوظ" readonly>
                        </div>
                    </div>
                </div>

                <!-- Edit Section -->
                <div id="editSection" class="section">
                    <!-- Search -->
                    <div class="search-container">
                        <div class="search-box">
                            <input type="text" id="searchInput" class="input-field" placeholder="ابحث عن اسم المستخدم..." style="flex: 1;">
                            <button class="btn btn-primary" onclick="searchUser()" style="width: auto; padding: 12px 20px;">
                                🔍
                            </button>
                        </div>
                    </div>

                    <!-- Display Fields -->
                    <div class="fields-container">
                        <input type="text" id="editUserName" class="input-field" placeholder="اسم المستخدم" readonly>
                        <input type="text" id="editJobTitle" class="input-field" placeholder="المسمى الوظيفي" readonly>
                        <input type="text" id="editDepartment" class="input-field" placeholder="القسم" readonly>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button class="btn btn-save" onclick="saveEditedUser()">
                            💾 حفظ
                        </button>
                        <button class="btn btn-edit" onclick="enableEdit()">
                            ✏️ تعديل
                        </button>
                        <button class="btn btn-delete" onclick="deleteUser()">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script>
        // User Database
        let userDatabase = [
            { name: 'أحمد محمد', jobTitle: 'مدير عام', department: 'العمليات' },
            { name: 'فاطمة أحمد', jobTitle: 'مدير قسم', department: 'الموارد البشرية' },
            { name: 'محمد سعد', jobTitle: 'محاسب', department: 'المالية' }
        ];

        // Modal Functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Settings Functions
        function showSection(section) {
            const addBtn = document.getElementById('addBtn');
            const editBtn = document.getElementById('editBtn');
            const addSection = document.getElementById('addSection');
            const editSection = document.getElementById('editSection');

            if (section === 'add') {
                addBtn.classList.remove('inactive');
                editBtn.classList.add('inactive');
                addSection.classList.add('active');
                editSection.classList.remove('active');
            } else {
                addBtn.classList.add('inactive');
                editBtn.classList.remove('inactive');
                addSection.classList.remove('active');
                editSection.classList.add('active');
            }
        }

        // Add User Functions
        function saveUser() {
            const userName = document.getElementById('addUserName').value.trim();
            const jobTitle = document.getElementById('addJobTitle').value.trim();
            const department = document.getElementById('addDepartment').value.trim();

            if (!userName || !jobTitle || !department) {
                showNotification('⚠️ يرجى ملء جميع الحقول', 'error');
                return;
            }

            // Check if user exists
            const exists = userDatabase.some(user => user.name === userName);
            if (exists) {
                showNotification('⚠️ المستخدم موجود بالفعل', 'error');
                return;
            }

            // Add user
            userDatabase.push({ name: userName, jobTitle, department });

            // Display in right column
            document.getElementById('displayUserName').value = userName;
            document.getElementById('displayJobTitle').value = jobTitle;
            document.getElementById('displayDepartment').value = department;

            // Clear input fields
            document.getElementById('addUserName').value = '';
            document.getElementById('addJobTitle').value = '';
            document.getElementById('addDepartment').value = '';

            showNotification(`✅ تم حفظ بيانات: ${userName}`, 'success');
        }

        // Edit User Functions
        function searchUser() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            
            if (!searchTerm) {
                showNotification('⚠️ يرجى إدخال اسم للبحث', 'error');
                return;
            }

            const user = userDatabase.find(u => u.name.includes(searchTerm));
            
            if (user) {
                document.getElementById('editUserName').value = user.name;
                document.getElementById('editJobTitle').value = user.jobTitle;
                document.getElementById('editDepartment').value = user.department;
                showNotification(`✅ تم العثور على: ${user.name}`, 'success');
            } else {
                clearEditFields();
                showNotification(`❌ لم يتم العثور على: ${searchTerm}`, 'error');
            }
        }

        function enableEdit() {
            const userNameField = document.getElementById('editUserName');
            
            if (!userNameField.value) {
                showNotification('⚠️ يرجى البحث عن مستخدم أولاً', 'error');
                return;
            }

            document.getElementById('editUserName').readOnly = false;
            document.getElementById('editJobTitle').readOnly = false;
            document.getElementById('editDepartment').readOnly = false;

            // Change border color
            document.getElementById('editUserName').style.borderColor = '#fbbf24';
            document.getElementById('editJobTitle').style.borderColor = '#fbbf24';
            document.getElementById('editDepartment').style.borderColor = '#fbbf24';

            showNotification('🔓 تم تفعيل وضع التعديل', 'info');
        }

        function saveEditedUser() {
            const userName = document.getElementById('editUserName').value.trim();
            const jobTitle = document.getElementById('editJobTitle').value.trim();
            const department = document.getElementById('editDepartment').value.trim();

            if (!userName || !jobTitle || !department) {
                showNotification('⚠️ يرجى ملء جميع الحقول', 'error');
                return;
            }

            const userIndex = userDatabase.findIndex(u => u.name === userName);
            
            if (userIndex !== -1) {
                userDatabase[userIndex] = { name: userName, jobTitle, department };
                
                // Reset fields
                document.getElementById('editUserName').readOnly = true;
                document.getElementById('editJobTitle').readOnly = true;
                document.getElementById('editDepartment').readOnly = true;
                
                // Reset border colors
                document.getElementById('editUserName').style.borderColor = '#ccc';
                document.getElementById('editJobTitle').style.borderColor = '#ccc';
                document.getElementById('editDepartment').style.borderColor = '#ccc';

                showNotification(`✅ تم تعديل بيانات: ${userName}`, 'success');
            } else {
                showNotification(`❌ لم يتم العثور على المستخدم`, 'error');
            }
        }

        function deleteUser() {
            const userName = document.getElementById('editUserName').value.trim();
            
            if (!userName) {
                showNotification('⚠️ يرجى البحث عن مستخدم أولاً', 'error');
                return;
            }

            const userIndex = userDatabase.findIndex(u => u.name === userName);
            
            if (userIndex !== -1) {
                userDatabase.splice(userIndex, 1);
                clearEditFields();
                document.getElementById('searchInput').value = '';
                showNotification(`✅ تم حذف: ${userName}`, 'success');
            } else {
                showNotification(`❌ لم يتم العثور على المستخدم`, 'error');
            }
        }

        function clearEditFields() {
            document.getElementById('editUserName').value = '';
            document.getElementById('editJobTitle').value = '';
            document.getElementById('editDepartment').value = '';
            
            // Reset readonly state
            document.getElementById('editUserName').readOnly = true;
            document.getElementById('editJobTitle').readOnly = true;
            document.getElementById('editDepartment').readOnly = true;
            
            // Reset border colors
            document.getElementById('editUserName').style.borderColor = '#ccc';
            document.getElementById('editJobTitle').style.borderColor = '#ccc';
            document.getElementById('editDepartment').style.borderColor = '#ccc';
        }

        // Notification Function
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            
            // Set color based on type
            if (type === 'error') {
                notification.style.background = '#ef4444';
            } else if (type === 'info') {
                notification.style.background = '#3b82f6';
            } else {
                notification.style.background = '#10b981';
            }
            
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    modal.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html>
