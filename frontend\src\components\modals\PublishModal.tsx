import React, { useState } from 'react';
import { X, Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import api from '@/services/api';
import toast from 'react-hot-toast';

interface PublishModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface PublishFormData {
  fileName: string;
  file: FileList;
}

const PublishModal: React.FC<PublishModalProps> = ({ isOpen, onClose }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<PublishFormData>();

  const selectedFile = watch('file')?.[0];

  const onSubmit = async (data: PublishFormData) => {
    if (!data.file || data.file.length === 0) {
      toast.error('يرجى اختيار ملف للنشر');
      return;
    }

    const formData = new FormData();
    formData.append('file', data.file[0]);
    formData.append('fileName', data.fileName);

    try {
      setUploading(true);
      setUploadProgress(0);

      const response = await api.post('/files/publish', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / (progressEvent.total || 1)
          );
          setUploadProgress(progress);
        },
      });

      toast.success('تم نشر الملف بنجاح!');
      reset();
      onClose();
    } catch (error: any) {
      const message = error.response?.data?.message || 'خطأ في نشر الملف';
      toast.error(message);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type === 'application/pdf') {
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        setValue('file', dataTransfer.files);
        
        if (!watch('fileName')) {
          setValue('fileName', file.name.replace('.pdf', ''));
        }
      } else {
        toast.error('يُسمح بملفات PDF فقط');
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && !watch('fileName')) {
      setValue('fileName', file.name.replace('.pdf', ''));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50">
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-white rounded-xl shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="h-10 w-10 bg-purple-500 rounded-lg flex items-center justify-center">
                <Upload className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">نشر ملف جديد</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* File Name Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم الملف
              </label>
              <input
                {...register('fileName', { required: 'اسم الملف مطلوب' })}
                type="text"
                className="input w-full"
                placeholder="أدخل اسم الملف"
                disabled={uploading}
              />
              {errors.fileName && (
                <p className="mt-1 text-sm text-red-600">{errors.fileName.message}</p>
              )}
            </div>

            {/* File Upload Area */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اختيار الملف
              </label>
              <div
                className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                  dragActive
                    ? 'border-purple-400 bg-purple-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  {...register('file', { required: 'الملف مطلوب' })}
                  type="file"
                  accept=".pdf"
                  onChange={handleFileSelect}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  disabled={uploading}
                />
                
                {selectedFile ? (
                  <div className="space-y-2">
                    <FileText className="h-12 w-12 text-purple-500 mx-auto" />
                    <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                    <p className="text-xs text-gray-500">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                    <p className="text-sm text-gray-600">
                      اسحب وأفلت ملف PDF هنا أو انقر للاختيار
                    </p>
                    <p className="text-xs text-gray-500">
                      يُسمح بملفات PDF فقط (حد أقصى 10 MB)
                    </p>
                  </div>
                )}
              </div>
              {errors.file && (
                <p className="mt-1 text-sm text-red-600">{errors.file.message}</p>
              )}
            </div>

            {/* Upload Progress */}
            {uploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">جاري الرفع...</span>
                  <span className="text-gray-600">{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Warning */}
            <div className="flex items-start space-x-3 space-x-reverse p-4 bg-yellow-50 rounded-lg">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">تنبيه:</p>
                <p>سيتم إرسال هذا الملف إلى جميع المستخدمين المصرح لهم وعرضه على شاشات العرض تلقائياً.</p>
              </div>
            </div>

            {/* Buttons */}
            <div className="flex items-center justify-end space-x-3 space-x-reverse pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn btn-secondary"
                disabled={uploading}
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="btn btn-primary flex items-center space-x-2 space-x-reverse"
                disabled={uploading}
              >
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>جاري النشر...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    <span>نشر الملف</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default PublishModal;
