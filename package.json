{"name": "tv-office-management-system", "version": "1.0.0", "description": "نظام إدارة وعرض الملفات للتلفزيونات في المكتب", "main": "index.js", "scripts": {"setup": "node setup.js", "start": "node run-system.js", "server": "node websocket-server.js", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build"}, "keywords": ["tv", "office", "management", "display", "files"], "author": "TV Office System", "license": "MIT", "dependencies": {"ws": "^8.14.2"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.1"}}