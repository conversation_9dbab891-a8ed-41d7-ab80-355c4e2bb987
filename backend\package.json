{"name": "tv-office-backend", "version": "1.0.0", "description": "Backend API for TV Office Management System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node src/seed.js", "db:setup": "npm run db:push && npm run db:generate && npm run db:seed"}, "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "prisma": "^5.7.1", "socket.io": "^4.7.4", "sqlite3": "^5.1.7", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}