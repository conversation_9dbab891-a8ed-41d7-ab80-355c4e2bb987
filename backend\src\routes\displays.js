const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { body, validationResult } = require('express-validator');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// الحصول على جميع الشاشات
router.get('/', authenticateToken, async (req, res) => {
  try {
    const displays = await prisma.display.findMany({
      include: {
        operator: {
          select: {
            id: true,
            fullName: true,
            username: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(displays);
  } catch (error) {
    console.error('Get displays error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// إضافة شاشة جديدة (لمسؤول الاستعلامات فقط)
router.post('/', authenticateToken, authorizeRoles('DISPLAY_OP'), [
  body('screenId').notEmpty().withMessage('معرف الشاشة مطلوب'),
  body('screenName').notEmpty().withMessage('اسم الشاشة مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { screenId, screenName } = req.body;

    // التحقق من عدم وجود معرف الشاشة مسبقاً
    const existingDisplay = await prisma.display.findUnique({
      where: { screenId }
    });

    if (existingDisplay) {
      return res.status(400).json({ message: 'معرف الشاشة موجود مسبقاً' });
    }

    // إنشاء الشاشة الجديدة
    const newDisplay = await prisma.display.create({
      data: {
        screenId,
        screenName,
        operatorId: req.user.id
      },
      include: {
        operator: {
          select: {
            id: true,
            fullName: true,
            username: true
          }
        }
      }
    });

    res.status(201).json({
      message: 'تم إضافة الشاشة بنجاح',
      display: newDisplay
    });
  } catch (error) {
    console.error('Create display error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحديث حالة الاتصال للشاشة
router.patch('/:id/connection', authenticateToken, authorizeRoles('DISPLAY_OP'), [
  body('isConnected').isBoolean().withMessage('حالة الاتصال يجب أن تكون true أو false')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { id } = req.params;
    const { isConnected } = req.body;

    // التحقق من أن الشاشة تخص المستخدم الحالي
    const display = await prisma.display.findFirst({
      where: { 
        id, 
        operatorId: req.user.id 
      }
    });

    if (!display) {
      return res.status(404).json({ message: 'الشاشة غير موجودة أو ليس لديك صلاحية للوصول إليها' });
    }

    const updatedDisplay = await prisma.display.update({
      where: { id },
      data: { isConnected },
      include: {
        operator: {
          select: {
            id: true,
            fullName: true,
            username: true
          }
        }
      }
    });

    // إرسال إشعار عبر Socket.io
    const io = req.app.get('io');
    if (io) {
      io.emit('displayConnectionChanged', {
        display: updatedDisplay
      });
    }

    res.json({
      message: 'تم تحديث حالة الاتصال بنجاح',
      display: updatedDisplay
    });
  } catch (error) {
    console.error('Update display connection error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// حذف شاشة
router.delete('/:id', authenticateToken, authorizeRoles('DISPLAY_OP'), async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من أن الشاشة تخص المستخدم الحالي
    const display = await prisma.display.findFirst({
      where: { 
        id, 
        operatorId: req.user.id 
      }
    });

    if (!display) {
      return res.status(404).json({ message: 'الشاشة غير موجودة أو ليس لديك صلاحية للوصول إليها' });
    }

    await prisma.display.delete({
      where: { id }
    });

    res.json({ message: 'تم حذف الشاشة بنجاح' });
  } catch (error) {
    console.error('Delete display error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الشاشات الخاصة بالمستخدم الحالي
router.get('/my-displays', authenticateToken, authorizeRoles('DISPLAY_OP'), async (req, res) => {
  try {
    const displays = await prisma.display.findMany({
      where: { operatorId: req.user.id },
      include: {
        operator: {
          select: {
            id: true,
            fullName: true,
            username: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(displays);
  } catch (error) {
    console.error('Get my displays error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
