const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const { supabase } = require('../config/supabase');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();

// جلب جميع المستخدمين
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('id, username, full_name, job_title, department, user_type, is_active, created_at, updated_at')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json(users);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'خطأ في جلب المستخدمين' });
  }
});

// جلب مستخدم محدد
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: user, error } = await supabase
      .from('users')
      .select('id, username, full_name, job_title, department, user_type, is_active, created_at, updated_at')
      .eq('id', id)
      .single();

    if (error || !user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json(user);
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'خطأ في جلب بيانات المستخدم' });
  }
});

// إضافة مستخدم جديد
router.post('/', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN'),
  body('username').isLength({ min: 3 }).withMessage('اسم المستخدم يجب أن يكون 3 أحرف على الأقل'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  body('fullName').notEmpty().withMessage('الاسم الكامل مطلوب'),
  body('jobTitle').notEmpty().withMessage('المسمى الوظيفي مطلوب'),
  body('department').notEmpty().withMessage('القسم مطلوب'),
  body('userType').isIn(['MAIN_ADMIN', 'OPERATIONS_MGR', 'DEPT_MANAGER', 'DISPLAY_OP']).withMessage('نوع المستخدم غير صالح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { username, password, fullName, jobTitle, department, userType } = req.body;

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('username', username)
      .single();

    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود مسبقاً' });
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 12);

    // إضافة المستخدم الجديد
    const { data: newUser, error } = await supabase
      .from('users')
      .insert({
        username,
        password: hashedPassword,
        full_name: fullName,
        job_title: jobTitle,
        department,
        user_type: userType,
        is_active: true
      })
      .select('id, username, full_name, job_title, department, user_type, is_active, created_at')
      .single();

    if (error) {
      throw error;
    }

    res.status(201).json({
      message: 'تم إضافة المستخدم بنجاح',
      user: newUser
    });

  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ message: 'خطأ في إضافة المستخدم' });
  }
});

// تحديث بيانات مستخدم
router.put('/:id', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN'),
  body('fullName').optional().notEmpty().withMessage('الاسم الكامل لا يمكن أن يكون فارغاً'),
  body('jobTitle').optional().notEmpty().withMessage('المسمى الوظيفي لا يمكن أن يكون فارغاً'),
  body('department').optional().notEmpty().withMessage('القسم لا يمكن أن يكون فارغاً'),
  body('userType').optional().isIn(['MAIN_ADMIN', 'OPERATIONS_MGR', 'DEPT_MANAGER', 'DISPLAY_OP']).withMessage('نوع المستخدم غير صالح'),
  body('password').optional().isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { id } = req.params;
    const { fullName, jobTitle, department, userType, password, isActive } = req.body;

    // التحقق من وجود المستخدم
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('id')
      .eq('id', id)
      .single();

    if (fetchError || !existingUser) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // إعداد البيانات للتحديث
    const updateData = {};
    if (fullName !== undefined) updateData.full_name = fullName;
    if (jobTitle !== undefined) updateData.job_title = jobTitle;
    if (department !== undefined) updateData.department = department;
    if (userType !== undefined) updateData.user_type = userType;
    if (isActive !== undefined) updateData.is_active = isActive;

    // تشفير كلمة المرور الجديدة إذا تم توفيرها
    if (password) {
      updateData.password = await bcrypt.hash(password, 12);
    }

    // تحديث البيانات
    const { data: updatedUser, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', id)
      .select('id, username, full_name, job_title, department, user_type, is_active, created_at, updated_at')
      .single();

    if (error) {
      throw error;
    }

    res.json({
      message: 'تم تحديث بيانات المستخدم بنجاح',
      user: updatedUser
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ message: 'خطأ في تحديث بيانات المستخدم' });
  }
});

// حذف مستخدم
router.delete('/:id', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN')
], async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من عدم حذف المستخدم الحالي
    if (id === req.user.id) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الخاص' });
    }

    // التحقق من وجود المستخدم
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('id, username')
      .eq('id', id)
      .single();

    if (fetchError || !existingUser) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // حذف المستخدم
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    res.json({ message: 'تم حذف المستخدم بنجاح' });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ message: 'خطأ في حذف المستخدم' });
  }
});

// جلب طلبات إعادة تعيين كلمة المرور
router.get('/password-reset-requests', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN')
], async (req, res) => {
  try {
    const { data: requests, error } = await supabase
      .from('password_reset_requests')
      .select(`
        id,
        message,
        is_read,
        created_at,
        user:users!inner(id, username, full_name)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json(requests);
  } catch (error) {
    console.error('Get password reset requests error:', error);
    res.status(500).json({ message: 'خطأ في جلب طلبات إعادة تعيين كلمة المرور' });
  }
});

module.exports = router;
