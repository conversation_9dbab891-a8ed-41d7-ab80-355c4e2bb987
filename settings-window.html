<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة التلفزيونات المكتبية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden;
        }
        
        /* Window Frame */
        .window-frame {
            width: 90vw;
            height: 80vh;
            max-width: 1400px;
            background: white;
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 12px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }
        
        /* Title Bar */
        .title-bar {
            background: #e5e5e5;
            height: 40px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            border-bottom: 1px solid #ccc;
        }

        .title-content {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #333;
            font-weight: 500;
            flex: 1;
        }

        .window-controls {
            display: flex;
            gap: 0;
            margin-left: auto;
        }
        
        .control-btn {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            background: transparent;
            color: #333;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: #f0f0f0;
        }

        .minimize { }
        .maximize { }
        .close {
            color: #dc2626;
        }

        .close:hover {
            background: #dc2626;
            color: white;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            width: 100%;
        }
        
        /* Tab Navigation */
        .tab-navigation {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            margin-top: 25px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e5e5e5;
            width: 100%;
        }

        .tab-btn {
            background: none;
            color: #6b7280;
            border: none;
            padding: 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            font-weight: 700;
            transition: all 0.3s ease;
            font-size: 18px;
            text-align: center;
        }

        .tab-btn.active {
            color: #1e40af;
        }

        .tab-btn:hover:not(.active) {
            color: #4b5563;
        }

        .tab-btn svg {
            transition: all 0.3s ease;
        }

        .tab-btn.active svg {
            stroke: #1e40af;
        }
        
        /* Tab Content */
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Add Users Tab */
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #000;
            margin-bottom: 15px;
        }

        .form-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
            width: 100%;
        }

        .form-section {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            width: 100%;
        }

        .form-section h3 {
            font-size: 16px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
            background: white;
            height: 38px;
            font-weight: 500;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #1e40af;
            box-shadow: 0 0 0 1px rgba(30, 64, 175, 0.1);
        }

        .save-btn {
            background: #1e40af;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            width: 100%;
            margin-top: 8px;
            font-size: 16px;
            height: 38px;
        }

        .save-btn-small {
            background: #1e40af;
            color: white;
            border: none;
            padding: 0 80px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            width: auto;
            margin: 15px auto 0;
            font-size: 16px;
            height: 38px;
            display: block;
        }

        .save-btn:hover {
            background: #1e3a8a;
        }
        
        /* Permissions Tab */
        .permissions-container {
            width: 100%;
        }

        .permission-form {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            margin-bottom: 15px;
            width: 100%;
        }

        .permission-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        /* Table Styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            font-size: 12px;
        }

        .data-table th {
            background: #f3f4f6;
            padding: 8px 10px;
            text-align: right;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            font-size: 12px;
            height: 35px;
        }

        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            color: #374151;
            font-size: 14px;
            height: 35px;
            font-weight: 500;
        }

        .data-table tr:hover {
            background: #dbeafe;
            transition: background-color 0.2s ease;
        }

        .action-btns {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .edit-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: #6b7280;
            padding: 4px;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .edit-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .delete-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: #6b7280;
            padding: 4px;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .delete-btn:hover {
            background: #fef2f2;
            color: #dc2626;
        }
        
        /* Password Reset Tab */
        .notification-badge {
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
        }
        
        .request-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px 15px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
            height: 50px;
        }

        .request-item.new-request {
            background: #fef2f2;
            border-color: #fecaca;
            border-width: 2px;
        }

        .request-item:hover {
            background: #dbeafe;
            border-color: #93c5fd;
        }

        .request-item.new-request:hover {
            background: #dbeafe;
            border-color: #93c5fd;
        }

        .request-info {
            display: flex;
            align-items: center;
            gap: 20px;
            flex: 1;
        }

        .request-info h4 {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            min-width: 120px;
        }

        .request-info .department {
            font-size: 13px;
            color: #6b7280;
            min-width: 100px;
        }

        .request-info .program-type {
            font-size: 13px;
            color: #1e40af;
            font-weight: 500;
            min-width: 120px;
        }

        .request-info .date {
            font-size: 12px;
            color: #9ca3af;
        }
        
        /* Modal */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-overlay.active {
            display: flex;
        }
        
        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 25px;
            width: 450px;
            max-width: 90vw;
            border: 3px solid #3b82f6;
        }

        .modal-header {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #1e40af;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }
        
        .btn-cancel {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .btn-confirm {
            background: #1e40af;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 2000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="window-frame">
        <!-- Title Bar -->
        <div class="title-bar">
            <div class="title-content">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                    <circle cx="12" cy="12" r="3"/>
                </svg>
                <span style="font-size: 16px; font-weight: 600;">الإعدادات</span>
            </div>
            <div class="window-controls">
                <button class="control-btn close" onclick="closeWindow()">×</button>
                <button class="control-btn maximize" onclick="maximizeWindow()">□</button>
                <button class="control-btn minimize" onclick="minimizeWindow()">−</button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Tab Navigation -->
            <div class="tab-navigation">
                <button class="tab-btn active" onclick="switchTab('users')" id="usersTab">
                    <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                    </svg>
                    إضافة المعلومات
                </button>
                <button class="tab-btn" onclick="switchTab('permissions')" id="permissionsTab">
                    <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                    </svg>
                    الصلاحيات
                </button>
                <button class="tab-btn" onclick="switchTab('password')" id="passwordTab">
                    <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                    </svg>
                    إعادة تعيين كلمة المرور
                    <span class="notification-badge" id="passwordNotificationBadge">1</span>
                </button>
            </div>

            <!-- Tab Contents -->
            
            <!-- Add Users Tab -->
            <div class="tab-content active" id="usersContent">
                <div class="section-title">إضافة معلومات المستخدمين</div>
                
                <div class="form-container">
                    <!-- Input Section -->
                    <div class="form-section">
                        <h3>إدخال معلومات</h3>
                        
                        <div class="form-group">
                            <label>اسم المستخدم</label>
                            <input type="text" class="form-input" id="userName" placeholder="أدخل اسم المستخدم">
                        </div>
                        
                        <div class="form-group">
                            <label>المسمى الوظيفي</label>
                            <input type="text" class="form-input" id="jobTitle" placeholder="أدخل المسمى الوظيفي">
                        </div>
                        
                        <div class="form-group">
                            <label>القسم</label>
                            <input type="text" class="form-input" id="department" placeholder="أدخل اسم القسم">
                        </div>
                        
                        <button class="save-btn" onclick="saveUserInfo()">حفظ</button>
                    </div>
                    
                    <!-- Selection Lists Section -->
                    <div class="form-section">
                        <h3>اختيار القائمة</h3>
                        
                        <div class="form-group">
                            <label>الاختيارات لأسماء المستخدمين</label>
                            <select class="form-select" id="userNameList">
                                <option value="">اختر اسم المستخدم</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>الاختيارات مسمى الوظيفي</label>
                            <select class="form-select" id="jobTitleList">
                                <option value="">اختر المسمى الوظيفي</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>الاختيارات للأقسام</label>
                            <select class="form-select" id="departmentList">
                                <option value="">اختر القسم</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Permissions Tab -->
            <div class="tab-content" id="permissionsContent">
                <div class="section-title">إدارة الصلاحيات</div>
                
                <div class="permissions-container">
                    <div class="permission-form">
                        <h3 style="margin-bottom: 15px; font-weight: 600;">منح صلاحيات جديدة</h3>
                        
                        <div class="permission-inputs">
                            <div class="form-group">
                                <label>اختيار اسم المستخدم</label>
                                <select class="form-select" id="permissionUser">
                                    <option value="">اختر المستخدم</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>اختيار نوع البرنامج</label>
                                <select class="form-select" id="programType">
                                    <option value="">اختر نوع البرنامج</option>
                                    <option value="مدير رئيسي">مدير رئيسي</option>
                                    <option value="مدير تشغيل">مدير تشغيل</option>
                                    <option value="مدير قسم">مدير قسم</option>
                                    <option value="مسؤول استعلامات">مسؤول استعلامات</option>
                                </select>
                            </div>
                        </div>
                        
                        <button class="save-btn-small" onclick="savePermission()">حفظ</button>
                    </div>
                    
                    <!-- Permissions Table -->
                    <table class="data-table" id="permissionsTable">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>نوع الصلاحيات</th>
                                <th>التاريخ والوقت</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="permissionsTableBody">
                            <!-- Data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Password Reset Tab -->
            <div class="tab-content" id="passwordContent">
                <div class="section-title">إعادة تعيين كلمة المرور بطلب من المستخدم</div>
                
                <div id="passwordRequests">
                    <!-- Password reset requests will be shown here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Password Reset Modal -->
    <div class="modal-overlay" id="passwordModal">
        <div class="modal-content">
            <div class="modal-header">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                    <circle cx="12" cy="16" r="1"/>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                </svg>
                إعادة تعيين كلمة مرور جديدة
            </div>
            
            <div class="form-group">
                <label>اسم المستخدم</label>
                <input type="text" class="form-input" id="modalUserName" readonly>
            </div>
            
            <div class="form-group">
                <label>كلمة المرور الجديدة</label>
                <input type="password" class="form-input" id="newPassword" placeholder="أدخل كلمة المرور الجديدة">
            </div>
            
            <div class="modal-buttons">
                <button class="btn-cancel" onclick="closePasswordModal()">إلغاء</button>
                <button class="btn-confirm" onclick="confirmPasswordReset()">حفظ</button>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification"></div>

    <script>
        // Data Storage
        let users = [];
        let permissions = [];
        let passwordRequests = [
            {
                id: 1,
                userName: 'أحمد محمد علي',
                jobTitle: 'مدير التشغيل',
                department: 'العمليات',
                programType: 'مدير تشغيل',
                requestDate: new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                }) + ' ' + new Date().toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                }),
                isNew: true
            }
        ];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updatePasswordRequests();
            updatePasswordNotificationBadge();

            // Initialize tab colors
            document.querySelectorAll('.tab-btn').forEach(btn => {
                const svg = btn.querySelector('svg');
                if (svg) {
                    if (btn.classList.contains('active')) {
                        svg.style.stroke = '#1e40af';
                    } else {
                        svg.style.stroke = '#6b7280';
                    }
                }
            });
        });

        // Window Controls
        function minimizeWindow() {
            showNotification('تم تصغير النافذة');
        }

        function maximizeWindow() {
            showNotification('تم تكبير النافذة');
        }

        function closeWindow() {
            if (confirm('هل تريد إغلاق نافذة الإعدادات؟')) {
                // Try to go back to main page
                if (window.opener) {
                    window.close();
                } else {
                    // If opened directly, redirect to main page
                    window.location.href = 'complete-system.html';
                }
            }
        }

        // Tab Switching
        function switchTab(tabName) {
            // Remove active class from all tabs and contents
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
                // Reset SVG stroke color
                const svg = btn.querySelector('svg');
                if (svg) {
                    svg.style.stroke = '#6b7280';
                }
            });
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to selected tab and content
            const activeTab = document.getElementById(tabName + 'Tab');
            activeTab.classList.add('active');

            // Set active SVG stroke color
            const activeSvg = activeTab.querySelector('svg');
            if (activeSvg) {
                activeSvg.style.stroke = '#1e40af';
            }

            document.getElementById(tabName + 'Content').classList.add('active');
        }

        // User Management Functions
        function saveUserInfo() {
            const userName = document.getElementById('userName').value.trim();
            const jobTitle = document.getElementById('jobTitle').value.trim();
            const department = document.getElementById('department').value.trim();

            if (!userName || !jobTitle || !department) {
                showNotification('يرجى ملء جميع الحقول', 'error');
                return;
            }

            // Check if user already exists
            if (users.find(user => user.userName === userName)) {
                showNotification('اسم المستخدم موجود بالفعل', 'error');
                return;
            }

            // Add new user
            const newUser = {
                id: users.length + 1,
                userName: userName,
                jobTitle: jobTitle,
                department: department,
                createdDate: new Date().toLocaleString('ar-SA')
            };

            users.push(newUser);
            updateSelectLists();
            clearUserForm();
            showNotification('تم حفظ معلومات المستخدم بنجاح');
        }

        function clearUserForm() {
            document.getElementById('userName').value = '';
            document.getElementById('jobTitle').value = '';
            document.getElementById('department').value = '';
        }

        function updateSelectLists() {
            const userNameList = document.getElementById('userNameList');
            const jobTitleList = document.getElementById('jobTitleList');
            const departmentList = document.getElementById('departmentList');
            const permissionUserList = document.getElementById('permissionUser');

            // Clear existing options (except first)
            [userNameList, jobTitleList, departmentList, permissionUserList].forEach(select => {
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }
            });

            // Add user names
            users.forEach(user => {
                const option1 = new Option(user.userName, user.userName);
                const option2 = new Option(user.userName, user.userName);
                userNameList.appendChild(option1);
                permissionUserList.appendChild(option2);
            });

            // Add unique job titles
            const uniqueJobTitles = [...new Set(users.map(user => user.jobTitle))];
            uniqueJobTitles.forEach(jobTitle => {
                const option = new Option(jobTitle, jobTitle);
                jobTitleList.appendChild(option);
            });

            // Add unique departments
            const uniqueDepartments = [...new Set(users.map(user => user.department))];
            uniqueDepartments.forEach(department => {
                const option = new Option(department, department);
                departmentList.appendChild(option);
            });
        }

        // Permissions Management
        function savePermission() {
            const userName = document.getElementById('permissionUser').value;
            const programType = document.getElementById('programType').value;

            if (!userName || !programType) {
                showNotification('يرجى اختيار المستخدم ونوع البرنامج', 'error');
                return;
            }

            // Check if permission already exists
            if (permissions.find(p => p.userName === userName && p.programType === programType)) {
                showNotification('هذه الصلاحية موجودة بالفعل للمستخدم', 'error');
                return;
            }

            const newPermission = {
                id: permissions.length + 1,
                userName: userName,
                programType: programType,
                dateTime: new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                }) + ' ' + new Date().toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                })
            };

            permissions.push(newPermission);
            updatePermissionsTable();
            clearPermissionForm();
            showNotification('تم منح الصلاحية بنجاح');
        }

        function clearPermissionForm() {
            document.getElementById('permissionUser').value = '';
            document.getElementById('programType').value = '';
        }

        function updatePermissionsTable() {
            const tbody = document.getElementById('permissionsTableBody');
            tbody.innerHTML = '';

            permissions.forEach(permission => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${permission.userName}</td>
                    <td>${permission.programType}</td>
                    <td>${permission.dateTime}</td>
                    <td>
                        <div class="action-btns">
                            <button class="edit-btn" onclick="editPermission(${permission.id})">تعديل</button>
                            <button class="delete-btn" onclick="deletePermission(${permission.id})">حذف</button>
                        </div>
                    </td>
                `;
            });
        }

        function editPermission(id) {
            const permission = permissions.find(p => p.id === id);
            if (permission) {
                document.getElementById('permissionUser').value = permission.userName;
                document.getElementById('programType').value = permission.programType;

                // Remove the old permission
                permissions = permissions.filter(p => p.id !== id);
                updatePermissionsTable();

                showNotification('يمكنك الآن تعديل الصلاحية');
            }
        }

        function deletePermission(id) {
            if (confirm('هل تريد حذف هذه الصلاحية؟')) {
                permissions = permissions.filter(p => p.id !== id);
                updatePermissionsTable();
                showNotification('تم حذف الصلاحية');
            }
        }

        // Password Reset Management
        function updatePasswordRequests() {
            const container = document.getElementById('passwordRequests');
            container.innerHTML = '';

            if (passwordRequests.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 40px;">لا توجد طلبات إعادة تعيين كلمة المرور</p>';
                return;
            }

            passwordRequests.forEach(request => {
                const requestElement = document.createElement('div');
                requestElement.className = request.isNew ? 'request-item new-request' : 'request-item';
                requestElement.onclick = () => openPasswordModal(request);

                requestElement.innerHTML = `
                    <div class="request-info">
                        <h4>${request.userName}</h4>
                        <span class="department">${request.department}</span>
                        <span class="program-type">${request.programType}</span>
                        <span class="date">${request.requestDate}</span>
                    </div>
                    <div class="action-btns">
                        <button class="delete-btn" onclick="event.stopPropagation(); deletePasswordRequest(${request.id})" title="حذف">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"/>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                                <line x1="10" y1="11" x2="10" y2="17"/>
                                <line x1="14" y1="11" x2="14" y2="17"/>
                            </svg>
                        </button>
                    </div>
                `;

                container.appendChild(requestElement);
            });
        }

        function updatePasswordNotificationBadge() {
            const badge = document.getElementById('passwordNotificationBadge');
            const count = passwordRequests.length;

            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }

        function openPasswordModal(request) {
            document.getElementById('modalUserName').value = request.userName;
            document.getElementById('newPassword').value = '';
            document.getElementById('passwordModal').classList.add('active');

            // Mark request as opened (no longer new)
            const requestIndex = passwordRequests.findIndex(req => req.id === request.id);
            if (requestIndex !== -1) {
                passwordRequests[requestIndex].isNew = false;
                updatePasswordRequests();
                updatePasswordNotificationBadge();
            }

            // Store current request ID for later use
            document.getElementById('passwordModal').dataset.requestId = request.id;
        }

        function closePasswordModal() {
            document.getElementById('passwordModal').classList.remove('active');
            document.getElementById('newPassword').value = '';
        }

        function confirmPasswordReset() {
            const newPassword = document.getElementById('newPassword').value.trim();
            const requestId = parseInt(document.getElementById('passwordModal').dataset.requestId);

            if (!newPassword) {
                showNotification('يرجى إدخال كلمة المرور الجديدة', 'error');
                return;
            }

            if (newPassword.length < 6) {
                showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
                return;
            }

            // Remove the request from the list
            passwordRequests = passwordRequests.filter(req => req.id !== requestId);

            updatePasswordRequests();
            updatePasswordNotificationBadge();
            closePasswordModal();

            showNotification('تم إعادة تعيين كلمة المرور بنجاح');
        }

        function deletePasswordRequest(id) {
            if (confirm('هل تريد حذف طلب إعادة تعيين كلمة المرور؟')) {
                passwordRequests = passwordRequests.filter(req => req.id !== id);
                updatePasswordRequests();
                updatePasswordNotificationBadge();
                showNotification('تم حذف الطلب');
            }
        }

        // Notification System
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = 'notification show';

            if (type === 'error') {
                notification.style.background = '#ef4444';
            } else {
                notification.style.background = '#10b981';
            }

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Close modal when clicking outside
        document.getElementById('passwordModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePasswordModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePasswordModal();
            }
        });
    </script>
</body>
</html>
