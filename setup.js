const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 إعداد نظام إدارة التلفزيونات المكتبية...\n');

try {
  // تثبيت متطلبات Backend
  console.log('📦 تثبيت متطلبات Backend...');
  process.chdir(path.join(__dirname, 'backend'));
  execSync('npm install', { stdio: 'inherit' });

  // تثبيت متطلبات Frontend
  console.log('\n📦 تثبيت متطلبات Frontend...');
  process.chdir(path.join(__dirname, 'frontend'));
  execSync('npm install', { stdio: 'inherit' });

  // العودة للمجلد الرئيسي
  process.chdir(__dirname);

  console.log('\n✅ تم إعداد النظام بنجاح!');
  console.log('\n🚀 لتشغيل النظام استخدم:');
  console.log('node run-system.js');
  
} catch (error) {
  console.error('❌ خطأ في الإعداد:', error.message);
  process.exit(1);
}
