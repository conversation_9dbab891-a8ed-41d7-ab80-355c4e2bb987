const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 بدء تهيئة قاعدة البيانات...');

  // إنشاء المدير الرئيسي الافتراضي
  const hashedPassword = await bcrypt.hash('admin123', 12);
  
  const adminUser = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      password: hashedPassword,
      fullName: 'المدير الرئيسي',
      jobTitle: 'مدير النظام',
      department: 'تقنية المعلومات',
      userType: 'MAIN_ADMIN',
      isActive: true
    }
  });

  console.log('✅ تم إنشاء المدير الرئيسي:', adminUser.username);

  // إنشاء مستخدمين تجريبيين
  const users = [
    {
      username: 'operations_mgr',
      password: await bcrypt.hash('123456', 12),
      fullName: 'أحمد محمد علي',
      jobTitle: 'مدير التشغيل',
      department: 'العمليات',
      userType: 'OPERATIONS_MGR'
    },
    {
      username: 'dept_mgr_hr',
      password: await bcrypt.hash('123456', 12),
      fullName: 'فاطمة أحمد سالم',
      jobTitle: 'مدير الموارد البشرية',
      department: 'الموارد البشرية',
      userType: 'DEPT_MANAGER'
    },
    {
      username: 'dept_mgr_finance',
      password: await bcrypt.hash('123456', 12),
      fullName: 'محمد سعد الدين',
      jobTitle: 'مدير المالية',
      department: 'المالية',
      userType: 'DEPT_MANAGER'
    },
    {
      username: 'display_op',
      password: await bcrypt.hash('123456', 12),
      fullName: 'علي حسن محمد',
      jobTitle: 'مسؤول الاستعلامات',
      department: 'خدمة العملاء',
      userType: 'DISPLAY_OP'
    }
  ];

  for (const userData of users) {
    const user = await prisma.user.upsert({
      where: { username: userData.username },
      update: {},
      create: {
        ...userData,
        isActive: true
      }
    });
    console.log('✅ تم إنشاء المستخدم:', user.username);
  }

  // إنشاء شاشات عرض تجريبية
  const displayOperator = await prisma.user.findUnique({
    where: { username: 'display_op' }
  });

  if (displayOperator) {
    const displays = [
      {
        screenId: 'TV-001',
        screenName: 'شاشة الاستقبال الرئيسية',
        operatorId: displayOperator.id
      },
      {
        screenId: 'TV-002',
        screenName: 'شاشة قاعة الاجتماعات',
        operatorId: displayOperator.id
      }
    ];

    for (const displayData of displays) {
      const display = await prisma.display.upsert({
        where: { screenId: displayData.screenId },
        update: {},
        create: displayData
      });
      console.log('✅ تم إنشاء الشاشة:', display.screenName);
    }
  }

  console.log('🎉 تم الانتهاء من تهيئة قاعدة البيانات بنجاح!');
  console.log('\n📋 بيانات تسجيل الدخول:');
  console.log('المدير الرئيسي: admin / admin123');
  console.log('مدير التشغيل: operations_mgr / 123456');
  console.log('مدير الموارد البشرية: dept_mgr_hr / 123456');
  console.log('مدير المالية: dept_mgr_finance / 123456');
  console.log('مسؤول الاستعلامات: display_op / 123456');
}

main()
  .catch((e) => {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
