const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');
const { body, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// تسجيل الدخول
router.post('/login', [
  body('username').notEmpty().withMessage('اسم المستخدم مطلوب'),
  body('password').notEmpty().withMessage('كلمة المرور مطلوبة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { username, password } = req.body;

    // البحث عن المستخدم
    const user = await prisma.user.findUnique({
      where: { username }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }

    // إنشاء JWT token
    const token = jwt.sign(
      { userId: user.id, userType: user.userType },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        id: user.id,
        username: user.username,
        fullName: user.fullName,
        jobTitle: user.jobTitle,
        department: user.department,
        userType: user.userType
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تسجيل الخروج
router.post('/logout', authenticateToken, (req, res) => {
  res.json({ message: 'تم تسجيل الخروج بنجاح' });
});

// التحقق من صحة الرمز المميز
router.get('/verify', authenticateToken, (req, res) => {
  res.json({
    message: 'الرمز المميز صالح',
    user: req.user
  });
});

// طلب إعادة تعيين كلمة المرور
router.post('/request-password-reset', [
  body('username').notEmpty().withMessage('اسم المستخدم مطلوب'),
  body('message').optional()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { username, message } = req.body;

    const user = await prisma.user.findUnique({
      where: { username }
    });

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // إنشاء طلب إعادة تعيين كلمة المرور
    await prisma.passwordResetRequest.create({
      data: {
        userId: user.id,
        message: message || 'طلب إعادة تعيين كلمة المرور'
      }
    });

    res.json({ message: 'تم إرسال طلب إعادة تعيين كلمة المرور' });
  } catch (error) {
    console.error('Password reset request error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
