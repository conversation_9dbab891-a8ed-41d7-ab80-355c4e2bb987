{"name": "tv-office-backend-cloud", "version": "1.0.0", "description": "Backend API for TV Office Management System - Cloud Version", "main": "api/index.js", "scripts": {"start": "node api/index.js", "dev": "nodemon api/index.js", "build": "echo 'Build complete'"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}, "engines": {"node": "18.x"}}