<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فرع C - برنامج المستخدمين</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: bold;
            color: #4a5568;
        }

        .branch-indicator {
            background: #3182ce;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
        }

        .main-container {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .publish-area {
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .publish-area:hover {
            border-color: #3182ce;
            background: rgba(49, 130, 206, 0.05);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3182ce;
            color: white;
        }

        .btn-primary:hover {
            background: #2c5aa0;
            transform: translateY(-2px);
        }

        .notification-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 500px;
            width: 90%;
            text-align: center;
            display: none;
        }

        .notification-popup.show {
            display: block;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translate(-50%, -60%); }
            to { opacity: 1; transform: translate(-50%, -50%); }
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .overlay.show { display: block; }

        .file-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .content-preview {
            width: 150px;
            height: 200px;
            margin: 0 auto 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
        }

        .status-bar {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #3182ce;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            🎯 فرع C - المستخدمين
        </div>
        <div class="branch-indicator">
            فرع C
        </div>
    </div>

    <div class="main-container">
        <div class="content-grid">
            <div class="section">
                <div class="section-title">
                    📤 نشر محتوى جديد
                </div>
                <div class="publish-area" onclick="publishContent()">
                    <div style="font-size: 48px; margin-bottom: 15px;">📚</div>
                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">انقر لنشر محتوى جديد</div>
                    <div style="color: #718096;">سيظهر على جميع الفروع تلقائياً</div>
                </div>
                <button class="btn btn-primary" onclick="publishContent()" style="width: 100%; margin-top: 20px;">
                    نشر محتوى تجريبي
                </button>
            </div>

            <div class="section">
                <div class="section-title">
                    📥 المحتوى المستلم
                </div>
                <div class="file-list" id="receivedContent">
                    <div style="text-align: center; color: #718096; padding: 40px;">
                        في انتظار محتوى جديد...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="overlay" id="overlay"></div>
    <div class="notification-popup" id="notificationPopup">
        <div class="content-preview" id="contentPreview">📖</div>
        <div style="font-size: 20px; font-weight: bold; margin-bottom: 10px;" id="contentTitle">محتوى جديد</div>
        <div style="color: #718096; margin-bottom: 20px;" id="contentAuthor">تم نشره للتو</div>
        <div style="display: flex; gap: 15px; justify-content: center;">
            <button class="btn btn-primary" onclick="saveContent()">حفظ في المستلمة</button>
            <button class="btn" onclick="closeNotification()" style="background: #e2e8f0; color: #4a5568;">إغلاق</button>
        </div>
    </div>

    <div class="status-bar">
        <div class="status-dot"></div>
        <span>متصل - فرع C</span>
    </div>

    <script>
        let currentNotification = null;
        let permissions = { publish: true, view: true, manage: true, delete: false };

        // تحديث الصلاحيات من الفرع الرئيسي
        function updatePermissions() {
            const branchPermissions = localStorage.getItem('branchPermissions');
            if (branchPermissions) {
                const allPermissions = JSON.parse(branchPermissions);
                if (allPermissions.C) {
                    permissions = allPermissions.C.permissions;
                    updateUI();
                }
            }
        }

        // تحديث واجهة المستخدم حسب الصلاحيات
        function updateUI() {
            const publishBtn = document.querySelector('.btn-primary');
            const publishArea = document.querySelector('.publish-area');

            if (!permissions.publish) {
                publishBtn.disabled = true;
                publishBtn.textContent = '🚫 النشر غير مسموح';
                publishBtn.style.background = '#a0aec0';
                publishArea.style.opacity = '0.5';
                publishArea.style.pointerEvents = 'none';
            } else {
                publishBtn.disabled = false;
                publishBtn.textContent = 'نشر محتوى تجريبي';
                publishBtn.style.background = '#3182ce';
                publishArea.style.opacity = '1';
                publishArea.style.pointerEvents = 'auto';
            }

            if (!permissions.view) {
                document.getElementById('receivedContent').innerHTML =
                    '<div style="text-align: center; color: #e53e3e; padding: 40px;">🚫 ليس لديك صلاحية لعرض المحتوى</div>';
            }

            // إضافة مؤشر للصلاحيات المتقدمة
            if (permissions.manage) {
                const manageIndicator = document.createElement('div');
                manageIndicator.innerHTML = '⚙️ صلاحيات إدارية متقدمة';
                manageIndicator.style.cssText = 'background: #e6fffa; color: #234e52; padding: 8px 12px; border-radius: 6px; font-size: 12px; margin-top: 10px; text-align: center;';
                document.querySelector('.section-title').appendChild(manageIndicator);
            }
        }

        // محاكاة نشر محتوى
        function publishContent() {
            if (!permissions.publish) {
                alert('🚫 ليس لديك صلاحية للنشر!\n\nيرجى التواصل مع الإدارة للحصول على الصلاحيات المطلوبة.');
                return;
            }

            const sampleContent = [
                { title: 'دليل الجودة والمعايير', author: 'قسم ضمان الجودة', icon: '⭐', branch: 'C' },
                { title: 'إجراءات التدقيق الداخلي', author: 'قسم التدقيق', icon: '🔍', branch: 'C' },
                { title: 'معايير الأداء الجديدة', author: 'إدارة الجودة', icon: '📏', branch: 'C' },
                { title: 'تقرير التحسين المستمر', author: 'فريق التطوير', icon: '🔄', branch: 'C' }
            ];

            const content = sampleContent[Math.floor(Math.random() * sampleContent.length)];
            content.timestamp = Date.now();
            content.date = new Date().toLocaleDateString('ar-SA');

            // حفظ في التخزين المشترك
            localStorage.setItem('sharedContent', JSON.stringify(content));

            alert(`✅ تم نشر المحتوى بنجاح!

📄 ${content.title}
👤 بواسطة: ${content.author}
🏢 من: فرع ${content.branch}
📅 ${content.date}

سيظهر الآن على جميع الفروع (A, B, C, D)`);

            // إضافة للمحتوى المحلي
            addToReceived(content);
        }

        // مراقبة المحتوى الجديد
        function monitorContent() {
            const lastContent = localStorage.getItem('lastContentTimestamp_C') || '0';
            const sharedContent = localStorage.getItem('sharedContent');

            if (sharedContent) {
                const content = JSON.parse(sharedContent);
                if (content.timestamp > parseInt(lastContent) && content.branch !== 'C') {
                    showNotification(content);
                    localStorage.setItem('lastContentTimestamp_C', content.timestamp.toString());
                }
            }
        }

        // عرض الإشعار
        function showNotification(content) {
            currentNotification = content;
            document.getElementById('contentPreview').textContent = content.icon;
            document.getElementById('contentTitle').textContent = content.title;
            document.getElementById('contentAuthor').textContent = `من: ${content.author} - فرع ${content.branch}`;
            
            document.getElementById('overlay').classList.add('show');
            document.getElementById('notificationPopup').classList.add('show');
        }

        // حفظ المحتوى
        function saveContent() {
            if (currentNotification) {
                addToReceived(currentNotification);
                closeNotification();
                alert('تم حفظ المحتوى في المستلمة!');
            }
        }

        // إغلاق الإشعار
        function closeNotification() {
            document.getElementById('overlay').classList.remove('show');
            document.getElementById('notificationPopup').classList.remove('show');
            currentNotification = null;
        }

        // إضافة للمحتوى المستلم
        function addToReceived(content) {
            const received = JSON.parse(localStorage.getItem('branchC_received') || '[]');
            received.unshift(content);
            localStorage.setItem('branchC_received', JSON.stringify(received));
            loadReceived();
        }

        // تحميل المحتوى المستلم
        function loadReceived() {
            const received = JSON.parse(localStorage.getItem('branchC_received') || '[]');
            const container = document.getElementById('receivedContent');

            if (received.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #718096; padding: 40px;">لا يوجد محتوى مستلم</div>';
                return;
            }

            container.innerHTML = received.map(content => `
                <div class="file-item">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="font-size: 24px;">${content.icon}</div>
                        <div>
                            <div style="font-weight: 600; color: #2d3748;">${content.title}</div>
                            <div style="font-size: 14px; color: #718096;">من: ${content.author} - فرع ${content.branch}</div>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: #a0aec0;">${content.date}</div>
                </div>
            `).join('');
        }

        // تشغيل المراقبة
        setInterval(monitorContent, 1000);
        setInterval(updatePermissions, 2000); // مراقبة الصلاحيات كل ثانيتين
        loadReceived();
        updatePermissions(); // تحديث أولي

        // إغلاق الإشعار عند النقر على الخلفية
        document.getElementById('overlay').addEventListener('click', closeNotification);
    </script>
</body>
</html>
