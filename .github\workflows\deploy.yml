name: Deploy TV Office System

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  deploy-backend:
    name: Deploy Backend to Vercel
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: backend-cloud/package-lock.json

      - name: Install dependencies
        run: |
          cd backend-cloud
          npm ci

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: backend-cloud
          vercel-args: '--prod'

  deploy-frontend:
    name: Deploy Frontend to GitHub Pages
    runs-on: ubuntu-latest
    needs: deploy-backend
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        run: |
          cd frontend
          npm ci

      - name: Update API configuration
        run: |
          cd frontend
          # تحديث رابط Backend في ملف الإعداد
          sed -i 's|https://your-backend-url.vercel.app|${{ secrets.BACKEND_URL }}|g' src/config/api.ts

      - name: Build project
        run: |
          cd frontend
          npm run build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        if: github.ref == 'refs/heads/main'
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: frontend/dist
          cname: ${{ secrets.CUSTOM_DOMAIN }}

  test-deployment:
    name: Test Deployment
    runs-on: ubuntu-latest
    needs: [deploy-backend, deploy-frontend]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Test Backend Health
        run: |
          curl -f ${{ secrets.BACKEND_URL }}/api/health || exit 1

      - name: Test Frontend
        run: |
          curl -f https://${{ github.repository_owner }}.github.io/tv-office/ || exit 1

      - name: Notify Success
        if: success()
        run: |
          echo "✅ Deployment successful!"
          echo "🔗 Frontend: https://${{ github.repository_owner }}.github.io/tv-office/"
          echo "🔗 Backend: ${{ secrets.BACKEND_URL }}"

      - name: Notify Failure
        if: failure()
        run: |
          echo "❌ Deployment failed!"
          exit 1
