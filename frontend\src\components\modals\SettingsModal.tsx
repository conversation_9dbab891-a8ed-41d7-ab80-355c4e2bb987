import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { X, Plus, Edit, Trash2, Eye, EyeOff } from 'lucide-react';
import toast from 'react-hot-toast';
import Modal from '@/components/ui/Modal';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { User, CreateUserData, UserType, USER_TYPE_LABELS } from '@/types';
import api from '@/services/api';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<CreateUserData>();

  // جلب المستخدمين
  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await api.get<User[]>('/users');
      setUsers(response.data);
    } catch (error: any) {
      toast.error('خطأ في جلب المستخدمين');
      console.error('Fetch users error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  // إضافة مستخدم جديد
  const onSubmit = async (data: CreateUserData) => {
    try {
      setIsLoading(true);
      if (editingUser) {
        // تحديث مستخدم موجود
        await api.put(`/users/${editingUser.id}`, data);
        toast.success('تم تحديث المستخدم بنجاح');
      } else {
        // إضافة مستخدم جديد
        await api.post('/users', data);
        toast.success('تم إضافة المستخدم بنجاح');
      }

      await fetchUsers();
      setShowAddForm(false);
      setEditingUser(null);
      reset();
    } catch (error: any) {
      const message = error.response?.data?.message || 'خطأ في حفظ المستخدم';
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  // حذف مستخدم
  const deleteUser = async (userId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;

    try {
      setIsLoading(true);
      await api.delete(`/users/${userId}`);
      toast.success('تم حذف المستخدم بنجاح');
      await fetchUsers();
    } catch (error: any) {
      const message = error.response?.data?.message || 'خطأ في حذف المستخدم';
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  // تحرير مستخدم
  const editUser = (user: User) => {
    setEditingUser(user);
    setShowAddForm(true);
    reset({
      username: user.username,
      fullName: user.fullName,
      jobTitle: user.jobTitle,
      department: user.department,
      userType: user.userType,
      password: '' // لا نعرض كلمة المرور الحالية
    });
  };

  // إلغاء التحرير
  const cancelEdit = () => {
    setShowAddForm(false);
    setEditingUser(null);
    reset();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="إعدادات النظام">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900">إدارة المستخدمين</h3>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            إضافة مستخدم جديد
          </button>
        </div>

        {/* Add/Edit User Form */}
        {showAddForm && (
          <div className="bg-gray-50 p-6 rounded-lg mb-6">
            <h4 className="text-md font-semibold mb-4">
              {editingUser ? 'تحرير المستخدم' : 'إضافة مستخدم جديد'}
            </h4>

            <form onSubmit={handleSubmit(onSubmit)} className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* اسم المستخدم */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  اسم المستخدم
                </label>
                <input
                  {...register('username', { required: 'اسم المستخدم مطلوب' })}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!!editingUser} // لا يمكن تغيير اسم المستخدم عند التحرير
                />
                {errors.username && (
                  <p className="text-red-500 text-sm mt-1">{errors.username.message}</p>
                )}
              </div>

              {/* كلمة المرور */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  كلمة المرور {editingUser && '(اتركها فارغة للاحتفاظ بالحالية)'}
                </label>
                <div className="relative">
                  <input
                    {...register('password', editingUser ? {} : { required: 'كلمة المرور مطلوبة' })}
                    type={showPassword ? 'text' : 'password'}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
                )}
              </div>

              {/* الاسم الكامل */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم الكامل
                </label>
                <input
                  {...register('fullName', { required: 'الاسم الكامل مطلوب' })}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.fullName && (
                  <p className="text-red-500 text-sm mt-1">{errors.fullName.message}</p>
                )}
              </div>

              {/* المسمى الوظيفي */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المسمى الوظيفي
                </label>
                <input
                  {...register('jobTitle', { required: 'المسمى الوظيفي مطلوب' })}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.jobTitle && (
                  <p className="text-red-500 text-sm mt-1">{errors.jobTitle.message}</p>
                )}
              </div>

              {/* القسم */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  القسم
                </label>
                <input
                  {...register('department', { required: 'القسم مطلوب' })}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.department && (
                  <p className="text-red-500 text-sm mt-1">{errors.department.message}</p>
                )}
              </div>

              {/* نوع المستخدم */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  نوع المستخدم
                </label>
                <select
                  {...register('userType', { required: 'نوع المستخدم مطلوب' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">اختر نوع المستخدم</option>
                  {Object.entries(USER_TYPE_LABELS).map(([value, label]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>
                {errors.userType && (
                  <p className="text-red-500 text-sm mt-1">{errors.userType.message}</p>
                )}
              </div>

              {/* أزرار الحفظ والإلغاء */}
              <div className="md:col-span-2 flex gap-3 pt-4">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
                >
                  {isLoading && <LoadingSpinner size="sm" />}
                  {editingUser ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={cancelEdit}
                  className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Users List */}
        <div className="bg-white rounded-lg border">
          {isLoading && !showAddForm ? (
            <div className="flex justify-center items-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المستخدم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المسمى الوظيفي
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      القسم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      النوع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {user.fullName}
                          </div>
                          <div className="text-sm text-gray-500">
                            @{user.username}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {user.jobTitle}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {user.department}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {USER_TYPE_LABELS[user.userType as UserType]}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {user.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <button
                            onClick={() => editUser(user)}
                            className="text-blue-600 hover:text-blue-900"
                            title="تحرير"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => deleteUser(user.id)}
                            className="text-red-600 hover:text-red-900"
                            title="حذف"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {users.length === 0 && !isLoading && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد مستخدمين
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default SettingsModal;