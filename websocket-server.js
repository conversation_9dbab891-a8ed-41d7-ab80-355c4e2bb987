// خادم WebSocket للربط الحقيقي مع التلفزيونات
const WebSocket = require('ws');
const http = require('http');
const fs = require('fs');
const path = require('path');

// إنشاء خادم HTTP لتقديم الملفات
const server = http.createServer((req, res) => {
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './tv-display.html';
    }

    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html',
        '.js': 'text/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.wav': 'audio/wav',
        '.mp4': 'video/mp4',
        '.woff': 'application/font-woff',
        '.ttf': 'application/font-ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.otf': 'application/font-otf',
        '.wasm': 'application/wasm'
    };

    const contentType = mimeTypes[extname] || 'application/octet-stream';

    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code == 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 - File Not Found</h1>', 'utf-8');
            } else {
                res.writeHead(500);
                res.end('Sorry, check with the site admin for error: ' + error.code + ' ..\n');
            }
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content, 'utf-8');
        }
    });
});

// إنشاء خادم WebSocket
const wss = new WebSocket.Server({ server });

// قائمة الشاشات المتصلة
const connectedDisplays = new Map();

// معالجة الاتصالات الجديدة
wss.on('connection', (ws, req) => {
    const clientIP = req.socket.remoteAddress;
    console.log(`🔗 اتصال جديد من: ${clientIP}`);

    // معالجة الرسائل الواردة
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            console.log(`📨 رسالة من ${clientIP}:`, data);

            switch (data.type) {
                case 'register_display':
                    // تسجيل شاشة عرض جديدة
                    const securityCode = generateSecurityCode();
                    connectedDisplays.set(ws, {
                        ip: clientIP,
                        securityCode: securityCode,
                        name: data.name || 'شاشة غير مسماة',
                        connected: false
                    });
                    
                    // إرسال رمز الأمان للشاشة
                    ws.send(JSON.stringify({
                        type: 'security_code',
                        code: securityCode,
                        ip: getLocalIP()
                    }));
                    
                    console.log(`🔐 تم إنشاء رمز أمان للشاشة: ${securityCode}`);
                    break;

                case 'auth':
                    // محاولة مصادقة من البرنامج الرئيسي
                    const display = findDisplayByCode(data.token);
                    if (display) {
                        display.connected = true;
                        display.name = data.deviceName;
                        
                        // إرسال تأكيد النجاح
                        ws.send(JSON.stringify({
                            type: 'auth_success',
                            message: 'تم التوصيل بنجاح'
                        }));
                        
                        console.log(`✅ تم ربط الشاشة: ${data.deviceName}`);
                    } else {
                        // إرسال رسالة فشل
                        ws.send(JSON.stringify({
                            type: 'auth_failed',
                            message: 'رمز الأمان غير صحيح'
                        }));
                        
                        console.log(`❌ فشل في المصادقة: رمز خاطئ ${data.token}`);
                    }
                    break;

                case 'display_content':
                    // إرسال محتوى للعرض
                    broadcastToDisplays(data);
                    console.log(`📺 تم إرسال محتوى للشاشات: ${data.fileType}`);
                    break;

                case 'ping':
                    // فحص الاتصال
                    ws.send(JSON.stringify({ type: 'pong' }));
                    break;
            }
        } catch (error) {
            console.error('❌ خطأ في معالجة الرسالة:', error);
        }
    });

    // معالجة قطع الاتصال
    ws.on('close', () => {
        connectedDisplays.delete(ws);
        console.log(`🔌 انقطع الاتصال مع: ${clientIP}`);
    });

    // معالجة الأخطاء
    ws.on('error', (error) => {
        console.error(`❌ خطأ في الاتصال مع ${clientIP}:`, error);
    });
});

// دوال مساعدة
function generateSecurityCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

function findDisplayByCode(code) {
    for (let [ws, display] of connectedDisplays) {
        if (display.securityCode === code) {
            return display;
        }
    }
    return null;
}

function broadcastToDisplays(content) {
    connectedDisplays.forEach((display, ws) => {
        if (display.connected && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(content));
        }
    });
}

function getLocalIP() {
    const { networkInterfaces } = require('os');
    const nets = networkInterfaces();
    
    for (const name of Object.keys(nets)) {
        for (const net of nets[name]) {
            if (net.family === 'IPv4' && !net.internal) {
                return net.address;
            }
        }
    }
    return '*************'; // fallback
}

// بدء الخادم
const PORT = 8080;
server.listen(PORT, () => {
    const localIP = getLocalIP();
    console.log('🚀 خادم التلفزيونات يعمل على:');
    console.log(`   📡 HTTP: http://${localIP}:${PORT}`);
    console.log(`   🔌 WebSocket: ws://${localIP}:${PORT}`);
    console.log('');
    console.log('📺 لربط التلفاز:');
    console.log(`   1. افتح متصفح التلفاز`);
    console.log(`   2. اذهب إلى: http://${localIP}:${PORT}/tv-display.html`);
    console.log(`   3. انسخ رمز الأمان المعروض`);
    console.log(`   4. استخدمه في البرنامج الرئيسي`);
    console.log('');
    console.log('⚡ الخادم جاهز لاستقبال الاتصالات...');
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    server.close(() => {
        console.log('✅ تم إيقاف الخادم بنجاح');
        process.exit(0);
    });
});
