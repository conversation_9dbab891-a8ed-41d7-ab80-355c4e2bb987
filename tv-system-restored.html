<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة التلفزيونات المكتبية</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f9fafb; direction: rtl; }
        
        .header { background-color: #E6E6E6; border-bottom: 2px solid #d1d5db; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
        .header-container { display: flex; justify-content: space-between; align-items: center; height: 64px; padding: 0 16px; }
        .header-left { display: flex; align-items: center; gap: 16px; }
        .three-dots { background: none; border: none; padding: 8px; border-radius: 8px; cursor: pointer; }
        .logo { width: 40px; height: 40px; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 12px; display: flex; align-items: center; justify-content: center; cursor: pointer; }
        .app-name { font-size: 18px; font-weight: bold; color: #374151; }
        .home-section { display: flex; align-items: center; gap: 8px; color: #4b5563; }
        .notification-bell { position: relative; background: none; border: none; padding: 8px; border-radius: 8px; cursor: pointer; }
        .notification-bell.has-notification { color: #ef4444; }
        .notification-dot { position: absolute; top: 4px; right: 4px; width: 8px; height: 8px; background-color: #ef4444; border-radius: 50%; }
        .user-info { display: flex; align-items: center; gap: 8px; color: #4b5563; }
        .logout-btn { background-color: #ef4444; color: white; border: none; padding: 8px 16px; border-radius: 8px; cursor: pointer; }
        
        .main-content { max-width: 1200px; margin: 0 auto; padding: 32px 16px; }
        .cards-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }
        .home-card { background: white; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); border: 3px solid #e5e7eb; padding: 32px; text-align: center; cursor: pointer; transition: all 0.3s ease; }
        .home-card:hover { box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15); transform: translateY(-8px) scale(1.02); }
        
        .card-icon { width: 64px; height: 64px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px; color: white; font-size: 24px; }
        .card-icon.purple { background-color: #CA188F; }
        .card-icon.blue { background-color: #3b82f6; }
        .card-icon.gray { background-color: #6b7280; }
        .card-icon.green { background-color: #10b981; }
        .card-icon.red { background-color: #ef4444; }
        .card-icon.indigo { background-color: #6366f1; }
        
        .card-title { font-size: 20px; font-weight: 600; color: #111827; margin-bottom: 8px; }
        .card-description { color: #6b7280; font-size: 14px; }
        
        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); display: none; align-items: center; justify-content: center; z-index: 1000; }
        .modal-overlay.active { display: flex; }
        .modal-content { background: white; border-radius: 16px; padding: 0; max-width: 95vw; max-height: 95vh; width: 95vw; height: 85vh; overflow: hidden; }
        .modal-header { display: flex; align-items: center; justify-content: space-between; padding: 24px; border-bottom: 1px solid #e5e7eb; }
        .modal-title { font-size: 20px; font-weight: 600; color: #111827; }
        .modal-close { background: none; border: none; padding: 8px; border-radius: 8px; cursor: pointer; color: #6b7280; font-size: 24px; }
        .modal-body { padding: 24px; max-height: 75vh; overflow-y: auto; }
        
        .notification { position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 12px 20px; border-radius: 8px; z-index: 2000; transform: translateX(400px); transition: transform 0.3s ease; }
        .notification.show { transform: translateX(0); }
        
        .tabs-container { display: flex; justify-content: center; margin-bottom: 30px; }
        .tab { background: #6b7280; color: white; border: none; padding: 12px 24px; margin: 0 5px; border-radius: 8px; cursor: pointer; font-weight: 600; display: flex; align-items: center; gap: 8px; }
        .tab.active { background: #1e40af; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        
        .settings-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .settings-section { background: #f9f9f9; padding: 15px; border-radius: 6px; border: 1px solid #ddd; }
        .section-title { font-size: 16px; font-weight: 700; color: #333; margin-bottom: 15px; text-align: center; }
        .form-group { margin-bottom: 10px; }
        .form-label { display: block; margin-bottom: 5px; font-weight: 600; color: #333; font-size: 14px; }
        .form-input { width: 100%; padding: 8px 10px; border: 1px solid #ccc; border-radius: 4px; font-size: 16px; background: white; height: 38px; }
        .btn { border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; font-weight: 600; font-size: 16px; height: 38px; }
        .btn-primary { background: #1e40af; color: white; width: 100%; margin-top: 8px; }
        .btn-search { background: #3b82f6; color: white; height: 38px; display: flex; align-items: center; justify-content: center; }
        .btn-save { background: #60a5fa; color: white; font-size: 14px; }
        .btn-edit { background: #fbbf24; color: white; font-size: 14px; }
        .btn-delete { background: #ef4444; color: white; font-size: 14px; }
        .search-container { display: flex; gap: 8px; align-items: center; margin-bottom: 15px; }
        .action-buttons { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px; }
        
        .user-list { display: flex; flex-direction: column; gap: 10px; max-height: 300px; overflow-y: auto; }
        .user-item { background: white; padding: 15px; border-radius: 8px; border: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; }
        .user-info { flex: 1; }
        .user-name { font-weight: 600; color: #111827; margin-bottom: 4px; }
        .user-details { font-size: 14px; color: #6b7280; }
        .user-actions { display: flex; gap: 8px; }
        .action-btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; }
        
        .password-requests { display: flex; flex-direction: column; gap: 10px; }
        .request-item { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 8px; display: flex; justify-content: space-between; align-items: center; }
        .request-item.processed { background: #f9fafb; border-color: #e5e7eb; }
        .request-info { flex: 1; }
        .request-user { font-weight: 600; color: #111827; margin-bottom: 4px; }
        .request-date { font-size: 14px; color: #6b7280; }
        .request-actions { display: flex; gap: 8px; }
        
        .permission-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .permission-section { background: #f9fafb; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb; }
        .permission-title { font-size: 16px; font-weight: 600; color: #111827; margin-bottom: 15px; text-align: center; }
        .user-select { width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 6px; margin-bottom: 15px; }
        .permission-list { display: flex; flex-direction: column; gap: 8px; }
        .permission-item { display: flex; align-items: center; gap: 8px; }
        .permission-checkbox { margin: 0; }
        .permission-label { font-size: 14px; color: #374151; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="three-dots" onclick="showNotification('قائمة البروفايل والإعدادات')">⋮</button>
                <div class="logo" onclick="showNotification('TV-Office Management System v1.0')">📺</div>
                <div class="app-name">TV-Office</div>
                <div class="home-section">
                    <span>🏠</span>
                    <span>Home</span>
                </div>
                <button class="notification-bell has-notification" onclick="toggleNotification()" id="notificationBell">
                    🔔
                    <span class="notification-dot" id="notificationDot"></span>
                </button>
                <div class="user-info">
                    <span>👤</span>
                    <span>حسين نهاد</span>
                </div>
            </div>
            <button class="logout-btn" onclick="showNotification('تم تسجيل الخروج بنجاح')">
                🚪 تسجيل الخروج
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="cards-grid">
            <div class="home-card" onclick="showModal('clipboard')">
                <div class="card-icon purple">📋</div>
                <h3 class="card-title">الحافظة</h3>
                <p class="card-description">الملفات المرسلة والمستلمة</p>
            </div>

            <div class="home-card" onclick="showModal('settings')">
                <div class="card-icon gray">⚙️</div>
                <h3 class="card-title">الإعدادات</h3>
                <p class="card-description">إدارة المستخدمين والصلاحيات</p>
            </div>

            <div class="home-card" onclick="showModal('search')">
                <div class="card-icon green">🔍</div>
                <h3 class="card-title">البحث</h3>
                <p class="card-description">البحث في الملفات المنشورة</p>
            </div>

            <div class="home-card" onclick="showModal('publish')">
                <div class="card-icon blue">📤</div>
                <h3 class="card-title">النشر</h3>
                <p class="card-description">نشر ملف جديد</p>
            </div>

            <div class="home-card" onclick="showModal('deleted')">
                <div class="card-icon red">🗑️</div>
                <h3 class="card-title">المحذوفات</h3>
                <p class="card-description">الملفات المحذوفة</p>
            </div>

            <div class="home-card" onclick="showModal('displays')">
                <div class="card-icon indigo">📺</div>
                <h3 class="card-title">شاشات العرض</h3>
                <p class="card-description">إدارة شاشات التلفزيون</p>
            </div>
        </div>
    </main>

    <!-- Modal -->
    <div class="modal-overlay" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">عنوان النافذة</div>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Content will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification"></div>

    <script>
        let hasNotification = true;
        
        // Sample data
        const userDatabase = [
            { id: 1, name: 'أحمد محمد علي', jobTitle: 'مدير التشغيل', department: 'العمليات', status: 'نشط' },
            { id: 2, name: 'فاطمة أحمد سالم', jobTitle: 'مدير الموارد البشرية', department: 'الموارد البشرية', status: 'نشط' },
            { id: 3, name: 'محمد سعد الدين', jobTitle: 'مدير المالية', department: 'المالية', status: 'نشط' },
            { id: 4, name: 'علي حسن محمد', jobTitle: 'مسؤول الاستعلامات', department: 'خدمة العملاء', status: 'غير نشط' }
        ];

        const passwordRequests = [
            { id: 1, user: 'أحمد محمد علي', date: '2024-01-15 10:30', status: 'جديد' },
            { id: 2, user: 'فاطمة أحمد سالم', date: '2024-01-14 14:20', status: 'معالج' }
        ];

        function showModal(type) {
            const modal = document.getElementById('modal');
            const modalTitle = document.getElementById('modalTitle');
            const modalBody = document.getElementById('modalBody');

            let content = '';
            let title = '';

            switch(type) {
                case 'clipboard':
                    title = 'الحافظة';
                    content = generateClipboardInterface();
                    break;
                case 'settings':
                    title = 'إدارة المستخدمين والصلاحيات';
                    content = generateSettingsInterface();
                    break;
                case 'search':
                    title = 'البحث في الملفات';
                    content = generateSearchInterface();
                    break;
                case 'publish':
                    title = 'نشر ملف جديد';
                    content = generatePublishInterface();
                    break;
                case 'deleted':
                    title = 'الملفات المحذوفة';
                    content = generateDeletedInterface();
                    break;
                case 'displays':
                    title = 'إدارة شاشات العرض';
                    content = generateDisplaysInterface();
                    break;
            }

            modalTitle.textContent = title;
            modalBody.innerHTML = content;
            modal.classList.add('active');
        }

        function closeModal() {
            document.getElementById('modal').classList.remove('active');
        }

        function generateClipboardInterface() {
            return `
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <h3>الحافظة</h3>
                    <p>قريباً - واجهة الحافظة</p>
                </div>
            `;
        }

        function generateSearchInterface() {
            return `
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <h3>البحث</h3>
                    <p>قريباً - واجهة البحث</p>
                </div>
            `;
        }

        function generatePublishInterface() {
            return `
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <h3>النشر</h3>
                    <p>قريباً - واجهة النشر</p>
                </div>
            `;
        }

        function generateDeletedInterface() {
            return `
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <h3>المحذوفات</h3>
                    <p>قريباً - واجهة المحذوفات</p>
                </div>
            `;
        }

        function generateDisplaysInterface() {
            return `
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <h3>شاشات العرض</h3>
                    <p>قريباً - واجهة شاشات العرض</p>
                </div>
            `;
        }

        function generateSettingsInterface() {
            return `
                <!-- Tabs -->
                <div class="tabs-container">
                    <button class="tab active" onclick="showTab('addUsers')" id="addUsersTab">
                        👥 إضافة معلومات المستخدمين
                    </button>
                    <button class="tab" onclick="showTab('permissions')" id="permissionsTab">
                        🛡️ الصلاحيات
                    </button>
                    <button class="tab" onclick="showTab('passwordReset')" id="passwordResetTab">
                        🔔 إعادة تعيين كلمة المرور
                        <span style="background: #ef4444; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px;">2</span>
                    </button>
                </div>

                <!-- Add Users Tab -->
                <div id="addUsersContent" class="tab-content active">
                    <div class="settings-grid">
                        <!-- Input Section -->
                        <div class="settings-section">
                            <h3 class="section-title">إدخال معلومات</h3>

                            <div class="form-group">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" id="displayUserName" class="form-input" placeholder="أدخل اسم المستخدم">
                            </div>

                            <div class="form-group">
                                <label class="form-label">المسمى الوظيفي</label>
                                <input type="text" id="displayJobTitle" class="form-input" placeholder="أدخل المسمى الوظيفي">
                            </div>

                            <div class="form-group">
                                <label class="form-label">القسم</label>
                                <input type="text" id="displayDepartment" class="form-input" placeholder="أدخل اسم القسم">
                            </div>

                            <button onclick="saveUserData()" class="btn btn-primary">💾 حفظ</button>
                        </div>

                        <!-- Search and Actions Section -->
                        <div class="settings-section">
                            <h3 class="section-title">البحث والعرض</h3>

                            <div class="form-group">
                                <label class="form-label">البحث عن مستخدم</label>
                                <div class="search-container">
                                    <input type="text" id="searchUserInput" class="form-input" placeholder="اكتب اسم المستخدم للبحث..." style="flex: 1; margin-bottom: 0;">
                                    <button onclick="searchUser()" class="btn btn-search">🔍</button>
                                </div>
                            </div>

                            <div class="action-buttons">
                                <button onclick="saveUserData()" class="btn btn-save">💾 حفظ</button>
                                <button onclick="editUserData()" class="btn btn-edit">✏️ تعديل</button>
                                <button onclick="deleteUserData()" class="btn btn-delete">🗑️ حذف</button>
                            </div>
                        </div>
                    </div>

                    <!-- Users List -->
                    <div style="margin-top: 20px;">
                        <h3 style="margin-bottom: 15px; color: #111827;">المستخدمون المسجلون</h3>
                        <div class="user-list" id="usersList">
                            ${generateUsersList()}
                        </div>
                    </div>
                </div>

                <!-- Permissions Tab -->
                <div id="permissionsContent" class="tab-content">
                    <div class="permission-grid">
                        <div class="permission-section">
                            <h3 class="permission-title">اختيار المستخدم</h3>
                            <select class="user-select" id="permissionUser">
                                <option value="">اختر المستخدم</option>
                                ${userDatabase.map(user => `<option value="${user.id}">${user.name}</option>`).join('')}
                            </select>
                            <button class="btn btn-primary" onclick="loadUserPermissions()">تحميل الصلاحيات</button>
                        </div>

                        <div class="permission-section">
                            <h3 class="permission-title">صلاحيات البرنامج</h3>
                            <div class="permission-list">
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_clipboard" class="permission-checkbox">
                                    <label for="perm_clipboard" class="permission-label">الحافظة</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_settings" class="permission-checkbox">
                                    <label for="perm_settings" class="permission-label">الإعدادات</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_search" class="permission-checkbox">
                                    <label for="perm_search" class="permission-label">البحث</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_publish" class="permission-checkbox">
                                    <label for="perm_publish" class="permission-label">النشر</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_deleted" class="permission-checkbox">
                                    <label for="perm_deleted" class="permission-label">المحذوفات</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_displays" class="permission-checkbox">
                                    <label for="perm_displays" class="permission-label">شاشات العرض</label>
                                </div>
                            </div>
                            <button class="btn btn-primary" onclick="savePermissions()" style="margin-top: 15px;">💾 حفظ الصلاحيات</button>
                        </div>
                    </div>
                </div>

                <!-- Password Reset Tab -->
                <div id="passwordResetContent" class="tab-content">
                    <h3 style="margin-bottom: 20px; color: #111827; text-align: center;">🔒 طلبات إعادة تعيين كلمة المرور</h3>
                    <div class="password-requests">
                        ${generatePasswordRequests()}
                    </div>
                </div>
            `;
        }

        function generateUsersList() {
            return userDatabase.map(user => `
                <div class="user-item">
                    <div class="user-info">
                        <div class="user-name">${user.name}</div>
                        <div class="user-details">${user.jobTitle} • ${user.department} • ${user.status}</div>
                    </div>
                    <div class="user-actions">
                        <button class="action-btn btn-edit" onclick="editUser(${user.id})">✏️ تعديل</button>
                        <button class="action-btn btn-delete" onclick="deleteUser(${user.id})">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        function generatePasswordRequests() {
            return passwordRequests.map(request => `
                <div class="request-item ${request.status === 'معالج' ? 'processed' : ''}">
                    <div class="request-info">
                        <div class="request-user">🔒 ${request.user}</div>
                        <div class="request-date">📅 ${request.date}</div>
                    </div>
                    <div class="request-actions">
                        ${request.status === 'جديد' ? `
                            <button class="action-btn btn-save" onclick="approvePasswordReset(${request.id})">✅ موافقة</button>
                            <button class="action-btn btn-delete" onclick="rejectPasswordReset(${request.id})">❌ رفض</button>
                        ` : `
                            <span style="color: #10b981; font-weight: 600;">✅ تم المعالجة</span>
                        `}
                    </div>
                </div>
            `).join('');
        }

        // Tab Functions
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.add('active');
            document.getElementById(tabName + 'Content').classList.add('active');
        }

        // User Management Functions
        function saveUserData() {
            const userName = document.getElementById('displayUserName').value.trim();
            const jobTitle = document.getElementById('displayJobTitle').value.trim();
            const department = document.getElementById('displayDepartment').value.trim();

            if (!userName || !jobTitle || !department) {
                showNotification('⚠️ يرجى ملء جميع الحقول');
                return;
            }

            // Check if user already exists
            const existingUser = userDatabase.find(user => user.name === userName);
            if (existingUser) {
                showNotification('⚠️ المستخدم موجود بالفعل');
                return;
            }

            // Add new user
            const newUser = {
                id: userDatabase.length + 1,
                name: userName,
                jobTitle: jobTitle,
                department: department,
                status: 'نشط'
            };

            userDatabase.push(newUser);
            showNotification('✅ تم حفظ بيانات: ' + userName);
            clearDisplayFields();
            updateUsersList();
        }

        function editUserData() {
            const userName = document.getElementById('displayUserName').value.trim();
            const jobTitle = document.getElementById('displayJobTitle').value.trim();
            const department = document.getElementById('displayDepartment').value.trim();

            if (!userName) {
                showNotification('⚠️ يرجى إدخال اسم المستخدم للتعديل');
                return;
            }

            const userIndex = userDatabase.findIndex(user => user.name === userName);
            if (userIndex !== -1) {
                userDatabase[userIndex].jobTitle = jobTitle;
                userDatabase[userIndex].department = department;
                showNotification('✅ تم تعديل بيانات: ' + userName);
                updateUsersList();
            } else {
                showNotification('❌ لم يتم العثور على المستخدم: ' + userName);
            }
        }

        function deleteUserData() {
            const userName = document.getElementById('displayUserName').value.trim();

            if (!userName) {
                showNotification('⚠️ يرجى إدخال اسم المستخدم للحذف');
                return;
            }

            const userIndex = userDatabase.findIndex(user => user.name === userName);
            if (userIndex !== -1) {
                userDatabase.splice(userIndex, 1);
                showNotification('✅ تم حذف المستخدم: ' + userName);
                clearDisplayFields();
                updateUsersList();
            } else {
                showNotification('❌ لم يتم العثور على المستخدم: ' + userName);
            }
        }

        function searchUser() {
            const searchInput = document.getElementById('searchUserInput');
            const searchName = searchInput.value.trim();

            if (!searchName) {
                showNotification('⚠️ يرجى إدخال اسم للبحث');
                return;
            }

            const foundUser = userDatabase.find(user => user.name.includes(searchName));
            if (foundUser) {
                document.getElementById('displayUserName').value = foundUser.name;
                document.getElementById('displayJobTitle').value = foundUser.jobTitle;
                document.getElementById('displayDepartment').value = foundUser.department;
                showNotification('✅ تم العثور على: ' + foundUser.name);
            } else {
                showNotification('❌ لم يتم العثور على: ' + searchName);
            }
        }

        function clearDisplayFields() {
            document.getElementById('displayUserName').value = '';
            document.getElementById('displayJobTitle').value = '';
            document.getElementById('displayDepartment').value = '';
            document.getElementById('searchUserInput').value = '';
        }

        function updateUsersList() {
            const usersList = document.getElementById('usersList');
            if (usersList) {
                usersList.innerHTML = generateUsersList();
            }
        }

        function editUser(userId) {
            const user = userDatabase.find(u => u.id === userId);
            if (user) {
                document.getElementById('displayUserName').value = user.name;
                document.getElementById('displayJobTitle').value = user.jobTitle;
                document.getElementById('displayDepartment').value = user.department;
                showNotification('تم تحميل بيانات: ' + user.name);
            }
        }

        function deleteUser(userId) {
            const userIndex = userDatabase.findIndex(u => u.id === userId);
            if (userIndex !== -1) {
                const userName = userDatabase[userIndex].name;
                userDatabase.splice(userIndex, 1);
                showNotification('✅ تم حذف المستخدم: ' + userName);
                updateUsersList();
            }
        }

        // Permission Functions
        function loadUserPermissions() {
            const userId = document.getElementById('permissionUser').value;
            if (!userId) {
                showNotification('⚠️ يرجى اختيار مستخدم');
                return;
            }
            showNotification('تم تحميل صلاحيات المستخدم');
        }

        function savePermissions() {
            const userId = document.getElementById('permissionUser').value;
            if (!userId) {
                showNotification('⚠️ يرجى اختيار مستخدم');
                return;
            }
            showNotification('✅ تم حفظ الصلاحيات');
        }

        // Password Reset Functions
        function approvePasswordReset(requestId) {
            const request = passwordRequests.find(r => r.id === requestId);
            if (request) {
                request.status = 'معالج';
                showNotification('✅ تم الموافقة على طلب: ' + request.user);
                // Refresh the password reset tab content
                document.getElementById('passwordResetContent').innerHTML = `
                    <h3 style="margin-bottom: 20px; color: #111827; text-align: center;">🔒 طلبات إعادة تعيين كلمة المرور</h3>
                    <div class="password-requests">
                        ${generatePasswordRequests()}
                    </div>
                `;
            }
        }

        function rejectPasswordReset(requestId) {
            const requestIndex = passwordRequests.findIndex(r => r.id === requestId);
            if (requestIndex !== -1) {
                const userName = passwordRequests[requestIndex].user;
                passwordRequests.splice(requestIndex, 1);
                showNotification('❌ تم رفض طلب: ' + userName);
                // Refresh the password reset tab content
                document.getElementById('passwordResetContent').innerHTML = `
                    <h3 style="margin-bottom: 20px; color: #111827; text-align: center;">🔒 طلبات إعادة تعيين كلمة المرور</h3>
                    <div class="password-requests">
                        ${generatePasswordRequests()}
                    </div>
                `;
            }
        }

        function toggleNotification() {
            hasNotification = !hasNotification;
            const bell = document.getElementById('notificationBell');
            const dot = document.getElementById('notificationDot');

            if (hasNotification) {
                bell.classList.add('has-notification');
                dot.style.display = 'block';
            } else {
                bell.classList.remove('has-notification');
                dot.style.display = 'none';
            }

            showNotification(hasNotification ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات');
        }

        // Notification Function
        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
