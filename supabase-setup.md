# إعداد قاعدة البيانات على Supabase

## الخطوات المطلوبة:

### 1. إنشاء حساب Supabase
1. اذهب إلى [supabase.com](https://supabase.com)
2. ان<PERSON><PERSON> على "Start your project"
3. سجل دخول باستخدام GitHub أو Google
4. انقر على "New Project"

### 2. إعداد المشروع
1. **اسم المشروع**: `tv-office-system`
2. **كلمة مرور قاعدة البيانات**: اختر كلمة مرور قوية واحفظها
3. **المنطقة**: اختر أقرب منطقة لك
4. **الخطة**: Free Plan (مجاني)
5. انقر على "Create new project"

### 3. تشغيل SQL Scripts
بعد إنشاء المشروع:

1. اذهب إلى **SQL Editor** في لوحة التحكم
2. انقر على **"New query"**
3. انسخ محتوى ملف `database/supabase-schema.sql` والصقه
4. انقر على **"Run"** لتنفيذ الأمر
5. كرر العملية مع ملف `database/supabase-seed.sql`

### 4. الحصول على بيانات الاتصال
من لوحة التحكم، اذهب إلى **Settings > API**:

1. **Project URL**: انسخ الرابط (مثل: `https://xxxxx.supabase.co`)
2. **anon public key**: انسخ المفتاح العام
3. **service_role key**: انسخ مفتاح الخدمة (سري)

### 5. إعداد Storage للملفات
1. اذهب إلى **Storage** في لوحة التحكم
2. انقر على **"Create bucket"**
3. **اسم الـ Bucket**: `files`
4. **Public bucket**: نعم (لعرض الملفات)
5. انقر على **"Create bucket"**

### 6. إعداد سياسات Storage
في **Storage > files > Policies**:

```sql
-- سياسة للقراءة (عرض الملفات)
CREATE POLICY "Public Access" ON storage.objects
FOR SELECT USING (bucket_id = 'files');

-- سياسة للكتابة (رفع الملفات)
CREATE POLICY "Authenticated users can upload" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'files' AND auth.role() = 'authenticated');

-- سياسة للحذف
CREATE POLICY "Users can delete own files" ON storage.objects
FOR DELETE USING (bucket_id = 'files' AND auth.role() = 'authenticated');
```

### 7. إعداد Authentication
في **Authentication > Settings**:

1. **Site URL**: `https://skayhr.github.io`
2. **Redirect URLs**: أضف:
   - `https://skayhr.github.io/tv-office/demo.html`
   - `http://localhost:3000` (للتطوير)

### 8. تفعيل Row Level Security
تأكد من أن RLS مُفعل لجميع الجداول في **Database > Tables**.

## بيانات تسجيل الدخول الافتراضية:

بعد تشغيل البيانات الأساسية، ستكون بيانات تسجيل الدخول:

- **المدير الرئيسي**: `admin` / `admin123`
- **مدير التشغيل**: `operations_mgr` / `123456`
- **مدير الموارد البشرية**: `dept_mgr_hr` / `123456`
- **مدير المالية**: `dept_mgr_finance` / `123456`
- **مسؤول الاستعلامات**: `display_op` / `123456`

## ملاحظات مهمة:

1. **احفظ بيانات الاتصال** في مكان آمن
2. **لا تشارك service_role key** مع أحد
3. **استخدم anon key** في Frontend فقط
4. **كلمات المرور مُشفرة** باستخدام bcrypt

## الخطوة التالية:
بعد إكمال هذه الخطوات، أرسل لي:
1. Project URL
2. anon public key

وسأكمل إعداد Backend و Frontend للاتصال بقاعدة البيانات.
