# نظام إدارة التلفزيونات المكتبية - TV Office Management System

## 🎯 نظرة عامة

نظام متكامل لإدارة ونشر المحتوى على شاشات العرض في المكاتب والمؤسسات، مع إدارة المستخدمين والصلاحيات.

## 🚀 الوصول للنظام

- **الرابط المباشر**: [https://skayhr.github.io/tv-office/](https://skayhr.github.io/tv-office/)
- **النظام الكامل**: [https://skayhr.github.io/tv-office/demo.html](https://skayhr.github.io/tv-office/demo.html)

## 🔐 بيانات تسجيل الدخول

### حسابات تجريبية:

| نوع المستخدم | اسم المستخدم | كلمة المرور | الصلاحيات |
|--------------|---------------|-------------|-----------|
| المدير الرئيسي | `admin` | `admin123` | جميع الصلاحيات |
| مدير التشغيل | `operations_mgr` | `123456` | نشر الملفات |
| مدير الموارد البشرية | `dept_mgr_hr` | `123456` | استلام الملفات |
| مدير المالية | `dept_mgr_finance` | `123456` | استلام الملفات |
| مسؤول الاستعلامات | `display_op` | `123456` | إدارة الشاشات |

## ✨ المميزات

### 🏠 الصفحة الرئيسية (A)
- **المدير الرئيسي فقط**
- عرض شامل لجميع الأنشطة
- إحصائيات النظام
- إدارة المستخدمين والإعدادات

### 📤 مدير التشغيل (B)
- نشر الملفات للنظام
- إدارة الحافظة
- البحث في الملفات
- عرض المحذوفات

### 📥 مدير القسم (C)
- استلام الملفات تلقائياً
- حفظ الملفات في الحافظة
- تنبيهات صوتية
- البحث والمحذوفات

### 📺 مسؤول الاستعلامات (D)
- ربط شاشات التلفزيون
- عرض الملفات على الشاشات
- إدارة حالة الاتصال
- مراقبة الشاشات

## 🛠️ التقنيات المستخدمة

### Frontend
- **React** مع TypeScript
- **Tailwind CSS** للتصميم
- **Socket.io** للتحديثات المباشرة
- **React Hook Form** للنماذج
- **React Hot Toast** للإشعارات

### Backend
- **Node.js** مع Express
- **Supabase** (PostgreSQL + Storage)
- **Socket.io** للاتصال المباشر
- **JWT** للمصادقة
- **Multer** لرفع الملفات

### النشر
- **GitHub Pages** للـ Frontend
- **Vercel** للـ Backend
- **Supabase** لقاعدة البيانات

## 📱 الاستجابة

النظام مُحسن للعمل على:
- 💻 أجهزة الكمبيوتر المكتبية
- 📱 الأجهزة اللوحية
- 📱 الهواتف الذكية

## 🔒 الأمان

- **مصادقة JWT** لجميع الطلبات
- **Row Level Security** في قاعدة البيانات
- **تشفير كلمات المرور** باستخدام bcrypt
- **CORS** محدود للنطاقات المسموحة
- **تحقق من صحة البيانات** في جميع المراحل

## 📊 الوظائف الرئيسية

### إدارة المستخدمين
- ✅ إضافة مستخدمين جدد
- ✅ تحديد الصلاحيات حسب المسمى الوظيفي
- ✅ تعديل بيانات المستخدمين
- ✅ حذف المستخدمين
- ✅ إعادة تعيين كلمات المرور

### إدارة الملفات
- ✅ رفع ملفات PDF
- ✅ نشر الملفات للمستخدمين
- ✅ البحث في الملفات
- ✅ تصنيف الملفات
- ✅ سلة المهملات
- ✅ استعادة الملفات

### شاشات العرض
- ✅ ربط شاشات التلفزيون
- ✅ عرض الملفات تلقائياً
- ✅ مراقبة حالة الاتصال
- ✅ إدارة متعددة للشاشات

### التحديثات المباشرة
- ✅ إشعارات فورية للملفات الجديدة
- ✅ تحديث حالة الشاشات
- ✅ تنبيهات صوتية
- ✅ تزامن البيانات

## 🎮 كيفية الاستخدام

### 1. تسجيل الدخول
1. افتح [النظام](https://skayhr.github.io/tv-office/demo.html)
2. أدخل اسم المستخدم وكلمة المرور
3. انقر على "تسجيل الدخول"

### 2. نشر ملف (مدير التشغيل)
1. انقر على "نشر ملف جديد"
2. اختر ملف PDF
3. أدخل اسم الملف
4. انقر على "نشر"

### 3. ربط شاشة عرض (مسؤول الاستعلامات)
1. أدخل معرف الشاشة
2. انقر على "اتصال"
3. ستظهر الملفات تلقائياً

### 4. إدارة المستخدمين (المدير الرئيسي)
1. انقر على "الإعدادات"
2. انقر على "إضافة مستخدم جديد"
3. املأ البيانات المطلوبة
4. حدد نوع المستخدم
5. انقر على "إضافة"

## 🔄 التحديثات

النظام يتلقى تحديثات تلقائية عند:
- إضافة ملفات جديدة
- تغيير حالة الشاشات
- إضافة مستخدمين جدد
- تحديث البيانات

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: [<EMAIL>](mailto:<EMAIL>)
- 🐛 تقارير الأخطاء: [GitHub Issues](https://github.com/skayhr/tv-office/issues)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

تم تطوير هذا النظام باستخدام أحدث التقنيات وأفضل الممارسات في تطوير الويب.

---

**صُنع بحب في المملكة العربية السعودية 🇸🇦**
