const express = require('express');
const { body, validationResult } = require('express-validator');
const { supabase } = require('../config/supabase');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();

// جلب جميع الشاشات
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { data: displays, error } = await supabase
      .from('displays')
      .select(`
        *,
        operator:users!inner(id, full_name, username)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json(displays);
  } catch (error) {
    console.error('Get displays error:', error);
    res.status(500).json({ message: 'خطأ في جلب الشاشات' });
  }
});

// جلب شاشة محددة
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: display, error } = await supabase
      .from('displays')
      .select(`
        *,
        operator:users!inner(id, full_name, username)
      `)
      .eq('id', id)
      .single();

    if (error || !display) {
      return res.status(404).json({ message: 'الشاشة غير موجودة' });
    }

    res.json(display);
  } catch (error) {
    console.error('Get display error:', error);
    res.status(500).json({ message: 'خطأ في جلب بيانات الشاشة' });
  }
});

// إضافة شاشة جديدة
router.post('/', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN', 'DISPLAY_OP'),
  body('screenId').notEmpty().withMessage('معرف الشاشة مطلوب'),
  body('screenName').notEmpty().withMessage('اسم الشاشة مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { screenId, screenName } = req.body;

    // التحقق من عدم وجود معرف الشاشة مسبقاً
    const { data: existingDisplay } = await supabase
      .from('displays')
      .select('id')
      .eq('screen_id', screenId)
      .single();

    if (existingDisplay) {
      return res.status(400).json({ message: 'معرف الشاشة موجود مسبقاً' });
    }

    // تحديد مشغل الشاشة
    let operatorId = req.user.id;
    
    // إذا كان المستخدم مدير رئيسي، يمكنه تحديد مشغل آخر
    if (req.user.user_type === 'MAIN_ADMIN' && req.body.operatorId) {
      // التحقق من أن المشغل المحدد من نوع DISPLAY_OP
      const { data: operator, error: operatorError } = await supabase
        .from('users')
        .select('id, user_type')
        .eq('id', req.body.operatorId)
        .eq('user_type', 'DISPLAY_OP')
        .single();

      if (operator && !operatorError) {
        operatorId = req.body.operatorId;
      }
    }

    // إضافة الشاشة الجديدة
    const { data: newDisplay, error } = await supabase
      .from('displays')
      .insert({
        screen_id: screenId,
        screen_name: screenName,
        operator_id: operatorId,
        is_connected: false
      })
      .select(`
        *,
        operator:users!inner(id, full_name, username)
      `)
      .single();

    if (error) {
      throw error;
    }

    res.status(201).json({
      message: 'تم إضافة الشاشة بنجاح',
      display: newDisplay
    });

  } catch (error) {
    console.error('Create display error:', error);
    res.status(500).json({ message: 'خطأ في إضافة الشاشة' });
  }
});

// تحديث بيانات شاشة
router.put('/:id', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN', 'DISPLAY_OP'),
  body('screenName').optional().notEmpty().withMessage('اسم الشاشة لا يمكن أن يكون فارغاً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { id } = req.params;
    const { screenName } = req.body;

    // التحقق من وجود الشاشة والصلاحية
    const { data: existingDisplay, error: fetchError } = await supabase
      .from('displays')
      .select('id, operator_id')
      .eq('id', id)
      .single();

    if (fetchError || !existingDisplay) {
      return res.status(404).json({ message: 'الشاشة غير موجودة' });
    }

    // التحقق من الصلاحية
    if (req.user.user_type !== 'MAIN_ADMIN' && existingDisplay.operator_id !== req.user.id) {
      return res.status(403).json({ message: 'ليس لديك صلاحية لتعديل هذه الشاشة' });
    }

    // تحديث البيانات
    const updateData = {};
    if (screenName !== undefined) updateData.screen_name = screenName;

    const { data: updatedDisplay, error } = await supabase
      .from('displays')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        operator:users!inner(id, full_name, username)
      `)
      .single();

    if (error) {
      throw error;
    }

    res.json({
      message: 'تم تحديث بيانات الشاشة بنجاح',
      display: updatedDisplay
    });

  } catch (error) {
    console.error('Update display error:', error);
    res.status(500).json({ message: 'خطأ في تحديث بيانات الشاشة' });
  }
});

// تحديث حالة الاتصال
router.patch('/:id/connection', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN', 'DISPLAY_OP'),
  body('isConnected').isBoolean().withMessage('حالة الاتصال يجب أن تكون true أو false')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { id } = req.params;
    const { isConnected } = req.body;

    // التحقق من وجود الشاشة والصلاحية
    const { data: existingDisplay, error: fetchError } = await supabase
      .from('displays')
      .select('id, operator_id')
      .eq('id', id)
      .single();

    if (fetchError || !existingDisplay) {
      return res.status(404).json({ message: 'الشاشة غير موجودة' });
    }

    // التحقق من الصلاحية
    if (req.user.user_type !== 'MAIN_ADMIN' && existingDisplay.operator_id !== req.user.id) {
      return res.status(403).json({ message: 'ليس لديك صلاحية لتعديل هذه الشاشة' });
    }

    // تحديث حالة الاتصال
    const { data: updatedDisplay, error } = await supabase
      .from('displays')
      .update({ is_connected: isConnected })
      .eq('id', id)
      .select(`
        *,
        operator:users!inner(id, full_name, username)
      `)
      .single();

    if (error) {
      throw error;
    }

    res.json({
      message: `تم ${isConnected ? 'الاتصال بالشاشة' : 'قطع الاتصال عن الشاشة'}`,
      display: updatedDisplay
    });

  } catch (error) {
    console.error('Update display connection error:', error);
    res.status(500).json({ message: 'خطأ في تحديث حالة الاتصال' });
  }
});

// حذف شاشة
router.delete('/:id', [
  authenticateToken,
  authorizeRoles('MAIN_ADMIN', 'DISPLAY_OP')
], async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من وجود الشاشة والصلاحية
    const { data: existingDisplay, error: fetchError } = await supabase
      .from('displays')
      .select('id, screen_name, operator_id')
      .eq('id', id)
      .single();

    if (fetchError || !existingDisplay) {
      return res.status(404).json({ message: 'الشاشة غير موجودة' });
    }

    // التحقق من الصلاحية
    if (req.user.user_type !== 'MAIN_ADMIN' && existingDisplay.operator_id !== req.user.id) {
      return res.status(403).json({ message: 'ليس لديك صلاحية لحذف هذه الشاشة' });
    }

    // حذف الشاشة
    const { error } = await supabase
      .from('displays')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    res.json({ message: 'تم حذف الشاشة بنجاح' });

  } catch (error) {
    console.error('Delete display error:', error);
    res.status(500).json({ message: 'خطأ في حذف الشاشة' });
  }
});

// جلب الشاشات الخاصة بمشغل معين
router.get('/operator/:operatorId', authenticateToken, async (req, res) => {
  try {
    const { operatorId } = req.params;

    // التحقق من الصلاحية
    if (req.user.user_type !== 'MAIN_ADMIN' && req.user.id !== operatorId) {
      return res.status(403).json({ message: 'ليس لديك صلاحية لعرض هذه البيانات' });
    }

    const { data: displays, error } = await supabase
      .from('displays')
      .select(`
        *,
        operator:users!inner(id, full_name, username)
      `)
      .eq('operator_id', operatorId)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json(displays);
  } catch (error) {
    console.error('Get operator displays error:', error);
    res.status(500).json({ message: 'خطأ في جلب شاشات المشغل' });
  }
});

module.exports = router;
