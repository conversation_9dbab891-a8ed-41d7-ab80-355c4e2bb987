<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TV-Office - اختبار النشر</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            margin: 0 auto;
        }
        h1 { font-size: 2.5em; margin-bottom: 20px; }
        p { font-size: 1.2em; margin-bottom: 30px; }
        .btn {
            background: white;
            color: #667eea;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { transform: scale(1.05); }
        .status { 
            background: #10b981; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 20px 0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 TV-Office</h1>
        <div class="status">✅ النشر يعمل بنجاح!</div>
        <p>نظام إدارة التلفزيونات المكتبية</p>
        <p>إذا كنت ترى هذه الصفحة، فإن النشر يعمل بشكل صحيح.</p>
        <a href="demo.html" class="btn">🚀 دخول النظام</a>
        <br><br>
        <small>تاريخ النشر: <span id="date"></span></small>
    </div>

    <script>
        document.getElementById('date').textContent = new Date().toLocaleString('ar-SA');
    </script>
</body>
</html>
