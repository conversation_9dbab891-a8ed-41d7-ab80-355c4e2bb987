<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفرع الرئيسي A - الإدارة العامة</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: bold;
            color: #4a5568;
        }

        .admin-badge {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }

        .main-container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary { background: #3182ce; color: white; }
        .btn-success { background: #38a169; color: white; }
        .btn-danger { background: #e53e3e; color: white; }
        .btn-warning { background: #d69e2e; color: white; }

        .btn:hover { transform: translateY(-2px); opacity: 0.9; }

        .branch-card {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .branch-card:hover {
            border-color: #3182ce;
            box-shadow: 0 4px 15px rgba(49, 130, 206, 0.2);
        }

        .branch-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-active { background: #c6f6d5; color: #22543d; }
        .status-restricted { background: #fed7d7; color: #742a2a; }
        .status-offline { background: #e2e8f0; color: #4a5568; }

        .permission-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: #f7fafc;
            border-radius: 6px;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal.show { display: flex; }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .content-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .content-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .activity-log {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .log-item {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
            color: #4a5568;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            👑 الفرع الرئيسي A - الإدارة العامة
        </div>
        <div class="admin-badge">
            مدير النظام
        </div>
    </div>

    <div class="main-container">
        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalBranches">3</div>
                <div class="stat-label">الفروع النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">12</div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalContent">8</div>
                <div class="stat-label">المحتوى المنشور</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeConnections">3</div>
                <div class="stat-label">الاتصالات النشطة</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- إدارة الفروع -->
            <div class="section">
                <div class="section-title">
                    🏢 إدارة الفروع
                </div>
                
                <div class="branch-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <strong>فرع B - المبيعات</strong>
                        <span class="branch-status status-active">نشط</span>
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="b_publish" checked onchange="updatePermission('B', 'publish', this.checked)">
                            <label for="b_publish">النشر</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="b_view" checked onchange="updatePermission('B', 'view', this.checked)">
                            <label for="b_view">العرض</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="b_manage" onchange="updatePermission('B', 'manage', this.checked)">
                            <label for="b_manage">الإدارة</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="b_delete" onchange="updatePermission('B', 'delete', this.checked)">
                            <label for="b_delete">الحذف</label>
                        </div>
                    </div>
                </div>

                <div class="branch-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <strong>فرع C - الجودة</strong>
                        <span class="branch-status status-active">نشط</span>
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="c_publish" checked onchange="updatePermission('C', 'publish', this.checked)">
                            <label for="c_publish">النشر</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="c_view" checked onchange="updatePermission('C', 'view', this.checked)">
                            <label for="c_view">العرض</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="c_manage" checked onchange="updatePermission('C', 'manage', this.checked)">
                            <label for="c_manage">الإدارة</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="c_delete" onchange="updatePermission('C', 'delete', this.checked)">
                            <label for="c_delete">الحذف</label>
                        </div>
                    </div>
                </div>

                <div class="branch-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <strong>فرع D - العرض</strong>
                        <span class="branch-status status-active">نشط</span>
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="d_view" checked disabled>
                            <label for="d_view">العرض (دائم)</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="d_fullscreen" checked onchange="updatePermission('D', 'fullscreen', this.checked)">
                            <label for="d_fullscreen">العرض الكامل</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إدارة المحتوى -->
            <div class="section">
                <div class="section-title">
                    📚 إدارة المحتوى
                </div>
                
                <button class="btn btn-primary" onclick="publishContent()" style="width: 100%; margin-bottom: 15px;">
                    📤 نشر محتوى جديد
                </button>
                
                <button class="btn btn-success" onclick="showContentManager()" style="width: 100%; margin-bottom: 15px;">
                    📋 إدارة المحتوى الموجود
                </button>
                
                <button class="btn btn-warning" onclick="showScheduledContent()" style="width: 100%; margin-bottom: 15px;">
                    ⏰ المحتوى المجدول
                </button>
                
                <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <div style="font-size: 14px; color: #4a5568; margin-bottom: 10px;">آخر المنشورات:</div>
                    <div id="recentContent">
                        <div style="font-size: 12px; color: #718096;">لا يوجد محتوى حديث</div>
                    </div>
                </div>
            </div>

            <!-- مراقبة النشاط -->
            <div class="section">
                <div class="section-title">
                    📊 مراقبة النشاط
                </div>
                
                <div class="activity-log" id="activityLog">
                    <div class="log-item">🟢 فرع B متصل - 14:30</div>
                    <div class="log-item">🟢 فرع C متصل - 14:29</div>
                    <div class="log-item">🟢 فرع D متصل - 14:28</div>
                    <div class="log-item">📤 تم نشر محتوى جديد - 14:25</div>
                    <div class="log-item">👤 مستخدم جديد في فرع B - 14:20</div>
                </div>
                
                <button class="btn btn-primary" onclick="refreshActivity()" style="width: 100%; margin-top: 15px;">
                    🔄 تحديث النشاط
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة إدارة المحتوى -->
    <div class="modal" id="contentModal">
        <div class="modal-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>إدارة المحتوى</h3>
                <button onclick="closeModal()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
            </div>
            <div class="content-list" id="contentList">
                <!-- سيتم إدراج المحتوى هنا -->
            </div>
        </div>
    </div>

    <script>
        // نظام إدارة الصلاحيات والمحتوى
        class AdminSystem {
            constructor() {
                this.initializeData();
                this.loadDashboard();
                this.startMonitoring();
            }

            initializeData() {
                if (!localStorage.getItem('adminSystem')) {
                    const defaultData = {
                        branches: {
                            B: { 
                                name: 'فرع B - المبيعات', 
                                status: 'active',
                                permissions: { publish: true, view: true, manage: false, delete: false },
                                users: 5,
                                lastActivity: new Date().toISOString()
                            },
                            C: { 
                                name: 'فرع C - الجودة', 
                                status: 'active',
                                permissions: { publish: true, view: true, manage: true, delete: false },
                                users: 4,
                                lastActivity: new Date().toISOString()
                            },
                            D: { 
                                name: 'فرع D - العرض', 
                                status: 'active',
                                permissions: { view: true, fullscreen: true },
                                users: 0,
                                lastActivity: new Date().toISOString()
                            }
                        },
                        content: [],
                        activityLog: [],
                        stats: {
                            totalBranches: 3,
                            totalUsers: 12,
                            totalContent: 0,
                            activeConnections: 3
                        }
                    };
                    this.saveData(defaultData);
                }
            }

            getData() {
                return JSON.parse(localStorage.getItem('adminSystem'));
            }

            saveData(data) {
                localStorage.setItem('adminSystem', JSON.stringify(data));
                // تحديث صلاحيات الفروع
                localStorage.setItem('branchPermissions', JSON.stringify(data.branches));
            }

            updatePermission(branch, permission, value) {
                const data = this.getData();
                data.branches[branch].permissions[permission] = value;
                this.saveData(data);
                
                this.addActivity(`تم ${value ? 'تفعيل' : 'إلغاء'} صلاحية ${permission} لفرع ${branch}`);
                
                alert(`تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} صلاحية ${permission} لفرع ${branch}!
                
سيتم تطبيق التغيير فوراً على الفرع.`);
            }

            publishContent() {
                const sampleContent = [
                    { title: 'تعليمات إدارية جديدة', author: 'الإدارة العامة', icon: '📋', branch: 'A' },
                    { title: 'سياسة الشركة المحدثة', author: 'الإدارة العامة', icon: '📜', branch: 'A' },
                    { title: 'إعلان هام لجميع الموظفين', author: 'الإدارة العامة', icon: '📢', branch: 'A' },
                    { title: 'دليل الإجراءات الجديد', author: 'الإدارة العامة', icon: '📖', branch: 'A' }
                ];

                const content = sampleContent[Math.floor(Math.random() * sampleContent.length)];
                content.timestamp = Date.now();
                content.date = new Date().toLocaleDateString('ar-SA');
                content.id = Date.now();

                // حفظ في النظام
                const data = this.getData();
                data.content.unshift(content);
                data.stats.totalContent = data.content.length;
                this.saveData(data);

                // نشر للفروع
                localStorage.setItem('sharedContent', JSON.stringify(content));

                this.addActivity(`تم نشر محتوى جديد: ${content.title}`);
                this.loadDashboard();

                alert(`✅ تم نشر المحتوى بنجاح!

📄 ${content.title}
👤 بواسطة: ${content.author}
🏢 من: الفرع الرئيسي A
📅 ${content.date}

سيظهر الآن على جميع الفروع المخولة.`);
            }

            addActivity(message) {
                const data = this.getData();
                const time = new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
                data.activityLog.unshift(`${message} - ${time}`);
                
                // الاحتفاظ بآخر 10 أنشطة فقط
                if (data.activityLog.length > 10) {
                    data.activityLog = data.activityLog.slice(0, 10);
                }
                
                this.saveData(data);
                this.loadActivityLog();
            }

            loadDashboard() {
                const data = this.getData();
                
                // تحديث الإحصائيات
                document.getElementById('totalBranches').textContent = data.stats.totalBranches;
                document.getElementById('totalUsers').textContent = data.stats.totalUsers;
                document.getElementById('totalContent').textContent = data.stats.totalContent;
                document.getElementById('activeConnections').textContent = data.stats.activeConnections;

                // تحديث المحتوى الحديث
                this.loadRecentContent();
                this.loadActivityLog();
            }

            loadRecentContent() {
                const data = this.getData();
                const container = document.getElementById('recentContent');
                
                if (data.content.length === 0) {
                    container.innerHTML = '<div style="font-size: 12px; color: #718096;">لا يوجد محتوى حديث</div>';
                    return;
                }

                const recent = data.content.slice(0, 3);
                container.innerHTML = recent.map(content => `
                    <div style="font-size: 12px; color: #4a5568; margin-bottom: 5px;">
                        ${content.icon} ${content.title}
                    </div>
                `).join('');
            }

            loadActivityLog() {
                const data = this.getData();
                const container = document.getElementById('activityLog');
                
                if (data.activityLog.length === 0) {
                    container.innerHTML = '<div class="log-item">لا يوجد نشاط حديث</div>';
                    return;
                }

                container.innerHTML = data.activityLog.map(activity => `
                    <div class="log-item">📊 ${activity}</div>
                `).join('');
            }

            showContentManager() {
                const data = this.getData();
                const modal = document.getElementById('contentModal');
                const contentList = document.getElementById('contentList');

                if (data.content.length === 0) {
                    contentList.innerHTML = '<div style="text-align: center; color: #718096; padding: 40px;">لا يوجد محتوى منشور</div>';
                } else {
                    contentList.innerHTML = data.content.map(content => `
                        <div class="content-item">
                            <div>
                                <div style="font-weight: 600; color: #2d3748;">${content.icon} ${content.title}</div>
                                <div style="font-size: 12px; color: #718096;">بواسطة: ${content.author} • ${content.date}</div>
                            </div>
                            <button class="btn btn-danger" onclick="deleteContent(${content.id})" style="padding: 5px 10px; font-size: 12px;">حذف</button>
                        </div>
                    `).join('');
                }

                modal.classList.add('show');
            }

            deleteContent(contentId) {
                if (confirm('هل تريد حذف هذا المحتوى؟')) {
                    const data = this.getData();
                    data.content = data.content.filter(c => c.id !== contentId);
                    data.stats.totalContent = data.content.length;
                    this.saveData(data);
                    
                    this.addActivity('تم حذف محتوى');
                    this.showContentManager();
                    this.loadDashboard();
                }
            }

            startMonitoring() {
                // مراقبة الفروع كل 5 ثوان
                setInterval(() => {
                    this.updateConnectionStatus();
                }, 5000);
            }

            updateConnectionStatus() {
                // محاكاة تحديث حالة الاتصال
                const data = this.getData();
                Object.keys(data.branches).forEach(branch => {
                    data.branches[branch].lastActivity = new Date().toISOString();
                });
                this.saveData(data);
            }
        }

        // إنشاء مثيل من النظام
        const adminSystem = new AdminSystem();

        // دوال التفاعل
        function updatePermission(branch, permission, value) {
            adminSystem.updatePermission(branch, permission, value);
        }

        function publishContent() {
            adminSystem.publishContent();
        }

        function showContentManager() {
            adminSystem.showContentManager();
        }

        function showScheduledContent() {
            alert('ميزة المحتوى المجدول قيد التطوير...');
        }

        function deleteContent(contentId) {
            adminSystem.deleteContent(contentId);
        }

        function refreshActivity() {
            adminSystem.addActivity('تم تحديث سجل النشاط');
        }

        function closeModal() {
            document.getElementById('contentModal').classList.remove('show');
        }
    </script>
</body>
</html>
