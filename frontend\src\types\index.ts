export interface User {
  id: string;
  username: string;
  fullName: string;
  jobTitle: string;
  department: string;
  userType: UserType;
  isActive: boolean;
  createdAt: string;
  updatedAt?: string;
}

export type UserType = 'MAIN_ADMIN' | 'OPERATIONS_MGR' | 'DEPT_MANAGER' | 'DISPLAY_OP';

export interface AuthResponse {
  message: string;
  token: string;
  user: User;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface CreateUserData {
  username: string;
  password: string;
  fullName: string;
  jobTitle: string;
  department: string;
  userType: UserType;
}

export interface File {
  id: string;
  fileName: string;
  originalName: string;
  filePath: string;
  fileType: string;
  fileSize: number;
  publisherId: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  publisher: {
    id: string;
    fullName: string;
    department: string;
  };
}

export interface PublishFileData {
  fileName: string;
  file: File;
}

export interface Display {
  id: string;
  screenId: string;
  screenName: string;
  operatorId: string;
  isConnected: boolean;
  createdAt: string;
  updatedAt: string;
  operator: {
    id: string;
    fullName: string;
    username: string;
  };
}

export interface CreateDisplayData {
  screenId: string;
  screenName: string;
}

export interface PasswordResetRequest {
  id: string;
  userId: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  user: {
    id: string;
    fullName: string;
    username: string;
  };
}

export interface SearchFilters {
  search?: string;
  publisher?: string;
  department?: string;
  fileType?: string;
}

export interface ApiResponse<T = any> {
  message: string;
  data?: T;
  error?: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

export interface SocketEvents {
  newFilePublished: (data: { file: File; publisher: User }) => void;
  displayConnectionChanged: (data: { display: Display }) => void;
  operatorDisconnected: (data: { operatorId: string; operatorName: string }) => void;
  newFileToDisplay: (data: { file: File; timestamp: string }) => void;
  error: (data: { message: string }) => void;
}

export interface UserPermissions {
  canViewHome: boolean;
  canManageUsers: boolean;
  canPublishFiles: boolean;
  canViewFiles: boolean;
  canDeleteFiles: boolean;
  canManageDisplays: boolean;
  canViewDisplays: boolean;
  canReceiveFiles: boolean;
}

// User type labels in Arabic
export const USER_TYPE_LABELS: Record<UserType, string> = {
  MAIN_ADMIN: 'المدير الرئيسي',
  OPERATIONS_MGR: 'مدير التشغيل',
  DEPT_MANAGER: 'مدير القسم',
  DISPLAY_OP: 'مسؤول الاستعلامات'
};

// File size formatter
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Date formatter for Arabic
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
