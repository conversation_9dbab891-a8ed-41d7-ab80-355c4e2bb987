import React, { useState, useRef, useEffect } from 'react';
import { User, Settings, Globe, MoreVertical } from 'lucide-react';

interface DropdownMenuProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

const DropdownMenu: React.FC<DropdownMenuProps> = ({ trigger, children, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div onClick={() => setIsOpen(!isOpen)} className="cursor-pointer">
        {trigger}
      </div>
      
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {children}
        </div>
      )}
    </div>
  );
};

interface DropdownItemProps {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  className?: string;
}

const DropdownItem: React.FC<DropdownItemProps> = ({ icon, label, onClick, className = '' }) => {
  return (
    <button
      onClick={onClick}
      className={`w-full flex items-center space-x-3 space-x-reverse px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors ${className}`}
    >
      {icon}
      <span>{label}</span>
    </button>
  );
};

interface HeaderDropdownProps {
  onProfileClick: () => void;
  onSettingsClick: () => void;
  onLanguageClick: () => void;
}

const HeaderDropdown: React.FC<HeaderDropdownProps> = ({
  onProfileClick,
  onSettingsClick,
  onLanguageClick
}) => {
  return (
    <DropdownMenu
      trigger={
        <button className="p-2 hover:bg-gray-300 rounded-lg transition-colors">
          <MoreVertical className="h-5 w-5 text-gray-600" />
        </button>
      }
    >
      <DropdownItem
        icon={<User className="h-4 w-4" />}
        label="البروفايل"
        onClick={onProfileClick}
      />
      <DropdownItem
        icon={<Settings className="h-4 w-4" />}
        label="الإعدادات"
        onClick={onSettingsClick}
      />
      <DropdownItem
        icon={<Globe className="h-4 w-4" />}
        label="تغيير اللغة"
        onClick={onLanguageClick}
      />
    </DropdownMenu>
  );
};

export { DropdownMenu, DropdownItem, HeaderDropdown };
