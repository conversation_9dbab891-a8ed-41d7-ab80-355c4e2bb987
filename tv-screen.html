<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شاشة العرض التلفزيونية</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Arial', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            height: 100vh; 
            overflow: hidden;
        }
        
        .setup-screen { 
            display: flex; 
            flex-direction: column; 
            justify-content: center; 
            align-items: center; 
            height: 100vh;
            text-align: center;
            padding: 40px;
        }
        
        .logo { 
            font-size: 64px; 
            margin-bottom: 30px; 
            animation: pulse 2s infinite;
        }
        
        .title { 
            font-size: 48px; 
            font-weight: bold; 
            margin-bottom: 40px; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .connection-info { 
            background: rgba(255, 255, 255, 0.1); 
            padding: 40px; 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
            max-width: 800px;
        }
        
        .ip-display { 
            font-size: 36px; 
            font-weight: 900; 
            background: white; 
            color: #374151; 
            padding: 20px 40px; 
            border-radius: 15px; 
            margin: 20px 0; 
            letter-spacing: 2px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            font-family: 'Courier New', monospace;
        }
        
        .instructions { 
            font-size: 24px; 
            line-height: 1.6;
            margin: 20px 0;
        }
        
        .status { 
            font-size: 28px; 
            padding: 20px 40px; 
            border-radius: 15px; 
            margin-top: 30px; 
            font-weight: bold;
            background: rgba(59, 130, 246, 0.3); 
            border: 2px solid #3b82f6;
        }
        
        .content-display { 
            display: none; 
            position: fixed; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 100%; 
            background: white; 
            z-index: 1000;
        }
        
        .file-header { 
            background: transparent; 
            color: black; 
            padding: 30px; 
            text-align: center; 
            border-bottom: 3px solid #e5e7eb;
        }
        
        .file-title { 
            font-size: 48px; 
            font-weight: 900; 
            margin: 0; 
            color: black;
        }
        
        .file-content { 
            flex: 1; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            padding: 20px;
        }
        
        .connection-indicator {
            position: fixed;
            top: 30px;
            right: 30px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ef4444;
            animation: pulse 2s infinite;
            z-index: 100;
        }
        
        .connection-indicator.connected {
            background: #10b981;
        }
        
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .footer-info {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            z-index: 50;
        }
    </style>
</head>
<body>
    <div class="connection-indicator" id="connectionIndicator"></div>
    
    <div class="setup-screen" id="setupScreen">
        <div class="logo">📺</div>
        <div class="title">شاشة العرض التلفزيونية</div>
        
        <div class="connection-info">
            <div class="instructions">
                استخدم العنوان التالي في البرنامج:
            </div>
            
            <div class="ip-display" id="ipDisplay">
                جاري تحديد العنوان...
            </div>
            
            <div class="status" id="status">
                🔄 في انتظار الاتصال من البرنامج...
            </div>
        </div>
        
        <div class="instructions" style="font-size: 20px; opacity: 0.9;">
            💡 في البرنامج: شاشات العرض ← إضافة شاشة ← أدخل العنوان أعلاه
        </div>
    </div>

    <div class="content-display" id="contentDisplay">
        <!-- المحتوى سيظهر هنا -->
    </div>
    
    <div class="footer-info" id="footerInfo" style="display: none;">
        اضغط أي مكان للعودة للشاشة الرئيسية
    </div>

    <script>
        let isConnected = false;
        let serverIP = '';

        // الحصول على IP الخادم
        function getServerIP() {
            // استخدام hostname من URL أو localhost
            const hostname = window.location.hostname;
            const port = window.location.port || '8080';
            return hostname === '' ? 'localhost' : hostname;
        }

        // تحديث عرض IP
        function updateIPDisplay() {
            serverIP = getServerIP();
            const port = window.location.port || '8080';
            const fullAddress = `${serverIP}:${port}`;
            
            document.getElementById('ipDisplay').textContent = fullAddress;
            console.log('عنوان الخادم:', fullAddress);
        }

        // محاكاة الاتصال (للتجربة)
        function simulateConnection() {
            // فحص localStorage للاتصالات
            setInterval(() => {
                checkForConnection();
                checkForContent();
            }, 1000);
        }

        // فحص محاولات الاتصال
        function checkForConnection() {
            try {
                const connectionData = localStorage.getItem('tv_connection_request');
                if (connectionData) {
                    const data = JSON.parse(connectionData);
                    if (data.timestamp > Date.now() - 5000) { // خلال آخر 5 ثوان
                        handleConnection(data);
                        localStorage.removeItem('tv_connection_request');
                    }
                }
            } catch (e) {
                // تجاهل الأخطاء
            }
        }

        // فحص المحتوى للعرض
        function checkForContent() {
            try {
                const contentData = localStorage.getItem('tv_display_content');
                if (contentData) {
                    const data = JSON.parse(contentData);
                    if (data.timestamp > Date.now() - 3000) { // خلال آخر 3 ثوان
                        displayContent(data);
                        localStorage.removeItem('tv_display_content');
                    }
                }
            } catch (e) {
                // تجاهل الأخطاء
            }
        }

        // معالجة الاتصال
        function handleConnection(data) {
            console.log('محاولة اتصال من:', data);
            
            // التحقق من IP
            if (data.targetIP === serverIP || data.targetIP === 'localhost' || data.targetIP.includes(serverIP)) {
                isConnected = true;
                updateConnectionStatus(true);
                
                document.getElementById('status').innerHTML = `✅ متصل بنجاح مع: ${data.screenName}`;
                document.getElementById('status').style.background = 'rgba(16, 185, 129, 0.3)';
                document.getElementById('status').style.borderColor = '#10b981';
                
                // إرسال تأكيد النجاح
                localStorage.setItem('tv_connection_result', JSON.stringify({
                    success: true,
                    message: 'تم الربط بنجاح!',
                    timestamp: Date.now()
                }));
                
                // إخفاء شاشة الإعداد بعد 3 ثوان
                setTimeout(() => {
                    document.getElementById('setupScreen').style.display = 'none';
                }, 3000);
                
            } else {
                // فشل الاتصال
                localStorage.setItem('tv_connection_result', JSON.stringify({
                    success: false,
                    message: 'عنوان IP غير صحيح',
                    timestamp: Date.now()
                }));
            }
        }

        // عرض المحتوى
        function displayContent(data) {
            console.log('عرض محتوى:', data);
            
            const contentDisplay = document.getElementById('contentDisplay');
            const { fileType, content, contentType } = data;
            
            let displayHTML = '';
            
            if (contentType === 'image' && content) {
                displayHTML = `
                    <div style="display: flex; flex-direction: column; height: 100vh;">
                        <div class="file-header">
                            <h1 class="file-title">${fileType}</h1>
                        </div>
                        <div class="file-content" style="background: black;">
                            <img src="${content}" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="${fileType}">
                        </div>
                    </div>
                `;
            } else if (contentType === 'pdf' && content) {
                displayHTML = `
                    <div style="display: flex; flex-direction: column; height: 100vh;">
                        <div class="file-header">
                            <h1 class="file-title">${fileType}</h1>
                        </div>
                        <div class="file-content">
                            <embed src="${content}" type="application/pdf" width="100%" height="100%" style="border: none;">
                        </div>
                    </div>
                `;
            } else {
                displayHTML = `
                    <div style="display: flex; flex-direction: column; height: 100vh;">
                        <div class="file-header">
                            <h1 class="file-title">${fileType}</h1>
                        </div>
                        <div class="file-content" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <div style="text-align: center; color: white;">
                                <svg width="300" height="300" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                </svg>
                                <p style="font-size: 36px; margin-top: 30px; font-weight: bold;">${fileType}</p>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            contentDisplay.innerHTML = displayHTML;
            contentDisplay.style.display = 'block';
            document.getElementById('footerInfo').style.display = 'block';
            
            // إضافة حدث النقر للعودة
            contentDisplay.onclick = () => {
                contentDisplay.style.display = 'none';
                document.getElementById('setupScreen').style.display = 'flex';
                document.getElementById('footerInfo').style.display = 'none';
                contentDisplay.onclick = null;
            };
            
            // العودة التلقائية بعد 30 ثانية
            setTimeout(() => {
                if (contentDisplay.style.display === 'block') {
                    contentDisplay.style.display = 'none';
                    document.getElementById('setupScreen').style.display = 'flex';
                    document.getElementById('footerInfo').style.display = 'none';
                    contentDisplay.onclick = null;
                }
            }, 30000);
        }

        // تحديث حالة الاتصال
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('connectionIndicator');
            if (connected) {
                indicator.classList.add('connected');
            } else {
                indicator.classList.remove('connected');
            }
        }

        // بدء التطبيق
        function init() {
            console.log('🚀 بدء تشغيل شاشة التلفاز');
            updateIPDisplay();
            simulateConnection();
            updateConnectionStatus(false);
        }

        // تشغيل عند تحميل الصفحة
        window.addEventListener('load', init);
        
        // تحديث IP عند تغيير الصفحة
        window.addEventListener('focus', updateIPDisplay);
    </script>
</body>
</html>
