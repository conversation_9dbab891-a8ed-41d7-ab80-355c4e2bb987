import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import LoginPage from '@/pages/LoginPage';
import HomePage from '@/pages/HomePage';
import OperationsManagerPage from '@/pages/OperationsManagerPage';
import DepartmentManagerPage from '@/pages/DepartmentManagerPage';
import DisplayOperatorPage from '@/pages/DisplayOperatorPage';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Route based on user type
const UserTypeRoute: React.FC = () => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  switch (user.userType) {
    case 'MAIN_ADMIN':
      return <HomePage />;
    case 'OPERATIONS_MGR':
      return <OperationsManagerPage />;
    case 'DEPT_MANAGER':
      return <DepartmentManagerPage />;
    case 'DISPLAY_OP':
      return <DisplayOperatorPage />;
    default:
      return <Navigate to="/login" replace />;
  }
};

// App Routes Component
const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route path="/" element={
        <ProtectedRoute>
          <UserTypeRoute />
        </ProtectedRoute>
      } />
      <Route path="/home" element={
        <ProtectedRoute>
          <HomePage />
        </ProtectedRoute>
      } />
      <Route path="/operations" element={
        <ProtectedRoute>
          <OperationsManagerPage />
        </ProtectedRoute>
      } />
      <Route path="/department" element={
        <ProtectedRoute>
          <DepartmentManagerPage />
        </ProtectedRoute>
      } />
      <Route path="/display" element={
        <ProtectedRoute>
          <DisplayOperatorPage />
        </ProtectedRoute>
      } />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

// Main App Component
const App: React.FC = () => {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50">
        <AppRoutes />
      </div>
    </AuthProvider>
  );
};

export default App;
