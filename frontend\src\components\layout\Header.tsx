import React from 'react';
import { 
  LogOut,
  User,
  Bell,
  Home
} from 'lucide-react';
import { HeaderDropdown } from '@/components/ui/DropdownMenu';
import toast from 'react-hot-toast';

interface HeaderProps {
  hasNewNotification: boolean;
  onNotificationClick: () => void;
  onLogout: () => void;
  onProfileClick: () => void;
  onSettingsClick: () => void;
  onLanguageClick: () => void;
  userName?: string;
}

const Header: React.FC<HeaderProps> = ({
  hasNewNotification,
  onNotificationClick,
  onLogout,
  onProfileClick,
  onSettingsClick,
  onLanguageClick,
  userName = 'حسين نهاد'
}) => {
  return (
    <header className="bg-[#E6E6E6] shadow-sm border-b border-gray-300">
      <div className="max-w-full px-4">
        <div className="flex justify-between items-center h-16">
          {/* Left Side - Dropdown, Logo, App Name, Home, Notifications, User */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Three Dots Dropdown */}
            <HeaderDropdown
              onProfileClick={onProfileClick}
              onSettingsClick={onSettingsClick}
              onLanguageClick={onLanguageClick}
            />

            {/* Logo */}
            <div className="flex items-center">
              <img 
                src="/Logo.png" 
                alt="TV-Office Logo" 
                className="h-10 w-10 object-contain logo-hover cursor-pointer"
                onError={(e) => {
                  // Fallback to placeholder logo if original not found
                  e.currentTarget.src = '/logo-placeholder.svg';
                }}
                onClick={() => toast.info('TV-Office Management System v1.0')}
              />
            </div>

            {/* App Name */}
            <div className="text-lg font-bold text-gray-800">
              TV-Office
            </div>

            {/* Home Icon and Text */}
            <div className="flex items-center space-x-2 space-x-reverse text-gray-700">
              <Home className="h-5 w-5" />
              <span className="text-sm font-medium">Home</span>
            </div>

            {/* Notification Bell */}
            <button 
              className="relative p-2 hover:bg-gray-300 rounded-lg transition-colors"
              onClick={onNotificationClick}
            >
              <Bell className={`h-5 w-5 transition-colors ${
                hasNewNotification 
                  ? 'text-red-500 bell-notification' 
                  : 'text-gray-600'
              }`} />
              {hasNewNotification && (
                <span className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full animate-pulse"></span>
              )}
            </button>

            {/* User Info */}
            <div className="flex items-center space-x-2 space-x-reverse">
              <User className="h-5 w-5 text-gray-600" />
              <span className="text-sm font-medium text-gray-800">{userName}</span>
            </div>
          </div>

          {/* Right Side - Logout */}
          <div className="flex items-center">
            <button
              onClick={onLogout}
              className="flex items-center space-x-2 space-x-reverse bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <LogOut className="h-4 w-4" />
              <span className="text-sm font-medium">تسجيل الخروج</span>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
