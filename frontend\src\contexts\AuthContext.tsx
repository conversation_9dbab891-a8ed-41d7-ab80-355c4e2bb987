import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, UserType, UserPermissions } from '@/types';
import { authService } from '@/services/auth';
import socketService from '@/services/socket';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  permissions: UserPermissions;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Helper function to get user permissions based on user type
const getUserPermissions = (userType: UserType | null): UserPermissions => {
  if (!userType) {
    return {
      canViewHome: false,
      canManageUsers: false,
      canPublishFiles: false,
      canViewFiles: false,
      canDeleteFiles: false,
      canManageDisplays: false,
      canViewDisplays: false,
      canReceiveFiles: false,
    };
  }

  switch (userType) {
    case 'MAIN_ADMIN':
      return {
        canViewHome: true,
        canManageUsers: true,
        canPublishFiles: true,
        canViewFiles: true,
        canDeleteFiles: true,
        canManageDisplays: false,
        canViewDisplays: true,
        canReceiveFiles: true,
      };
    
    case 'OPERATIONS_MGR':
      return {
        canViewHome: false, // يفتح له واجهة خاصة
        canManageUsers: false,
        canPublishFiles: true,
        canViewFiles: true,
        canDeleteFiles: false,
        canManageDisplays: false,
        canViewDisplays: false,
        canReceiveFiles: true,
      };
    
    case 'DEPT_MANAGER':
      return {
        canViewHome: false, // يستلم الملفات تلقائياً
        canManageUsers: false,
        canPublishFiles: false,
        canViewFiles: true,
        canDeleteFiles: false,
        canManageDisplays: false,
        canViewDisplays: false,
        canReceiveFiles: true,
      };
    
    case 'DISPLAY_OP':
      return {
        canViewHome: false, // واجهة خاصة لإدارة الشاشات
        canManageUsers: false,
        canPublishFiles: false,
        canViewFiles: false,
        canDeleteFiles: false,
        canManageDisplays: true,
        canViewDisplays: true,
        canReceiveFiles: true,
      };
    
    default:
      return {
        canViewHome: false,
        canManageUsers: false,
        canPublishFiles: false,
        canViewFiles: false,
        canDeleteFiles: false,
        canManageDisplays: false,
        canViewDisplays: false,
        canReceiveFiles: false,
      };
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // مستخدم وهمي للاختبار
  const [user, setUser] = useState<User | null>({
    id: '1',
    username: 'admin',
    fullName: 'حسين نهاد',
    jobTitle: 'المدير الرئيسي',
    department: 'تقنية المعلومات',
    userType: 'MAIN_ADMIN',
    isActive: true,
    createdAt: new Date().toISOString()
  });
  const [isLoading, setIsLoading] = useState(false);

  const isAuthenticated = !!user;
  const permissions = getUserPermissions(user?.userType || null);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const currentUser = authService.getCurrentUser();
        const hasToken = authService.hasToken();

        if (currentUser && hasToken) {
          // Verify token with server
          try {
            const { user: verifiedUser } = await authService.verifyToken();
            setUser(verifiedUser);
            
            // Connect to socket
            socketService.connect();
          } catch (error) {
            // Token is invalid, clear local storage
            console.error('Token verification failed:', error);
            await authService.logout();
            setUser(null);
          }
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (username: string, password: string) => {
    try {
      const response = await authService.login({ username, password });
      setUser(response.user);
      
      // Connect to socket after successful login
      socketService.connect();
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
      socketService.disconnect();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear local state
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    permissions,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
