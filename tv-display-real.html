<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شاشة العرض التلفزيونية - الإصدار الحقيقي</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Arial', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            height: 100vh; 
            display: flex; 
            flex-direction: column; 
            justify-content: center; 
            align-items: center; 
            text-align: center;
        }
        
        .container { 
            max-width: 800px; 
            padding: 40px; 
            background: rgba(255, 255, 255, 0.1); 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo { 
            font-size: 48px; 
            margin-bottom: 20px; 
        }
        
        .title { 
            font-size: 36px; 
            font-weight: bold; 
            margin-bottom: 30px; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .security-code { 
            font-size: 72px; 
            font-weight: 900; 
            background: white; 
            color: #374151; 
            padding: 30px 50px; 
            border-radius: 15px; 
            margin: 30px 0; 
            letter-spacing: 8px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            font-family: 'Courier New', monospace;
        }
        
        .instructions { 
            font-size: 24px; 
            margin-bottom: 20px; 
            line-height: 1.6;
        }
        
        .status { 
            font-size: 20px; 
            padding: 15px 30px; 
            border-radius: 10px; 
            margin-top: 20px; 
            font-weight: bold;
        }
        
        .status.waiting { 
            background: rgba(59, 130, 246, 0.3); 
            border: 2px solid #3b82f6; 
        }
        
        .status.connected { 
            background: rgba(16, 185, 129, 0.3); 
            border: 2px solid #10b981; 
        }
        
        .status.error { 
            background: rgba(239, 68, 68, 0.3); 
            border: 2px solid #ef4444; 
        }
        
        .ip-info { 
            font-size: 18px; 
            margin-top: 20px; 
            opacity: 0.8;
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 10px;
        }
        
        .content-area { 
            display: none; 
            position: fixed; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 100%; 
            background: black; 
            z-index: 1000;
        }
        
        .connection-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ef4444;
            animation: pulse 2s infinite;
        }
        
        .connection-indicator.connected {
            background: #10b981;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="connection-indicator" id="connectionIndicator"></div>
    
    <div class="container">
        <div class="logo">📺</div>
        <div class="title">شاشة العرض التلفزيونية</div>
        
        <div class="instructions">
            أدخل رمز الأمان التالي في البرنامج لربط هذه الشاشة:
        </div>
        
        <div class="security-code" id="securityCode">------</div>
        
        <div class="status waiting" id="status">
            🔄 جاري الاتصال بالخادم...
        </div>
        
        <div class="ip-info" id="ipInfo">
            جاري تحديد عنوان الخادم...
        </div>
        
        <div class="instructions" style="font-size: 18px; margin-top: 30px;">
            📱 في البرنامج: شاشات العرض ← إضافة شاشة ← أدخل IP + رمز الأمان
        </div>
    </div>

    <!-- Content Display Area -->
    <div class="content-area" id="contentArea">
        <!-- Content will be displayed here -->
    </div>

    <script>
        let socket = null;
        let securityCode = '';
        let isConnected = false;
        let reconnectInterval = null;

        // الحصول على عنوان الخادم
        function getServerAddress() {
            // محاولة الحصول على IP من URL أو استخدام localhost
            const urlParams = new URLSearchParams(window.location.search);
            const serverIP = urlParams.get('server') || window.location.hostname || 'localhost';
            const serverPort = urlParams.get('port') || '8080';
            return `${serverIP}:${serverPort}`;
        }

        // الاتصال بخادم WebSocket
        function connectToServer() {
            const serverAddress = getServerAddress();
            const wsUrl = `ws://${serverAddress}`;
            
            console.log('محاولة الاتصال بـ:', wsUrl);
            
            try {
                socket = new WebSocket(wsUrl);
                
                socket.onopen = function() {
                    console.log('✅ تم الاتصال بالخادم');
                    isConnected = true;
                    updateConnectionStatus(true);
                    
                    // تسجيل هذه الشاشة
                    socket.send(JSON.stringify({
                        type: 'register_display',
                        name: 'شاشة تلفزيونية'
                    }));
                };
                
                socket.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        handleMessage(message);
                    } catch (e) {
                        console.error('خطأ في تحليل الرسالة:', e);
                    }
                };
                
                socket.onclose = function() {
                    console.log('❌ انقطع الاتصال مع الخادم');
                    isConnected = false;
                    updateConnectionStatus(false);
                    
                    // محاولة إعادة الاتصال
                    if (!reconnectInterval) {
                        reconnectInterval = setInterval(() => {
                            console.log('🔄 محاولة إعادة الاتصال...');
                            connectToServer();
                        }, 5000);
                    }
                };
                
                socket.onerror = function(error) {
                    console.error('❌ خطأ في الاتصال:', error);
                    updateConnectionStatus(false, 'خطأ في الاتصال بالخادم');
                };
                
            } catch (error) {
                console.error('❌ فشل في إنشاء الاتصال:', error);
                updateConnectionStatus(false, 'فشل في الاتصال');
            }
        }

        // معالجة الرسائل الواردة
        function handleMessage(message) {
            console.log('📨 رسالة واردة:', message);
            
            switch (message.type) {
                case 'security_code':
                    securityCode = message.code;
                    document.getElementById('securityCode').textContent = securityCode;
                    document.getElementById('ipInfo').innerHTML = `
                        <strong>عنوان الخادم:</strong> ${message.ip}:8080<br>
                        <strong>رمز الأمان:</strong> ${securityCode}
                    `;
                    document.getElementById('status').innerHTML = '🔄 في انتظار الربط من البرنامج...';
                    document.getElementById('status').className = 'status waiting';
                    break;
                    
                case 'display_content':
                    displayContent(message);
                    break;
                    
                case 'auth_success':
                    document.getElementById('status').innerHTML = '✅ تم الربط بنجاح!';
                    document.getElementById('status').className = 'status connected';
                    
                    // إخفاء شاشة الإعداد بعد 3 ثوان
                    setTimeout(() => {
                        document.querySelector('.container').style.display = 'none';
                    }, 3000);
                    break;
            }
        }

        // تحديث حالة الاتصال
        function updateConnectionStatus(connected, message = '') {
            const indicator = document.getElementById('connectionIndicator');
            const status = document.getElementById('status');
            
            if (connected) {
                indicator.classList.add('connected');
                if (reconnectInterval) {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }
            } else {
                indicator.classList.remove('connected');
                status.innerHTML = message || '❌ لا يوجد اتصال بالخادم';
                status.className = 'status error';
            }
        }

        // عرض المحتوى على الشاشة
        function displayContent(message) {
            console.log('📺 عرض محتوى:', message);
            
            const contentArea = document.getElementById('contentArea');
            const { fileType, content, contentType } = message;
            
            // إخفاء الشاشة الرئيسية
            document.querySelector('.container').style.display = 'none';
            
            let displayHTML = '';
            
            if (contentType === 'image' && content) {
                displayHTML = `
                    <div style="background: white; padding: 0; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div style="background: transparent; color: black; padding: 20px; text-align: center;">
                            <h1 style="font-size: 48px; font-weight: 900; margin: 0; color: black;">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: black; display: flex; align-items: center; justify-content: center;">
                            <img src="${content}" style="max-width: 100%; max-height: 100%; object-fit: contain;">
                        </div>
                    </div>
                `;
            } else if (contentType === 'pdf' && content) {
                displayHTML = `
                    <div style="background: white; padding: 0; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div style="background: transparent; color: black; padding: 20px; text-align: center;">
                            <h1 style="font-size: 48px; font-weight: 900; margin: 0; color: black;">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: white; display: flex; align-items: center; justify-content: center;">
                            <embed src="${content}" type="application/pdf" width="100%" height="100%" style="border: none;">
                        </div>
                    </div>
                `;
            } else {
                displayHTML = `
                    <div style="background: white; padding: 0; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <div style="background: transparent; color: black; padding: 20px; text-align: center;">
                            <h1 style="font-size: 48px; font-weight: 900; margin: 0; color: black;">${fileType}</h1>
                        </div>
                        <div style="flex: 1; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; flex-direction: column; align-items: center; justify-content: center;">
                            <svg width="300" height="300" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="1" style="margin: 0 auto; display: block; opacity: 0.8;">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                            </svg>
                            <p style="color: white; font-size: 36px; margin-top: 30px; font-weight: bold;">${fileType}</p>
                        </div>
                    </div>
                `;
            }
            
            contentArea.innerHTML = displayHTML;
            contentArea.style.display = 'block';
            
            // العودة للشاشة الرئيسية بعد 30 ثانية
            setTimeout(() => {
                contentArea.style.display = 'none';
                document.querySelector('.container').style.display = 'flex';
            }, 30000);
        }

        // بدء التطبيق
        function init() {
            console.log('🚀 بدء تشغيل شاشة العرض التلفزيونية');
            connectToServer();
        }

        // تشغيل عند تحميل الصفحة
        window.addEventListener('load', init);
        
        // إعادة الاتصال عند استعادة التركيز
        window.addEventListener('focus', () => {
            if (!isConnected) {
                connectToServer();
            }
        });
    </script>
</body>
</html>
