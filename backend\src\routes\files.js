const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();
const prisma = new PrismaClient();

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../../uploads');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760 // 10MB default
  },
  fileFilter: (req, file, cb) => {
    // قبول ملفات PDF فقط
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('يُسمح بملفات PDF فقط'), false);
    }
  }
});

// نشر ملف جديد
router.post('/publish', 
  authenticateToken, 
  authorizeRoles('MAIN_ADMIN', 'OPERATIONS_MGR'), 
  upload.single('file'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'الملف مطلوب' });
      }

      const { fileName } = req.body;
      if (!fileName) {
        return res.status(400).json({ message: 'اسم الملف مطلوب' });
      }

      // إنشاء سجل الملف في قاعدة البيانات
      const newFile = await prisma.file.create({
        data: {
          fileName,
          originalName: req.file.originalname,
          filePath: req.file.path,
          fileType: req.file.mimetype,
          fileSize: req.file.size,
          publisherId: req.user.id
        },
        include: {
          publisher: {
            select: {
              id: true,
              fullName: true,
              department: true
            }
          }
        }
      });

      // إرسال إشعار عبر Socket.io للمستخدمين المناسبين
      const io = req.app.get('io');
      if (io) {
        io.emit('newFilePublished', {
          file: newFile,
          publisher: newFile.publisher
        });
      }

      res.status(201).json({
        message: 'تم نشر الملف بنجاح',
        file: newFile
      });
    } catch (error) {
      console.error('Publish file error:', error);
      
      // حذف الملف في حالة حدوث خطأ
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      
      res.status(500).json({ message: 'خطأ في رفع الملف' });
    }
  }
);

// الحصول على جميع الملفات
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { search, publisher, department, fileType } = req.query;
    
    let whereClause = { isDeleted: false };
    
    // إضافة فلاتر البحث
    if (search) {
      whereClause.OR = [
        { fileName: { contains: search, mode: 'insensitive' } },
        { originalName: { contains: search, mode: 'insensitive' } }
      ];
    }
    
    if (publisher) {
      whereClause.publisher = {
        fullName: { contains: publisher, mode: 'insensitive' }
      };
    }
    
    if (department) {
      whereClause.publisher = {
        ...whereClause.publisher,
        department: { contains: department, mode: 'insensitive' }
      };
    }

    const files = await prisma.file.findMany({
      where: whereClause,
      include: {
        publisher: {
          select: {
            id: true,
            fullName: true,
            department: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(files);
  } catch (error) {
    console.error('Get files error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على ملف محدد
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const file = await prisma.file.findUnique({
      where: { id, isDeleted: false },
      include: {
        publisher: {
          select: {
            id: true,
            fullName: true,
            department: true
          }
        }
      }
    });

    if (!file) {
      return res.status(404).json({ message: 'الملف غير موجود' });
    }

    res.json(file);
  } catch (error) {
    console.error('Get file error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تحميل الملف
router.get('/:id/download', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const file = await prisma.file.findUnique({
      where: { id, isDeleted: false }
    });

    if (!file) {
      return res.status(404).json({ message: 'الملف غير موجود' });
    }

    if (!fs.existsSync(file.filePath)) {
      return res.status(404).json({ message: 'الملف غير موجود على الخادم' });
    }

    res.download(file.filePath, file.originalName);
  } catch (error) {
    console.error('Download file error:', error);
    res.status(500).json({ message: 'خطأ في تحميل الملف' });
  }
});

module.exports = router;
