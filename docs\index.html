<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة التلفزيونات المكتبية - TV Office</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
            margin: 20px;
        }

        .logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
        }

        h1 {
            color: #2d3748;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .subtitle {
            color: #4a5568;
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature {
            background: #f7fafc;
            padding: 20px;
            border-radius: 16px;
            border: 1px solid #e2e8f0;
        }

        .feature-icon {
            font-size: 24px;
            color: #667eea;
            margin-bottom: 10px;
        }

        .feature-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .feature-desc {
            color: #718096;
            font-size: 14px;
        }

        .status {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 12px;
            padding: 15px;
            margin: 20px 0;
        }

        .status-title {
            color: #22543d;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .status-desc {
            color: #2f855a;
            font-size: 14px;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }

        .info {
            background: #ebf8ff;
            border: 1px solid #90cdf4;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .info-title {
            color: #2c5282;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .info-list {
            color: #2b6cb0;
            font-size: 14px;
            line-height: 1.8;
        }

        .info-list li {
            margin-bottom: 5px;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 28px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-tv"></i>
        </div>

        <h1>نظام إدارة التلفزيونات المكتبية</h1>
        <p class="subtitle">
            نظام متكامل لإدارة ونشر المحتوى على شاشات العرض في المكاتب والمؤسسات
        </p>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="feature-title">إدارة المستخدمين</div>
                <div class="feature-desc">إضافة وإدارة المستخدمين مع تحديد الصلاحيات</div>
            </div>

            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="feature-title">نشر الملفات</div>
                <div class="feature-desc">رفع ونشر ملفات PDF للمستخدمين والشاشات</div>
            </div>

            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-desktop"></i>
                </div>
                <div class="feature-title">شاشات العرض</div>
                <div class="feature-desc">ربط وإدارة شاشات التلفزيون والعرض</div>
            </div>

            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="feature-title">إشعارات فورية</div>
                <div class="feature-desc">تحديثات مباشرة وإشعارات للمستخدمين</div>
            </div>
        </div>

        <div class="status">
            <div class="status-title">
                <i class="fas fa-check-circle"></i>
                النظام جاهز للاستخدام
            </div>
            <div class="status-desc">
                تم إعداد النظام بنجاح مع قاعدة بيانات سحابية وجميع الوظائف تعمل بشكل فعلي
            </div>
        </div>

        <div class="info">
            <div class="info-title">
                <i class="fas fa-info-circle"></i>
                بيانات تسجيل الدخول التجريبية:
            </div>
            <ul class="info-list">
                <li><strong>المدير الرئيسي:</strong> admin / admin123</li>
                <li><strong>مدير التشغيل:</strong> operations_mgr / 123456</li>
                <li><strong>مدير الموارد البشرية:</strong> dept_mgr_hr / 123456</li>
                <li><strong>مدير المالية:</strong> dept_mgr_finance / 123456</li>
                <li><strong>مسؤول الاستعلامات:</strong> display_op / 123456</li>
            </ul>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري التحقق من حالة النظام...</p>
        </div>

        <div class="buttons">
            <a href="demo.html" class="btn btn-primary" id="enterSystem">
                <i class="fas fa-sign-in-alt"></i>
                دخول النظام
            </a>
            
            <a href="https://github.com/skayhr/tv-office" class="btn btn-secondary" target="_blank">
                <i class="fab fa-github"></i>
                عرض الكود المصدري
            </a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0; color: #718096; font-size: 14px;">
            <p>
                <i class="fas fa-code"></i>
                تم تطويره باستخدام React, Node.js, Supabase
            </p>
            <p style="margin-top: 5px;">
                <i class="fas fa-heart" style="color: #e53e3e;"></i>
                صُنع بحب في المملكة العربية السعودية
            </p>
        </div>
    </div>

    <script>
        // التحقق من حالة النظام
        async function checkSystemStatus() {
            const loadingEl = document.getElementById('loading');
            const enterBtn = document.getElementById('enterSystem');
            
            try {
                loadingEl.style.display = 'block';
                
                // محاولة الاتصال بالنظام
                const response = await fetch('demo.html', { method: 'HEAD' });
                
                if (response.ok) {
                    loadingEl.style.display = 'none';
                    enterBtn.style.display = 'inline-flex';
                } else {
                    throw new Error('System not available');
                }
            } catch (error) {
                loadingEl.style.display = 'none';
                enterBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> النظام غير متاح حالياً';
                enterBtn.style.background = '#e53e3e';
                enterBtn.onclick = (e) => {
                    e.preventDefault();
                    alert('النظام غير متاح حالياً، يرجى المحاولة لاحقاً');
                };
            }
        }

        // تشغيل التحقق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', checkSystemStatus);

        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.feature').forEach(feature => {
            feature.addEventListener('mouseenter', () => {
                feature.style.transform = 'translateY(-5px)';
                feature.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
            });
            
            feature.addEventListener('mouseleave', () => {
                feature.style.transform = 'translateY(0)';
                feature.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>
