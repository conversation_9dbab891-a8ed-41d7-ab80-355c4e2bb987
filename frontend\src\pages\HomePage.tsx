import React, { useState } from 'react';
import {
  Clipboard,
  Settings,
  Search,
  Upload,
  Trash2,
  Monitor
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import toast from 'react-hot-toast';

// Import components
import ClipboardModal from '@/components/modals/ClipboardModal';
import SettingsModal from '@/components/modals/SettingsModal';
import SearchModal from '@/components/modals/SearchModal';
import PublishModal from '@/components/modals/PublishModal';
import DeletedItemsModal from '@/components/modals/DeletedItemsModal';
import DisplayScreensModal from '@/components/modals/DisplayScreensModal';

const HomePage: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [hasNewNotification, setHasNewNotification] = useState(true); // مؤقت للاختبار

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('تم تسجيل الخروج بنجاح');
    } catch (error) {
      toast.error('خطأ في تسجيل الخروج');
    }
  };

  const handleProfileClick = () => {
    toast.info('البروفايل قيد التطوير');
  };

  const handleSettingsClick = () => {
    setActiveModal('settings');
  };

  const handleLanguageClick = () => {
    toast.info('تغيير اللغة قيد التطوير');
  };

  const handleNotificationClick = () => {
    setHasNewNotification(false);
    toast.success('تم عرض جميع التنبيهات');
  };

  const homeCards = [
    {
      id: 'clipboard',
      title: 'الحافظة',
      icon: Clipboard,
      color: 'bg-blue-500',
      description: 'الملفات المرسلة والمستلمة'
    },
    {
      id: 'settings',
      title: 'الإعدادات',
      icon: Settings,
      color: 'bg-gray-600',
      description: 'إدارة المستخدمين والصلاحيات'
    },
    {
      id: 'search',
      title: 'البحث',
      icon: Search,
      color: 'bg-green-500',
      description: 'البحث في الملفات المنشورة'
    },
    {
      id: 'publish',
      title: 'النشر',
      icon: Upload,
      color: 'bg-purple-500',
      description: 'نشر ملف جديد'
    },
    {
      id: 'deleted',
      title: 'المحذوفات',
      icon: Trash2,
      color: 'bg-red-500',
      description: 'الملفات المحذوفة'
    },
    {
      id: 'displays',
      title: 'شاشات العرض',
      icon: Monitor,
      color: 'bg-indigo-500',
      description: 'إدارة شاشات التلفزيون'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header
        hasNewNotification={hasNewNotification}
        onNotificationClick={handleNotificationClick}
        onLogout={handleLogout}
        onProfileClick={handleProfileClick}
        onSettingsClick={handleSettingsClick}
        onLanguageClick={handleLanguageClick}
        userName={user?.fullName || 'حسين نهاد'}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً، {user?.fullName}
          </h2>
          <p className="text-gray-600">
            اختر من الخيارات التالية لإدارة النظام
          </p>
        </div>

        {/* Home Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {homeCards.map((card) => {
            const IconComponent = card.icon;
            return (
              <div
                key={card.id}
                onClick={() => setActiveModal(card.id)}
                className="home-card group"
              >
                <div className={`home-card-icon ${card.color}`}>
                  <IconComponent className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2 text-center">
                  {card.title}
                </h3>
                <p className="text-gray-600 text-center text-sm">
                  {card.description}
                </p>
              </div>
            );
          })}
        </div>
      </main>

      {/* Modals */}
      {activeModal === 'clipboard' && (
        <ClipboardModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
        />
      )}

      {activeModal === 'settings' && (
        <SettingsModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
        />
      )}

      {activeModal === 'search' && (
        <SearchModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
        />
      )}

      {activeModal === 'publish' && (
        <PublishModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
        />
      )}

      {activeModal === 'deleted' && (
        <DeletedItemsModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
        />
      )}

      {activeModal === 'displays' && (
        <DisplayScreensModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
        />
      )}
    </div>
  );
};

export default HomePage;
