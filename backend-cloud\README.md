# TV Office Management System - Backend Cloud

Backend API للنظام إدارة التلفزيونات المكتبية - النسخة السحابية

## التقنيات المستخدمة

- **Node.js** مع Express.js
- **Supabase** (PostgreSQL + Storage + Auth)
- **Socket.io** للتحديثات المباشرة
- **JWT** للمصادقة
- **Vercel** للاستضافة

## الإعداد والنشر

### 1. إعداد Supabase

1. أنشئ مشروع جديد على [Supabase](https://supabase.com)
2. شغل ملفات SQL من مجلد `../database/`
3. أنشئ bucket للملفات باسم `files`
4. احصل على:
   - Project URL
   - anon public key
   - service_role key

### 2. إعداد متغيرات البيئة

انسخ `.env.example` إلى `.env` وأضف البيانات:

```bash
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
JWT_SECRET=your-jwt-secret
```

### 3. النشر على Vercel

1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Vercel
3. أضف متغيرات البيئة في Vercel
4. انشر المشروع

## API Endpoints

### المصادقة
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/logout` - تسجيل الخروج
- `GET /api/auth/verify` - التحقق من الرمز المميز

### المستخدمين
- `GET /api/users` - جلب جميع المستخدمين
- `POST /api/users` - إضافة مستخدم جديد
- `PUT /api/users/:id` - تحديث بيانات المستخدم
- `DELETE /api/users/:id` - حذف مستخدم

### الملفات
- `POST /api/files/publish` - نشر ملف جديد
- `GET /api/files` - جلب جميع الملفات
- `GET /api/files/:id` - جلب ملف محدد
- `DELETE /api/files/:id` - حذف ملف
- `PATCH /api/files/:id/restore` - استعادة ملف

### الشاشات
- `GET /api/displays` - جلب جميع الشاشات
- `POST /api/displays` - إضافة شاشة جديدة
- `PATCH /api/displays/:id/connection` - تحديث حالة الاتصال
- `DELETE /api/displays/:id` - حذف شاشة

## Socket.io Events

### للعملاء
- `newFilePublished` - ملف جديد تم نشره
- `displayConnectionChanged` - تغيير حالة اتصال الشاشة
- `newFileToDisplay` - ملف جديد للعرض
- `autoReceiveFile` - استلام تلقائي للملف

### للخادم
- `connectToDisplay` - الاتصال بشاشة عرض
- `disconnectFromDisplay` - قطع الاتصال بشاشة العرض
- `sendFileToDisplay` - إرسال ملف للعرض
- `filePublished` - إشعار بنشر ملف

## الأمان

- **JWT Authentication** لجميع الطلبات
- **Row Level Security** في Supabase
- **CORS** محدود للنطاقات المسموحة
- **Rate Limiting** للحماية من الهجمات
- **Input Validation** لجميع البيانات

## المراقبة

- **Health Check**: `/api/health`
- **Logs**: متوفرة في Vercel Dashboard
- **Error Tracking**: مدمج مع Express

## الدعم

للمساعدة أو الإبلاغ عن مشاكل، تواصل مع فريق التطوير.
