<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة التلفزيونات المكتبية - محدث</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); direction: rtl; margin: 0; padding: 0; min-height: 100vh; overflow: hidden; }
        
        .container { display: flex; flex-direction: column; height: 100vh; }
        .header { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); padding: 20px; box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1); }
        .header-content { display: flex; justify-content: space-between; align-items: center; max-width: 1200px; margin: 0 auto; }
        .logo { display: flex; align-items: center; gap: 12px; }
        .logo-icon { width: 40px; height: 40px; background: #3b82f6; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; }
        .logo-text { font-size: 24px; font-weight: 700; color: #1f2937; }
        .user-info { display: flex; align-items: center; gap: 16px; }
        .notification-bell { position: relative; cursor: pointer; padding: 8px; border-radius: 8px; transition: all 0.2s; }
        .notification-bell:hover { background: rgba(59, 130, 246, 0.1); }
        .notification-dot { position: absolute; top: 6px; right: 6px; width: 8px; height: 8px; background: #ef4444; border-radius: 50%; }
        .user-avatar { width: 40px; height: 40px; background: #e5e7eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #6b7280; }
        
        .main-content { flex: 1; padding: 40px; display: flex; align-items: center; justify-content: center; }
        .dashboard { display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; max-width: 1000px; width: 100%; }
        
        .dashboard-item { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 40px; text-align: center; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); border: 2px solid transparent; }
        .dashboard-item:hover { transform: translateY(-10px); box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15); border-color: rgba(255, 255, 255, 0.3); }
        
        .dashboard-item.clipboard { background: linear-gradient(135deg, #CA188F 0%, #e91e63 100%); color: white; }
        .dashboard-item.settings { background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%); color: white; }
        .dashboard-item.search { background: linear-gradient(135deg, #10b981 0%, #34d399 100%); color: white; }
        .dashboard-item.publish { background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%); color: white; }
        .dashboard-item.deleted { background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%); color: white; }
        .dashboard-item.display { background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%); color: white; }
        
        .dashboard-icon { font-size: 48px; margin-bottom: 20px; }
        .dashboard-title { font-size: 20px; font-weight: 600; margin-bottom: 10px; }
        .dashboard-description { font-size: 14px; opacity: 0.9; }
        
        .modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); display: none; align-items: center; justify-content: center; z-index: 1000; }
        .modal-overlay.active { display: flex; }
        .modal-content { background: white; border-radius: 16px; padding: 0; max-width: 95vw; max-height: 95vh; width: 95vw; height: 85vh; overflow: hidden; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25); }
        .modal-header { display: flex; align-items: center; justify-content: space-between; padding: 24px; border-bottom: 1px solid #e5e7eb; }
        .modal-title { display: flex; align-items: center; gap: 12px; font-size: 20px; font-weight: 600; color: #111827; }
        .modal-close { background: none; border: none; padding: 8px; border-radius: 8px; cursor: pointer; color: #6b7280; transition: all 0.2s; }
        .modal-close:hover { background-color: #f3f4f6; color: #374151; }
        .modal-body { padding: 24px; max-height: 75vh; overflow-y: auto; }
        
        .notification { position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 16px 24px; border-radius: 12px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); transform: translateX(400px); transition: all 0.3s ease; z-index: 2000; }
        .notification.show { transform: translateX(0); }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">📺</div>
                    <div class="logo-text">نظام إدارة التلفزيونات المكتبية</div>
                </div>
                <div class="user-info">
                    <div class="notification-bell" onclick="toggleNotification()" id="notificationBell">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                            <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                        </svg>
                        <div class="notification-dot" id="notificationDot"></div>
                    </div>
                    <div class="user-avatar">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                    </div>
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="dashboard">
                <div class="dashboard-item clipboard" onclick="openModal('clipboard')">
                    <div class="dashboard-icon">📋</div>
                    <div class="dashboard-title">الحافظة</div>
                    <div class="dashboard-description">عرض الملفات المحفوظة</div>
                </div>
                
                <div class="dashboard-item settings" onclick="openModal('settings')">
                    <div class="dashboard-icon">⚙️</div>
                    <div class="dashboard-title">الإعدادات</div>
                    <div class="dashboard-description">إدارة النظام والمستخدمين</div>
                </div>
                
                <div class="dashboard-item search" onclick="openModal('search')">
                    <div class="dashboard-icon">🔍</div>
                    <div class="dashboard-title">البحث</div>
                    <div class="dashboard-description">البحث في الملفات</div>
                </div>
                
                <div class="dashboard-item publish" onclick="openModal('publish')">
                    <div class="dashboard-icon">📤</div>
                    <div class="dashboard-title">النشر</div>
                    <div class="dashboard-description">نشر ملف جديد</div>
                </div>
                
                <div class="dashboard-item deleted" onclick="openModal('deleted')">
                    <div class="dashboard-icon">🗑️</div>
                    <div class="dashboard-title">المحذوفات</div>
                    <div class="dashboard-description">الملفات المحذوفة</div>
                </div>
                
                <div class="dashboard-item display" onclick="openModal('display')">
                    <div class="dashboard-icon">📺</div>
                    <div class="dashboard-title">شاشات العرض</div>
                    <div class="dashboard-description">إدارة شاشات العرض</div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal -->
    <div class="modal-overlay" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">
                    <span id="modalIcon"></span>
                    <span id="modalTitleText">العنوان</span>
                </div>
                <button class="modal-close" onclick="closeModal()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Content will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification">
        رسالة التنبيه
    </div>

    <script>
        let hasNotification = true;
        let sampleFiles = [
            { id: 1, name: 'تقرير الأداء الشهري', size: '2.5 MB', date: '15/01/2024', publisher: 'أحمد محمد' },
            { id: 2, name: 'خطة العمل السنوية', size: '1.8 MB', date: '14/01/2024', publisher: 'فاطمة أحمد' },
            { id: 3, name: 'إعلان هام للموظفين', size: '0.9 MB', date: '13/01/2024', publisher: 'محمد سعد' }
        ];

        function openModal(type) {
            const modal = document.getElementById('modal');
            const modalTitle = document.getElementById('modalTitleText');
            const modalIcon = document.getElementById('modalIcon');
            const modalBody = document.getElementById('modalBody');
            
            let title = '';
            let icon = '';
            let content = '';
            
            switch(type) {
                case 'clipboard':
                    title = 'الحافظة';
                    icon = '<svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#CA188F" stroke-width="2"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/><rect x="8" y="2" width="8" height="4" rx="1" ry="1"/></svg>';
                    content = generateClipboardContent();
                    break;
                case 'settings':
                    title = 'إدارة المستخدمين والصلاحيات';
                    icon = '<svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#6b7280" stroke-width="2"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>';
                    content = generateSettingsInterface();
                    break;
                case 'search':
                    title = 'البحث في الملفات';
                    icon = '<svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2"><circle cx="11" cy="11" r="8"/><path d="M21 21l-4.35-4.35"/></svg>';
                    content = generateAdvancedSearchInterface();
                    break;
                case 'publish':
                    title = 'نشر ملف جديد';
                    icon = '<svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#3b82f6" stroke-width="2"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17,8 12,3 7,8"/><line x1="12" y1="3" x2="12" y2="15"/></svg>';
                    content = generatePublishInterface();
                    break;
                case 'deleted':
                    title = 'الملفات المحذوفة';
                    icon = '<svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#6b7280" stroke-width="2"><polyline points="3,6 5,6 21,6"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg>';
                    content = generateDeletedFiles();
                    break;
                case 'display':
                    title = 'شاشات العرض';
                    icon = '<svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#6b7280" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>';
                    content = '<div style="text-align: center; padding: 40px; color: #6b7280;">شاشات العرض - قيد التطوير</div>';
                    break;
            }
            
            modalTitle.textContent = title;
            modalIcon.innerHTML = icon;
            modalBody.innerHTML = content;
            modal.classList.add('active');
        }

        function closeModal() {
            document.getElementById('modal').classList.remove('active');
        }

        function generateClipboardContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <h3 style="font-size: 18px; font-weight: 600; color: #374151;">الملفات المحفوظة (${sampleFiles.length} ملف)</h3>
                </div>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    ${sampleFiles.map(file => `
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; background: #f9fafb; border-radius: 12px; border: 1px solid #e5e7eb; transition: all 0.2s;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='#f9fafb'">
                            <div style="display: flex; align-items: center; gap: 16px;">
                                <div style="width: 40px; height: 40px; background: #dbeafe; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #2563eb;">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 style="font-weight: 500; color: #111827; margin-bottom: 4px;">${file.name}</h4>
                                    <p style="font-size: 12px; color: #6b7280;">${file.size} • ${file.date} • ${file.publisher}</p>
                                </div>
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button style="padding: 8px; border: none; border-radius: 6px; cursor: pointer; transition: all 0.2s; color: #6b7280; background: #f3f4f6;" onclick="showNotification('تم عرض الملف: ${file.name}')" title="عرض">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        function generatePublishInterface() {
            return `
                <div style="height: 75vh; overflow-y: auto; padding: 20px;">
                    <!-- Upload Area -->
                    <div style="border: 2px dashed #d1d5db; border-radius: 12px; padding: 60px 40px; text-align: center; background: #f9fafb; transition: all 0.2s; cursor: pointer; margin-bottom: 30px;" onclick="document.getElementById('fileInput').click()" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" onmouseover="this.style.borderColor='#3b82f6'; this.style.background='#eff6ff';" onmouseout="this.style.borderColor='#d1d5db'; this.style.background='#f9fafb';">
                        <input type="file" id="fileInput" style="display: none;" accept="*/*" onchange="handleFileSelect(this)">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#6b7280" stroke-width="2" style="margin: 0 auto 20px; display: block;">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="17,8 12,3 7,8"/>
                            <line x1="12" y1="3" x2="12" y2="15"/>
                        </svg>
                        <h3 style="margin-bottom: 12px; color: #374151; font-size: 20px; font-weight: 600;">تحميل من الجهاز</h3>
                        <p style="color: #9ca3af; font-size: 14px; margin-top: 12px;">يُسمح بجميع أنواع الملفات (PDF, Word, Excel, PowerPoint, صور, فيديو)</p>
                        <p style="color: #9ca3af; font-size: 12px; margin-top: 8px;">حد أقصى 100 MB</p>
                    </div>

                    <!-- File Details Form -->
                    <div style="background: #f9f9f9; padding: 25px; border-radius: 12px; border: 1px solid #e5e7eb;">
                        <h3 style="font-size: 18px; font-weight: 600; color: #374151; margin-bottom: 20px;">تفاصيل الملف</h3>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">نوع الكتاب/المحتوى</label>
                            <input type="text" id="fileType" style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 6px; font-size: 16px; background: white;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">القسم</label>
                            <select id="department" style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 6px; font-size: 16px; background: white;">
                                <option value="">اختر القسم</option>
                                <option value="الكل">الكل</option>
                                <option value="العمليات">العمليات</option>
                                <option value="الموارد البشرية">الموارد البشرية</option>
                                <option value="المالية">المالية</option>
                                <option value="خدمة العملاء">خدمة العملاء</option>
                                <option value="تقنية المعلومات">تقنية المعلومات</option>
                            </select>
                        </div>

                        <!-- Publish Button -->
                        <button style="background: #3b82f6; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 16px; display: flex; align-items: center; gap: 10px; margin: 0 auto;" onclick="publishFile()">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="17,8 12,3 7,8"/>
                                <line x1="12" y1="3" x2="12" y2="15"/>
                            </svg>
                            نشر الملف
                        </button>
                    </div>
                </div>
            `;
        }

        function handleFileSelect(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                const uploadArea = input.parentElement;

                window.selectedFile = file;

                const reader = new FileReader();

                if (file.type.startsWith('image/')) {
                    reader.onload = function(e) {
                        uploadArea.innerHTML = `
                            <div style="text-align: center;">
                                <img src="${e.target.result}" style="max-width: 100%; max-height: 300px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); margin-bottom: 15px;" alt="${file.name}">
                                <div style="background: #dcfce7; padding: 10px; border-radius: 6px; margin-top: 10px;">
                                    <p style="color: #16a34a; font-weight: 600; margin: 0; font-size: 14px;">✅ تم اختيار الصورة: ${file.name}</p>
                                    <p style="color: #16a34a; margin: 5px 0 0 0; font-size: 12px;">الحجم: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                </div>
                                <button onclick="resetUploadArea()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px; margin-top: 10px;">
                                    🔄 اختيار ملف آخر
                                </button>
                            </div>
                        `;
                    };
                    reader.readAsDataURL(file);
                } else {
                    uploadArea.innerHTML = `
                        <div style="text-align: center;">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#6b7280" stroke-width="2" style="margin: 0 auto 20px; display: block;">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                            </svg>
                            <div style="background: #dcfce7; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <p style="color: #16a34a; font-weight: 600; margin: 0; font-size: 16px;">✅ تم اختيار الملف: ${file.name}</p>
                                <p style="color: #16a34a; margin: 5px 0 0 0; font-size: 14px;">الحجم: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                            </div>
                            <button onclick="resetUploadArea()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px;">
                                🔄 اختيار ملف آخر
                            </button>
                        </div>
                    `;
                }

                showNotification(`تم اختيار الملف: ${file.name}`);
            }
        }

        function resetUploadArea() {
            const uploadArea = document.querySelector('[onclick*="fileInput"]');
            uploadArea.innerHTML = `
                <input type="file" id="fileInput" style="display: none;" accept="*/*" onchange="handleFileSelect(this)">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#6b7280" stroke-width="2" style="margin: 0 auto 20px; display: block;">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="17,8 12,3 7,8"/>
                    <line x1="12" y1="3" x2="12" y2="15"/>
                </svg>
                <h3 style="margin-bottom: 12px; color: #374151; font-size: 20px; font-weight: 600;">تحميل من الجهاز</h3>
                <p style="color: #9ca3af; font-size: 14px; margin-top: 12px;">يُسمح بجميع أنواع الملفات (PDF, Word, Excel, PowerPoint, صور, فيديو)</p>
                <p style="color: #9ca3af; font-size: 12px; margin-top: 8px;">حد أقصى 100 MB</p>
            `;
            window.selectedFile = null;
        }

        function publishFile() {
            if (!window.selectedFile) {
                showNotification('⚠️ يرجى اختيار ملف أولاً');
                return;
            }

            const fileType = document.getElementById('fileType').value;
            const department = document.getElementById('department').value;

            if (!fileType.trim()) {
                showNotification('⚠️ يرجى إدخال نوع الكتاب/المحتوى');
                return;
            }

            if (!department) {
                showNotification('⚠️ يرجى اختيار القسم');
                return;
            }

            showNotification(`✅ تم نشر "${fileType}" بنجاح!`);
            showTVDisplayWindow(fileType, window.selectedFile);
            resetPublishForm();
        }

        function resetPublishForm() {
            resetUploadArea();
            const fileTypeInput = document.getElementById('fileType');
            const departmentSelect = document.getElementById('department');

            if (fileTypeInput) fileTypeInput.value = '';
            if (departmentSelect) departmentSelect.selectedIndex = 0;
            window.selectedFile = null;
        }

        function showTVDisplayWindow(fileType, file) {
            const modal = document.getElementById('modal');
            const modalTitle = document.getElementById('modalTitleText');
            const modalIcon = document.getElementById('modalIcon');
            const modalBody = document.getElementById('modalBody');

            modalTitle.textContent = 'شاشة العرض';
            modalIcon.innerHTML = '<svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#6366f1" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>';

            const reader = new FileReader();

            if (file && file.type.startsWith('image/')) {
                reader.onload = function(e) {
                    modalBody.innerHTML = `
                        <div style="background: white; padding: 0; border-radius: 12px; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                            <!-- File Type Header -->
                            <div style="background: transparent; color: black; padding: 20px; text-align: center;">
                                <h1 style="font-size: 36px; font-weight: 900; margin: 0;">${fileType}</h1>
                            </div>

                            <!-- Image Display -->
                            <div style="flex: 1; background: black; display: flex; align-items: center; justify-content: center; border-radius: 0 0 12px 12px;">
                                <img src="${e.target.result}" style="max-width: 100%; max-height: 100%; object-fit: contain;">
                            </div>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                modalBody.innerHTML = `
                    <div style="background: white; padding: 0; border-radius: 12px; text-align: center; min-height: 100vh; display: flex; flex-direction: column;">
                        <!-- File Type Header -->
                        <div style="background: transparent; color: black; padding: 20px; text-align: center;">
                            <h1 style="font-size: 36px; font-weight: 900; margin: 0;">${fileType}</h1>
                        </div>

                        <!-- File Icon Display -->
                        <div style="flex: 1; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; border-radius: 0 0 12px 12px;">
                            <svg width="200" height="200" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="1" style="margin: 0 auto; display: block; opacity: 0.8;">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                            </svg>
                        </div>
                    </div>
                `;
            }

            modal.classList.add('active');
        }

        function handleDrop(event) {
            event.preventDefault();
        }

        function handleDragOver(event) {
            event.preventDefault();
        }

        function generateSettingsInterface() {
            return '<div style="text-align: center; padding: 40px; color: #6b7280;">الإعدادات - قيد التطوير</div>';
        }

        function generateAdvancedSearchInterface() {
            return '<div style="text-align: center; padding: 40px; color: #6b7280;">البحث - قيد التطوير</div>';
        }

        function generateDeletedFiles() {
            return '<div style="text-align: center; padding: 40px; color: #6b7280;">المحذوفات - قيد التطوير</div>';
        }

        function toggleNotification() {
            showNotification('لا توجد إشعارات جديدة');
        }
    </script>
</body>
</html>
