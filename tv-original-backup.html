<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة التلفزيونات المكتبية</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f9fafb; direction: rtl; }
        
        .header { background-color: #E6E6E6; border-bottom: 2px solid #d1d5db; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
        .header-container { display: flex; justify-content: space-between; align-items: center; height: 64px; padding: 0 16px; }
        .header-left { display: flex; align-items: center; gap: 16px; }
        .logo { width: 40px; height: 40px; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 12px; display: flex; align-items: center; justify-content: center; cursor: pointer; }
        .app-name { font-size: 18px; font-weight: bold; color: #374151; }
        .logout-btn { background-color: #ef4444; color: white; border: none; padding: 8px 16px; border-radius: 8px; cursor: pointer; font-weight: 600; }
        
        .main-content { max-width: 1200px; margin: 0 auto; padding: 32px 16px; }
        .cards-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }
        .home-card { background: white; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); border: 3px solid #e5e7eb; padding: 32px; text-align: center; cursor: pointer; transition: all 0.3s ease; }
        .home-card:hover { box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15); transform: translateY(-8px) scale(1.02); }
        
        .card-icon { width: 64px; height: 64px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px; color: white; font-size: 24px; }
        .card-icon.purple { background-color: #CA188F; }
        .card-icon.blue { background-color: #3b82f6; }
        .card-icon.gray { background-color: #6b7280; }
        .card-icon.green { background-color: #10b981; }
        .card-icon.red { background-color: #ef4444; }
        .card-icon.indigo { background-color: #6366f1; }
        
        .card-title { font-size: 20px; font-weight: 600; color: #111827; margin-bottom: 8px; }
        .card-description { color: #6b7280; font-size: 14px; }
        
        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); display: none; align-items: center; justify-content: center; z-index: 1000; }
        .modal-overlay.active { display: flex; }
        .modal-content { background: white; border-radius: 16px; padding: 0; max-width: 95vw; max-height: 95vh; width: 95vw; height: 85vh; overflow: hidden; }
        .modal-header { display: flex; align-items: center; justify-content: space-between; padding: 24px; border-bottom: 1px solid #e5e7eb; }
        .modal-title { font-size: 20px; font-weight: 600; color: #111827; }
        .modal-close { background: none; border: none; padding: 8px; border-radius: 8px; cursor: pointer; color: #6b7280; font-size: 24px; }
        .modal-body { padding: 24px; max-height: 75vh; overflow-y: auto; }
        
        .notification { position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 12px 20px; border-radius: 8px; z-index: 2000; transform: translateX(400px); transition: transform 0.3s ease; }
        .notification.show { transform: translateX(0); }
        
        .settings-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .settings-section { background: #f9f9f9; padding: 15px; border-radius: 6px; border: 1px solid #ddd; }
        .section-title { font-size: 16px; font-weight: 700; color: #333; margin-bottom: 15px; text-align: center; }
        .form-group { margin-bottom: 10px; }
        .form-label { display: block; margin-bottom: 5px; font-weight: 600; color: #333; font-size: 14px; }
        .form-input { width: 100%; padding: 8px 10px; border: 1px solid #ccc; border-radius: 4px; font-size: 16px; background: white; height: 38px; font-weight: 500; }
        .btn { border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; font-weight: 600; font-size: 16px; height: 38px; }
        .btn-primary { background: #1e40af; color: white; width: 100%; margin-top: 8px; }
        .btn-search { background: #3b82f6; color: white; height: 38px; display: flex; align-items: center; justify-content: center; }
        .btn-save { background: #60a5fa; color: white; font-size: 14px; }
        .btn-edit { background: #fbbf24; color: white; font-size: 14px; }
        .btn-delete { background: #ef4444; color: white; font-size: 14px; }
        .search-container { display: flex; gap: 8px; align-items: center; margin-bottom: 15px; }
        .action-buttons { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <div class="logo">📺</div>
                <div class="app-name">TV-Office</div>
            </div>
            <button class="logout-btn" onclick="showNotification('تم تسجيل الخروج')">تسجيل الخروج</button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="cards-grid">
            <div class="home-card" onclick="showModal('clipboard')">
                <div class="card-icon purple">📋</div>
                <h3 class="card-title">الحافظة</h3>
                <p class="card-description">الملفات المرسلة والمستلمة</p>
            </div>

            <div class="home-card" onclick="showModal('settings')">
                <div class="card-icon gray">⚙️</div>
                <h3 class="card-title">الإعدادات</h3>
                <p class="card-description">إدارة المستخدمين والصلاحيات</p>
            </div>

            <div class="home-card" onclick="showModal('search')">
                <div class="card-icon green">🔍</div>
                <h3 class="card-title">البحث</h3>
                <p class="card-description">البحث في الملفات المنشورة</p>
            </div>

            <div class="home-card" onclick="showModal('publish')">
                <div class="card-icon blue">📤</div>
                <h3 class="card-title">النشر</h3>
                <p class="card-description">نشر ملف جديد</p>
            </div>

            <div class="home-card" onclick="showModal('deleted')">
                <div class="card-icon red">🗑️</div>
                <h3 class="card-title">المحذوفات</h3>
                <p class="card-description">الملفات المحذوفة</p>
            </div>

            <div class="home-card" onclick="showModal('displays')">
                <div class="card-icon indigo">📺</div>
                <h3 class="card-title">شاشات العرض</h3>
                <p class="card-description">إدارة شاشات التلفزيون</p>
            </div>
        </div>
    </main>

    <!-- Modal -->
    <div class="modal-overlay" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">عنوان النافذة</div>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Content will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification"></div>

    <script>
        // User Database
        var userDatabase = [
            { name: 'أحمد محمد', jobTitle: 'مدير عام', department: 'العمليات' },
            { name: 'فاطمة أحمد', jobTitle: 'مدير قسم', department: 'الموارد البشرية' },
            { name: 'محمد سعد', jobTitle: 'محاسب', department: 'المالية' },
            { name: 'علي حسن', jobTitle: 'موظف', department: 'تقنية المعلومات' },
            { name: 'سارة أحمد', jobTitle: 'سكرتير', department: 'خدمة العملاء' }
        ];

        // Modal Functions
        function showModal(type) {
            const modal = document.getElementById('modal');
            const modalTitle = document.getElementById('modalTitle');
            const modalBody = document.getElementById('modalBody');

            let content = '';
            let title = '';

            switch(type) {
                case 'clipboard':
                    title = 'الحافظة';
                    content = '<p style="text-align: center; padding: 40px; color: #6b7280;">قريباً - واجهة الحافظة</p>';
                    break;
                case 'settings':
                    title = 'الإعدادات';
                    content = generateSettingsInterface();
                    break;
                case 'search':
                    title = 'البحث';
                    content = '<p style="text-align: center; padding: 40px; color: #6b7280;">قريباً - واجهة البحث</p>';
                    break;
                case 'publish':
                    title = 'النشر';
                    content = '<p style="text-align: center; padding: 40px; color: #6b7280;">قريباً - واجهة النشر</p>';
                    break;
                case 'deleted':
                    title = 'المحذوفات';
                    content = '<p style="text-align: center; padding: 40px; color: #6b7280;">قريباً - واجهة المحذوفات</p>';
                    break;
                case 'displays':
                    title = 'شاشات العرض';
                    content = '<p style="text-align: center; padding: 40px; color: #6b7280;">قريباً - واجهة شاشات العرض</p>';
                    break;
            }

            modalTitle.textContent = title;
            modalBody.innerHTML = content;
            modal.classList.add('active');
        }

        function closeModal() {
            document.getElementById('modal').classList.remove('active');
        }

        function generateSettingsInterface() {
            return `
                <div style="font-size: 16px; font-weight: bold; color: #000; margin-bottom: 15px;">إضافة معلومات المستخدمين</div>

                <div class="settings-grid">
                    <!-- Input Section -->
                    <div class="settings-section">
                        <h3 class="section-title">إدخال معلومات</h3>

                        <div class="form-group">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" id="displayUserName" class="form-input" placeholder="أدخل اسم المستخدم">
                        </div>

                        <div class="form-group">
                            <label class="form-label">المسمى الوظيفي</label>
                            <input type="text" id="displayJobTitle" class="form-input" placeholder="أدخل المسمى الوظيفي">
                        </div>

                        <div class="form-group">
                            <label class="form-label">القسم</label>
                            <input type="text" id="displayDepartment" class="form-input" placeholder="أدخل اسم القسم">
                        </div>

                        <button onclick="saveUserData()" class="btn btn-primary">حفظ</button>
                    </div>

                    <!-- Search and Actions Section -->
                    <div class="settings-section">
                        <h3 class="section-title">البحث والعرض</h3>

                        <div class="form-group">
                            <label class="form-label">البحث عن مستخدم</label>
                            <div class="search-container">
                                <input type="text" id="searchUserInput" class="form-input" placeholder="اكتب اسم المستخدم للبحث..." style="flex: 1; margin-bottom: 0;">
                                <button onclick="searchUser()" class="btn btn-search">🔍</button>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button onclick="saveUserData()" class="btn btn-save">💾 حفظ</button>
                            <button onclick="editUserData()" class="btn btn-edit">✏️ تعديل</button>
                            <button onclick="deleteUserData()" class="btn btn-delete">🗑️ حذف</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // User Management Functions
        function saveUserData() {
            var userName = document.getElementById('displayUserName').value.trim();
            var jobTitle = document.getElementById('displayJobTitle').value.trim();
            var department = document.getElementById('displayDepartment').value.trim();

            if (!userName || !jobTitle || !department) {
                showNotification('⚠️ يرجى ملء جميع الحقول');
                return;
            }

            // Check if user already exists
            var existingUserIndex = -1;
            for (var i = 0; i < userDatabase.length; i++) {
                if (userDatabase[i].name === userName) {
                    existingUserIndex = i;
                    break;
                }
            }

            if (existingUserIndex !== -1) {
                showNotification('⚠️ المستخدم موجود بالفعل');
                return;
            }

            // Add new user
            userDatabase.push({
                name: userName,
                jobTitle: jobTitle,
                department: department
            });

            showNotification('✅ تم حفظ بيانات: ' + userName);
            clearDisplayFields();
        }

        function editUserData() {
            var userName = document.getElementById('displayUserName').value.trim();
            var jobTitle = document.getElementById('displayJobTitle').value.trim();
            var department = document.getElementById('displayDepartment').value.trim();

            if (!userName) {
                showNotification('⚠️ يرجى إدخال اسم المستخدم للتعديل');
                return;
            }

            // Find user in database
            var userIndex = -1;
            for (var i = 0; i < userDatabase.length; i++) {
                if (userDatabase[i].name === userName) {
                    userIndex = i;
                    break;
                }
            }

            if (userIndex !== -1) {
                // Update user data
                userDatabase[userIndex] = {
                    name: userName,
                    jobTitle: jobTitle,
                    department: department
                };

                showNotification('✅ تم تعديل بيانات: ' + userName);
            } else {
                showNotification('❌ لم يتم العثور على المستخدم: ' + userName);
            }
        }

        function deleteUserData() {
            var userName = document.getElementById('displayUserName').value.trim();

            if (!userName) {
                showNotification('⚠️ يرجى إدخال اسم المستخدم للحذف');
                return;
            }

            // Find and delete user
            var userIndex = -1;
            for (var i = 0; i < userDatabase.length; i++) {
                if (userDatabase[i].name === userName) {
                    userIndex = i;
                    break;
                }
            }

            if (userIndex !== -1) {
                userDatabase.splice(userIndex, 1);
                
                showNotification('✅ تم حذف المستخدم: ' + userName);
                clearDisplayFields();
            } else {
                showNotification('❌ لم يتم العثور على المستخدم: ' + userName);
            }
        }

        function searchUser() {
            var searchInput = document.getElementById('searchUserInput');
            var searchName = searchInput.value.trim();

            if (!searchName) {
                showNotification('⚠️ يرجى إدخال اسم للبحث');
                return;
            }

            // Search in database
            var foundUser = null;
            for (var i = 0; i < userDatabase.length; i++) {
                if (userDatabase[i].name.indexOf(searchName) !== -1 || searchName.indexOf(userDatabase[i].name) !== -1) {
                    foundUser = userDatabase[i];
                    break;
                }
            }

            if (foundUser) {
                // Fill the display fields
                document.getElementById('displayUserName').value = foundUser.name;
                document.getElementById('displayJobTitle').value = foundUser.jobTitle;
                document.getElementById('displayDepartment').value = foundUser.department;

                showNotification('✅ تم العثور على: ' + foundUser.name);
            } else {
                showNotification('❌ لم يتم العثور على: ' + searchName);
            }
        }

        function clearDisplayFields() {
            document.getElementById('displayUserName').value = '';
            document.getElementById('displayJobTitle').value = '';
            document.getElementById('displayDepartment').value = '';
            document.getElementById('searchUserInput').value = '';
        }

        // Notification Function
        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
