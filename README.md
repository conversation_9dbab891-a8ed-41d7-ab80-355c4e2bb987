# نظام إدارة التلفزيونات المكتبية

نظام شامل لإدارة وعرض الملفات على شاشات التلفزيون في المكاتب مع أربعة أنواع مختلفة من المستخدمين وصلاحيات متنوعة.

## المميزات

### أنواع المستخدمين

1. **المدير الرئيسي (A)** - صلاحيات كاملة
   - الوصول للصفحة الرئيسية مع جميع الخصائص
   - إدارة المستخدمين والصلاحيات
   - نشر وحذف الملفات
   - مراقبة شاشات العرض

2. **مدير التشغيل (B)** - صلاحيات النشر
   - نشر الملفات إلى الشاشة الرئيسية
   - إدارة الحافظة والبحث
   - عرض المحذوفات

3. **مدير القسم (C)** - استلام تلقائي
   - استلام الملفات تلقائياً على الشاشة
   - إغلاق وحفظ الملفات في الحافظة
   - تنبيهات صوتية للملفات الجديدة
   - البحث والمحذوفات

4. **مسؤول الاستعلامات (D)** - إدارة الشاشات
   - ربط وإدارة شاشات التلفزيون
   - عرض الملفات تلقائياً على الشاشات المربوطة
   - إدارة حالة الاتصال

### الخصائص الرئيسية

- **الحافظة**: عرض الملفات المرسلة والمستلمة
- **الإعدادات**: إدارة المستخدمين والصلاحيات
- **البحث**: البحث في الملفات المنشورة
- **النشر**: رفع ونشر ملفات PDF
- **المحذوفات**: إدارة الملفات المحذوفة
- **شاشات العرض**: مراقبة وإدارة شاشات التلفزيون

## التقنيات المستخدمة

### Backend
- **Node.js** مع Express.js
- **PostgreSQL** مع Prisma ORM
- **Socket.io** للتحديثات المباشرة
- **JWT** للمصادقة
- **Multer** لرفع الملفات

### Frontend
- **React 18** مع TypeScript
- **Tailwind CSS** للتصميم
- **React Query** لإدارة البيانات
- **React Router** للتنقل
- **Socket.io Client** للاتصال المباشر

## متطلبات التشغيل

- Node.js 18+ 
- PostgreSQL 14+
- npm أو yarn

## التثبيت والتشغيل

### 1. تحضير قاعدة البيانات

```bash
# إنشاء قاعدة بيانات PostgreSQL
createdb tv_office_db
```

### 2. تثبيت المتطلبات

```bash
# تثبيت جميع المتطلبات
npm run install:all
```

### 3. إعداد متغيرات البيئة

```bash
# نسخ ملف البيئة
cp backend/.env.example backend/.env

# تحرير الملف وإضافة البيانات المطلوبة
# DATABASE_URL, JWT_SECRET, etc.
```

### 4. إعداد قاعدة البيانات

```bash
cd backend
npm run db:push
npm run db:generate
```

### 5. تشغيل النظام

```bash
# تشغيل Backend و Frontend معاً
npm run dev

# أو تشغيل كل منهما منفصلاً
npm run dev:backend  # Backend على المنفذ 5000
npm run dev:frontend # Frontend على المنفذ 3000
```

## الوصول للنظام

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **Socket.io**: http://localhost:5000

## بيانات تسجيل الدخول الافتراضية

سيتم إنشاء مستخدم افتراضي عند أول تشغيل:

```
اسم المستخدم: admin
كلمة المرور: admin123
النوع: مدير رئيسي
```

## هيكل المشروع

```
TV-Office-System/
├── backend/                 # خادم Node.js
│   ├── src/
│   │   ├── routes/         # مسارات API
│   │   ├── middleware/     # وسطاء المصادقة
│   │   ├── socket/         # معالجات Socket.io
│   │   └── server.js       # الملف الرئيسي
│   ├── prisma/             # مخططات قاعدة البيانات
│   └── uploads/            # ملفات مرفوعة
├── frontend/               # تطبيق React
│   ├── src/
│   │   ├── components/     # مكونات UI
│   │   ├── pages/          # صفحات التطبيق
│   │   ├── services/       # خدمات API
│   │   ├── contexts/       # React Contexts
│   │   └── types/          # أنواع TypeScript
└── package.json            # إعدادات المشروع الرئيسية
```

## API Endpoints

### المصادقة
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/logout` - تسجيل الخروج
- `GET /api/auth/verify` - التحقق من الرمز المميز

### المستخدمين
- `GET /api/users` - جلب جميع المستخدمين
- `POST /api/users` - إضافة مستخدم جديد
- `PUT /api/users/:id` - تحديث بيانات المستخدم

### الملفات
- `POST /api/files/publish` - نشر ملف جديد
- `GET /api/files` - جلب جميع الملفات
- `GET /api/files/:id` - جلب ملف محدد
- `GET /api/files/:id/download` - تحميل ملف

### الشاشات
- `GET /api/displays` - جلب جميع الشاشات
- `POST /api/displays` - إضافة شاشة جديدة
- `PATCH /api/displays/:id/connection` - تحديث حالة الاتصال

## Socket.io Events

### للعملاء
- `newFilePublished` - ملف جديد تم نشره
- `displayConnectionChanged` - تغيير حالة اتصال الشاشة
- `newFileToDisplay` - ملف جديد للعرض

### للخادم
- `connectToDisplay` - الاتصال بشاشة عرض
- `disconnectFromDisplay` - قطع الاتصال بشاشة العرض
- `sendFileToDisplay` - إرسال ملف للعرض

## المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم، يرجى فتح issue في GitHub أو التواصل مع فريق التطوير.
