const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const setupSocketHandlers = (io) => {
  // Middleware للتحقق من المصادقة
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          username: true,
          fullName: true,
          userType: true,
          isActive: true
        }
      });

      if (!user || !user.isActive) {
        return next(new Error('User not found or inactive'));
      }

      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user.fullName} (${socket.user.userType})`);

    // انضمام المستخدم لغرفة حسب نوع المستخدم
    socket.join(socket.user.userType);
    socket.join(`user_${socket.user.id}`);

    // معالج لطلب الاتصال بشاشة العرض
    socket.on('connectToDisplay', async (data) => {
      try {
        if (socket.user.userType !== 'DISPLAY_OP') {
          socket.emit('error', { message: 'ليس لديك صلاحية للاتصال بالشاشات' });
          return;
        }

        const { displayId } = data;
        
        // التحقق من أن الشاشة تخص المستخدم
        const display = await prisma.display.findFirst({
          where: { 
            id: displayId, 
            operatorId: socket.user.id 
          }
        });

        if (!display) {
          socket.emit('error', { message: 'الشاشة غير موجودة' });
          return;
        }

        // انضمام لغرفة الشاشة
        socket.join(`display_${displayId}`);
        
        // تحديث حالة الاتصال
        await prisma.display.update({
          where: { id: displayId },
          data: { isConnected: true }
        });

        socket.emit('displayConnected', { displayId });
        
        // إشعار المديرين بتغيير حالة الاتصال
        io.to('MAIN_ADMIN').emit('displayConnectionChanged', {
          displayId,
          isConnected: true,
          operatorName: socket.user.fullName
        });

      } catch (error) {
        console.error('Connect to display error:', error);
        socket.emit('error', { message: 'خطأ في الاتصال بالشاشة' });
      }
    });

    // معالج لقطع الاتصال بشاشة العرض
    socket.on('disconnectFromDisplay', async (data) => {
      try {
        const { displayId } = data;
        
        // تحديث حالة الاتصال
        await prisma.display.update({
          where: { id: displayId },
          data: { isConnected: false }
        });

        socket.leave(`display_${displayId}`);
        socket.emit('displayDisconnected', { displayId });
        
        // إشعار المديرين بتغيير حالة الاتصال
        io.to('MAIN_ADMIN').emit('displayConnectionChanged', {
          displayId,
          isConnected: false,
          operatorName: socket.user.fullName
        });

      } catch (error) {
        console.error('Disconnect from display error:', error);
        socket.emit('error', { message: 'خطأ في قطع الاتصال بالشاشة' });
      }
    });

    // معالج لإرسال ملف للعرض
    socket.on('sendFileToDisplay', async (data) => {
      try {
        const { fileId, displayId } = data;
        
        // الحصول على بيانات الملف
        const file = await prisma.file.findUnique({
          where: { id: fileId },
          include: {
            publisher: {
              select: {
                fullName: true,
                department: true
              }
            }
          }
        });

        if (!file) {
          socket.emit('error', { message: 'الملف غير موجود' });
          return;
        }

        // إرسال الملف لشاشة العرض
        io.to(`display_${displayId}`).emit('newFileToDisplay', {
          file,
          timestamp: new Date()
        });

        socket.emit('fileSentToDisplay', { fileId, displayId });

      } catch (error) {
        console.error('Send file to display error:', error);
        socket.emit('error', { message: 'خطأ في إرسال الملف للعرض' });
      }
    });

    // معالج عند قطع الاتصال
    socket.on('disconnect', async () => {
      console.log(`User disconnected: ${socket.user.fullName}`);
      
      // تحديث حالة الاتصال لجميع الشاشات التي يديرها المستخدم
      if (socket.user.userType === 'DISPLAY_OP') {
        try {
          await prisma.display.updateMany({
            where: { operatorId: socket.user.id },
            data: { isConnected: false }
          });

          // إشعار المديرين بقطع الاتصال
          io.to('MAIN_ADMIN').emit('operatorDisconnected', {
            operatorId: socket.user.id,
            operatorName: socket.user.fullName
          });
        } catch (error) {
          console.error('Update displays on disconnect error:', error);
        }
      }
    });
  });

  // حفظ مرجع io في app للاستخدام في routes
  return io;
};

module.exports = { setupSocketHandlers };
