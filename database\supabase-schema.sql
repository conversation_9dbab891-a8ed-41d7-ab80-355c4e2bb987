-- إنشاء قاعدة البيانات لنظام إدارة التلفزيونات المكتبية
-- TV Office Management System Database Schema

-- إنشاء جدول المستخدمين
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    job_title VARCHAR(100) NOT NULL,
    department VARCHAR(100) NOT NULL,
    user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('MAIN_ADMIN', 'OPERATIONS_MGR', 'DEPT_MANAGER', 'DISPLAY_OP')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الملفات
CREATE TABLE files (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size INTEGER NOT NULL,
    publisher_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_deleted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الشاشات
CREATE TABLE displays (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    screen_id VARCHAR(50) UNIQUE NOT NULL,
    screen_name VARCHAR(100) NOT NULL,
    operator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_connected BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول طلبات إعادة تعيين كلمة المرور
CREATE TABLE password_reset_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    message TEXT,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول العلاقة بين الملفات والمستقبلين
CREATE TABLE file_receivers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_id UUID NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    receiver_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    received_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(file_id, receiver_id)
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_files_publisher_id ON files(publisher_id);
CREATE INDEX idx_files_is_deleted ON files(is_deleted);
CREATE INDEX idx_displays_operator_id ON displays(operator_id);
CREATE INDEX idx_displays_is_connected ON displays(is_connected);
CREATE INDEX idx_file_receivers_file_id ON file_receivers(file_id);
CREATE INDEX idx_file_receivers_receiver_id ON file_receivers(receiver_id);

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_files_updated_at BEFORE UPDATE ON files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_displays_updated_at BEFORE UPDATE ON displays
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- تمكين Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE files ENABLE ROW LEVEL SECURITY;
ALTER TABLE displays ENABLE ROW LEVEL SECURITY;
ALTER TABLE password_reset_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_receivers ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان (Security Policies)
-- سياسة للمستخدمين: يمكن للجميع القراءة، والمديرين فقط التعديل
CREATE POLICY "Users can view all users" ON users
    FOR SELECT USING (true);

CREATE POLICY "Only admins can insert users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type = 'MAIN_ADMIN'
        )
    );

CREATE POLICY "Only admins can update users" ON users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type = 'MAIN_ADMIN'
        )
    );

-- سياسة للملفات: يمكن للجميع القراءة، والمديرين والمشغلين النشر
CREATE POLICY "Users can view files" ON files
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Authorized users can insert files" ON files
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type IN ('MAIN_ADMIN', 'OPERATIONS_MGR')
        )
    );

-- سياسة للشاشات: يمكن للجميع القراءة، ومسؤولي الشاشات والمديرين التعديل
CREATE POLICY "Users can view displays" ON displays
    FOR SELECT USING (true);

CREATE POLICY "Display operators can manage displays" ON displays
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND (user_type IN ('MAIN_ADMIN', 'DISPLAY_OP') OR id = operator_id)
        )
    );
