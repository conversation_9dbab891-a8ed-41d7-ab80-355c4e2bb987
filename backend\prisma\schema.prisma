// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(uuid())
  username    String   @unique
  password    String
  fullName    String
  jobTitle    String
  department  String
  userType    UserType
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  publishedFiles File[] @relation("Publisher")
  receivedFiles  File[] @relation("Receiver")
  displays       Display[]
  passwordResetRequests PasswordResetRequest[]

  @@map("users")
}

model File {
  id          String     @id @default(uuid())
  fileName    String
  originalName String
  filePath    String
  fileType    String
  fileSize    Int
  publisherId String
  isDeleted   Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  publisher   User       @relation("Publisher", fields: [publisherId], references: [id])
  receivers   User[]     @relation("Receiver")
  
  @@map("files")
}

model Display {
  id          String        @id @default(uuid())
  screenId    String        @unique
  screenName  String
  operatorId  String
  isConnected Boolean       @default(false)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  operator    User          @relation(fields: [operatorId], references: [id])

  @@map("displays")
}

model PasswordResetRequest {
  id        String   @id @default(uuid())
  userId    String
  message   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  // Relations
  user      User     @relation(fields: [userId], references: [id])

  @@map("password_reset_requests")
}

enum UserType {
  MAIN_ADMIN      // A - المدير الرئيسي
  OPERATIONS_MGR  // B - مدير التشغيل
  DEPT_MANAGER    // C - مدير القسم
  DISPLAY_OP      // D - مسؤول الاستعلامات
}
