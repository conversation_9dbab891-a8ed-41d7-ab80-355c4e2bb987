import React, { useState, useEffect } from 'react';
import {
  Plus,
  Monitor,
  LogOut,
  User,
  Bell,
  Wifi,
  WifiOff,
  FileText
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import socketService from '@/services/socket';
import api from '@/services/api';
import { Display } from '@/types';
import toast from 'react-hot-toast';

const DisplayOperatorPage: React.FC = () => {
  const { user, logout } = useAuth();
  const [displays, setDisplays] = useState<Display[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDisplayScreen, setShowDisplayScreen] = useState(false);
  const [currentFile, setCurrentFile] = useState<any>(null);

  // جلب الشاشات من API
  const fetchDisplays = async () => {
    try {
      setIsLoading(true);
      const response = await api.get<Display[]>('/displays');
      setDisplays(response.data);
    } catch (error: any) {
      toast.error('خطأ في جلب الشاشات');
      console.error('Fetch displays error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // جلب الشاشات عند تحميل الصفحة
    fetchDisplays();

    // Connect to socket for receiving files to display
    const socket = socketService.connect();

    if (socket) {
      // Listen for new files to display
      socket.on('newFileToDisplay', (data) => {
        setCurrentFile(data.file);
        toast.success('تم استلام ملف جديد للعرض');
      });

      // Listen for display connection changes
      socket.on('displayConnectionChanged', (data) => {
        setDisplays(prev => prev.map(display =>
          display.id === data.display.id
            ? { ...display, isConnected: data.display.isConnected }
            : display
        ));
      });
    }

    return () => {
      if (socket) {
        socket.off('newFileToDisplay');
        socket.off('displayConnectionChanged');
      }
    };
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('تم تسجيل الخروج بنجاح');
    } catch (error) {
      toast.error('خطأ في تسجيل الخروج');
    }
  };

  const handleConnectDisplay = async (displayId: string) => {
    const display = displays.find(d => d.id === displayId);
    if (!display) {
      toast.error('الشاشة غير موجودة');
      return;
    }

    try {
      const newStatus = !display.isConnected;

      // تحديث حالة الاتصال في قاعدة البيانات
      await api.patch(`/displays/${displayId}/connection`, {
        isConnected: newStatus
      });

      // تحديث الحالة محلياً
      setDisplays(prev => prev.map(d =>
        d.id === displayId
          ? { ...d, isConnected: newStatus }
          : d
      ));

      toast.success(newStatus ? 'تم الاتصال بالشاشة' : 'تم قطع الاتصال');

      // Emit socket event
      if (newStatus) {
        socketService.connectToDisplay(displayId);
      } else {
        socketService.disconnectFromDisplay(displayId);
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'خطأ في تحديث حالة الاتصال';
      toast.error(message);
    }
  };

  // إضافة شاشة جديدة
  const addNewDisplay = async (screenId: string, screenName: string) => {
    try {
      const response = await api.post('/displays', {
        screenId,
        screenName
      });

      await fetchDisplays(); // إعادة جلب الشاشات
      toast.success('تم إضافة الشاشة بنجاح');
    } catch (error: any) {
      const message = error.response?.data?.message || 'خطأ في إضافة الشاشة';
      toast.error(message);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Title */}
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                لوحة مسؤول الاستعلامات
              </h1>
              <p className="text-sm text-gray-500">إدارة شاشات العرض</p>
            </div>

            {/* User Info and Actions */}
            <div className="flex items-center space-x-4 space-x-reverse">
              {/* Notifications */}
              <button className="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Bell className="h-6 w-6" />
                {currentFile && (
                  <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full animate-pulse"></span>
                )}
              </button>

              {/* User Info */}
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user?.fullName}</p>
                  <p className="text-xs text-gray-500">{user?.jobTitle}</p>
                </div>
                <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-600" />
                </div>
              </div>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="btn btn-secondary flex items-center space-x-2 space-x-reverse"
              >
                <LogOut className="h-4 w-4" />
                <span>خروج</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً، {user?.fullName}
          </h2>
          <p className="text-gray-600">
            قم بإدارة شاشات العرض وربطها بالتلفزيونات
          </p>
        </div>

        {/* Main Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Add Screen Card */}
          <div className="home-card bg-green-50 border-green-200">
            <div className="home-card-icon bg-green-500">
              <Plus className="h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
              إضافة شاشة
            </h3>
            
            {/* Display Configuration */}
            <div className="space-y-4">
              {isLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">جاري تحميل الشاشات...</p>
                </div>
              ) : displays.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  لا توجد شاشات مُضافة
                </div>
              ) : (
                displays.map((display) => (
                  <div key={display.id} className="flex items-center space-x-3 space-x-reverse p-3 bg-white rounded-lg border">
                    <span className="text-sm font-medium text-gray-700 min-w-[120px]">
                      {display.screenName}
                    </span>
                    <span className="text-xs text-gray-500 min-w-[80px]">
                      {display.screenId}
                    </span>
                    <button
                      onClick={() => handleConnectDisplay(display.id)}
                      className={`btn text-sm px-3 py-1 flex items-center space-x-1 space-x-reverse ${
                        display.isConnected
                          ? 'bg-green-500 text-white hover:bg-green-600'
                          : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
                      }`}
                    >
                      {display.isConnected ? (
                        <>
                          <Wifi className="h-4 w-4" />
                          <span>متصل</span>
                        </>
                      ) : (
                      <>
                        <WifiOff className="h-4 w-4" />
                        <span>ربط</span>
                      </>
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Display Screen Card */}
          <div 
            className="home-card bg-blue-50 border-blue-200 cursor-pointer"
            onClick={() => setShowDisplayScreen(true)}
          >
            <div className="home-card-icon bg-blue-500">
              <Monitor className="h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2 text-center">
              شاشة العرض
            </h3>
            <p className="text-gray-600 text-center text-sm">
              انقر لفتح شاشة العرض
            </p>
          </div>
        </div>

        {/* Connection Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">حالة الاتصال</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {displays.map((display) => (
              <div key={display.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">
                  {display.name}
                </span>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className={`h-2 w-2 rounded-full ${display.connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className={`text-xs ${display.connected ? 'text-green-600' : 'text-red-600'}`}>
                    {display.connected ? 'متصل' : 'غير متصل'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>

      {/* Display Screen Modal */}
      {showDisplayScreen && (
        <div className="fixed inset-0 z-50 bg-gray-800">
          {/* Header */}
          <div className="bg-gray-700 p-4 flex justify-between items-center">
            <h3 className="text-white font-semibold">شاشة العرض</h3>
            <button
              onClick={() => setShowDisplayScreen(false)}
              className="text-white hover:text-gray-300"
            >
              إغلاق
            </button>
          </div>
          
          {/* Display Content */}
          <div className="h-full p-8 flex flex-col">
            {/* Publisher Info */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-gray-700 p-4 rounded-lg border border-gray-600">
                <h4 className="text-gray-300 text-sm mb-1">اسم الناشر</h4>
                <p className="text-white font-medium">
                  {currentFile?.publisher?.fullName || 'لا يوجد ملف'}
                </p>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg border border-gray-600">
                <h4 className="text-gray-300 text-sm mb-1">اسم الملف</h4>
                <p className="text-white font-medium">
                  {currentFile?.fileName || 'لا يوجد ملف'}
                </p>
              </div>
            </div>
            
            {/* File Display Area */}
            <div className="flex-1 bg-gray-700 rounded-lg border border-gray-600 flex items-center justify-center">
              {currentFile ? (
                <div className="text-center">
                  <FileText className="h-24 w-24 text-gray-400 mx-auto mb-4" />
                  <p className="text-white text-lg font-medium mb-2">
                    {currentFile.originalName}
                  </p>
                  <p className="text-gray-300 text-sm">
                    عارض الملفات قيد التطوير
                  </p>
                </div>
              ) : (
                <div className="text-center">
                  <Monitor className="h-24 w-24 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">
                    في انتظار ملف للعرض...
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DisplayOperatorPage;
