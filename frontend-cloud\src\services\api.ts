import axios, { AxiosResponse, AxiosError } from 'axios';
import toast from 'react-hot-toast';
import { API_CONFIG, ERROR_MESSAGES, getAuthToken, removeAuthToken } from '../config/api';

// إنشاء instance من axios
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Interceptor للطلبات - إضافة رمز المصادقة
api.interceptors.request.use(
  (config) => {
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor للاستجابات - معالجة الأخطاء
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    const status = error.response?.status;
    const message = (error.response?.data as any)?.message;

    switch (status) {
      case 401:
        // غير مصرح - حذف الرمز المميز وإعادة التوجيه
        removeAuthToken();
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
        toast.error(message || ERROR_MESSAGES.UNAUTHORIZED);
        break;
        
      case 403:
        toast.error(message || ERROR_MESSAGES.FORBIDDEN);
        break;
        
      case 404:
        toast.error(message || ERROR_MESSAGES.NOT_FOUND);
        break;
        
      case 422:
        toast.error(message || ERROR_MESSAGES.VALIDATION_ERROR);
        break;
        
      case 500:
      case 502:
      case 503:
      case 504:
        toast.error(message || ERROR_MESSAGES.SERVER_ERROR);
        break;
        
      default:
        if (error.code === 'ECONNABORTED') {
          toast.error('انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى');
        } else if (!error.response) {
          toast.error(ERROR_MESSAGES.NETWORK_ERROR);
        } else {
          toast.error(message || 'حدث خطأ غير متوقع');
        }
    }
    
    return Promise.reject(error);
  }
);

// دوال API للمصادقة
export const authAPI = {
  // تسجيل الدخول
  login: async (credentials: { username: string; password: string }) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  // تسجيل الخروج
  logout: async () => {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // حتى لو فشل الطلب، نقوم بحذف البيانات المحلية
      console.error('Logout error:', error);
    } finally {
      removeAuthToken();
    }
  },

  // التحقق من صحة الرمز المميز
  verifyToken: async () => {
    const response = await api.get('/auth/verify');
    return response.data;
  },

  // طلب إعادة تعيين كلمة المرور
  requestPasswordReset: async (data: { username: string; message?: string }) => {
    const response = await api.post('/auth/request-password-reset', data);
    return response.data;
  }
};

// دوال API للمستخدمين
export const usersAPI = {
  // جلب جميع المستخدمين
  getAll: async () => {
    const response = await api.get('/users');
    return response.data;
  },

  // جلب مستخدم محدد
  getById: async (id: string) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  // إضافة مستخدم جديد
  create: async (userData: {
    username: string;
    password: string;
    fullName: string;
    jobTitle: string;
    department: string;
    userType: string;
  }) => {
    const response = await api.post('/users', userData);
    return response.data;
  },

  // تحديث بيانات مستخدم
  update: async (id: string, userData: {
    fullName?: string;
    jobTitle?: string;
    department?: string;
    userType?: string;
    password?: string;
    isActive?: boolean;
  }) => {
    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },

  // حذف مستخدم
  delete: async (id: string) => {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  },

  // جلب طلبات إعادة تعيين كلمة المرور
  getPasswordResetRequests: async () => {
    const response = await api.get('/users/password-reset-requests');
    return response.data;
  }
};

// دوال API للملفات
export const filesAPI = {
  // نشر ملف جديد
  publish: async (formData: FormData, onUploadProgress?: (progress: number) => void) => {
    const response = await api.post('/files/publish', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onUploadProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onUploadProgress(progress);
        }
      }
    });
    return response.data;
  },

  // جلب جميع الملفات
  getAll: async (filters?: {
    search?: string;
    publisher?: string;
    department?: string;
    fileType?: string;
  }) => {
    const response = await api.get('/files', { params: filters });
    return response.data;
  },

  // جلب ملف محدد
  getById: async (id: string) => {
    const response = await api.get(`/files/${id}`);
    return response.data;
  },

  // حذف ملف
  delete: async (id: string) => {
    const response = await api.delete(`/files/${id}`);
    return response.data;
  },

  // استعادة ملف
  restore: async (id: string) => {
    const response = await api.patch(`/files/${id}/restore`);
    return response.data;
  },

  // جلب الملفات المحذوفة
  getDeleted: async () => {
    const response = await api.get('/files/deleted/list');
    return response.data;
  },

  // حذف نهائي
  permanentDelete: async (id: string) => {
    const response = await api.delete(`/files/${id}/permanent`);
    return response.data;
  }
};

// دوال API للشاشات
export const displaysAPI = {
  // جلب جميع الشاشات
  getAll: async () => {
    const response = await api.get('/displays');
    return response.data;
  },

  // جلب شاشة محددة
  getById: async (id: string) => {
    const response = await api.get(`/displays/${id}`);
    return response.data;
  },

  // إضافة شاشة جديدة
  create: async (displayData: {
    screenId: string;
    screenName: string;
    operatorId?: string;
  }) => {
    const response = await api.post('/displays', displayData);
    return response.data;
  },

  // تحديث بيانات شاشة
  update: async (id: string, displayData: {
    screenName?: string;
  }) => {
    const response = await api.put(`/displays/${id}`, displayData);
    return response.data;
  },

  // تحديث حالة الاتصال
  updateConnection: async (id: string, isConnected: boolean) => {
    const response = await api.patch(`/displays/${id}/connection`, { isConnected });
    return response.data;
  },

  // حذف شاشة
  delete: async (id: string) => {
    const response = await api.delete(`/displays/${id}`);
    return response.data;
  },

  // جلب شاشات مشغل معين
  getByOperator: async (operatorId: string) => {
    const response = await api.get(`/displays/operator/${operatorId}`);
    return response.data;
  }
};

// دالة عامة للتحقق من حالة الخادم
export const healthCheck = async () => {
  try {
    const response = await api.get('/health');
    return response.data;
  } catch (error) {
    throw new Error('الخادم غير متاح');
  }
};

export default api;
