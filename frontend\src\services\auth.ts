import api from './api';
import { AuthResponse, LoginCredentials, User } from '@/types';

export const authService = {
  // تسجيل الدخول
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/login', credentials);
    
    // حفظ البيانات في localStorage
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    
    return response.data;
  },

  // تسجيل الخروج
  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // حتى لو فشل الطلب، نقوم بحذف البيانات المحلية
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }
  },

  // التحقق من صحة الرمز المميز
  async verifyToken(): Promise<{ user: User }> {
    const response = await api.get<{ user: User }>('/auth/verify');
    return response.data;
  },

  // طلب إعادة تعيين كلمة المرور
  async requestPasswordReset(username: string, message?: string): Promise<{ message: string }> {
    const response = await api.post<{ message: string }>('/auth/request-password-reset', {
      username,
      message
    });
    return response.data;
  },

  // الحصول على المستخدم الحالي من localStorage
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('Error parsing user from localStorage:', error);
        localStorage.removeItem('user');
      }
    }
    return null;
  },

  // التحقق من وجود رمز مميز
  hasToken(): boolean {
    return !!localStorage.getItem('token');
  },

  // الحصول على الرمز المميز
  getToken(): string | null {
    return localStorage.getItem('token');
  }
};
