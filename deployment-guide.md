# دليل النشر الكامل لنظام إدارة التلفزيونات المكتبية

## 📋 المتطلبات

1. **حساب Supabase** (مجاني)
2. **حساب Vercel** (مجاني)
3. **حساب GitHub** (موجود)

## 🚀 خطوات النشر

### المرحلة الأولى: إعداد قاعدة البيانات (Supabase)

#### 1. إنشاء مشروع Supabase
1. اذهب إلى [supabase.com](https://supabase.com)
2. انقر على "Start your project"
3. سجل دخول باستخدام GitHub
4. انقر على "New Project"
5. املأ البيانات:
   - **اسم المشروع**: `tv-office-system`
   - **كلمة مرور قاعدة البيانات**: اختر كلمة مرور قوية
   - **المنطقة**: اختر أقرب منطقة
6. انقر على "Create new project"

#### 2. إعداد قاعدة البيانات
1. اذهب إلى **SQL Editor** في لوحة التحكم
2. انقر على **"New query"**
3. انسخ محتوى ملف `database/supabase-schema.sql` والصقه
4. انقر على **"Run"**
5. كرر العملية مع ملف `database/supabase-seed.sql`

#### 3. إعداد Storage
1. اذهب إلى **Storage** في لوحة التحكم
2. انقر على **"Create bucket"**
3. **اسم الـ Bucket**: `files`
4. **Public bucket**: نعم
5. انقر على **"Create bucket"**

#### 4. إعداد سياسات Storage
في **Storage > files > Policies**، أضف:

```sql
-- سياسة للقراءة
CREATE POLICY "Public Access" ON storage.objects
FOR SELECT USING (bucket_id = 'files');

-- سياسة للكتابة
CREATE POLICY "Authenticated users can upload" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'files' AND auth.role() = 'authenticated');

-- سياسة للحذف
CREATE POLICY "Users can delete own files" ON storage.objects
FOR DELETE USING (bucket_id = 'files' AND auth.role() = 'authenticated');
```

#### 5. الحصول على بيانات الاتصال
من **Settings > API**:
- **Project URL**: انسخه (مثل: `https://xxxxx.supabase.co`)
- **anon public key**: انسخه
- **service_role key**: انسخه (احتفظ به سرياً)

### المرحلة الثانية: نشر Backend (Vercel)

#### 1. إعداد مشروع Vercel
1. اذهب إلى [vercel.com](https://vercel.com)
2. سجل دخول باستخدام GitHub
3. انقر على "New Project"
4. اختر مستودع `TV-Office`
5. في **Root Directory**، اختر `backend-cloud`
6. انقر على "Deploy"

#### 2. إعداد متغيرات البيئة
في لوحة تحكم Vercel، اذهب إلى **Settings > Environment Variables** وأضف:

```
SUPABASE_URL=https://xxxxx.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
JWT_SECRET=tv-office-super-secret-jwt-key-2024
NODE_ENV=production
```

#### 3. إعادة النشر
انقر على **"Redeploy"** لتطبيق متغيرات البيئة.

#### 4. الحصول على رابط Backend
انسخ رابط المشروع (مثل: `https://tv-office-backend.vercel.app`)

### المرحلة الثالثة: تحديث Frontend

#### 1. تحديث إعدادات API
في ملف `frontend/src/config/api.ts`، استبدل:

```typescript
BASE_URL: process.env.NODE_ENV === 'production' 
  ? 'https://your-backend-url.vercel.app/api'  // ضع الرابط الحقيقي هنا
  : 'http://localhost:5000/api',

SOCKET_URL: process.env.NODE_ENV === 'production'
  ? 'https://your-backend-url.vercel.app'  // ضع الرابط الحقيقي هنا
  : 'http://localhost:5000',
```

#### 2. تحديث خدمات API
انسخ ملفات من `frontend-cloud/src/` إلى `frontend/src/`:
- `config/api.ts`
- `services/api.ts`
- `services/socket.ts`

### المرحلة الرابعة: نشر Frontend (GitHub Pages)

#### 1. إعداد GitHub Pages
1. اذهب إلى **Settings** في مستودع GitHub
2. انتقل إلى **Pages**
3. في **Source**، اختر "GitHub Actions"

#### 2. إعداد GitHub Secrets
في **Settings > Secrets and variables > Actions**، أضف:

```
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-org-id
VERCEL_PROJECT_ID=your-project-id
BACKEND_URL=https://your-backend-url.vercel.app
GITHUB_TOKEN=automatic
```

#### 3. تشغيل النشر
1. ادفع التغييرات إلى branch `main`
2. سيتم تشغيل GitHub Actions تلقائياً
3. انتظر حتى اكتمال النشر

## 🔗 الروابط النهائية

بعد اكتمال النشر:

- **Frontend**: `https://skayhr.github.io/tv-office/`
- **Backend**: `https://your-backend-url.vercel.app`

## 🔐 بيانات تسجيل الدخول

- **المدير الرئيسي**: `admin` / `admin123`
- **مدير التشغيل**: `operations_mgr` / `123456`
- **مدير الموارد البشرية**: `dept_mgr_hr` / `123456`
- **مدير المالية**: `dept_mgr_finance` / `123456`
- **مسؤول الاستعلامات**: `display_op` / `123456`

## ✅ اختبار النظام

1. افتح رابط Frontend
2. سجل دخول باستخدام `admin` / `admin123`
3. جرب إضافة مستخدم جديد
4. جرب رفع ملف PDF
5. جرب ربط شاشة عرض

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من صحة متغيرات البيئة في Vercel
   - تأكد من تشغيل SQL scripts في Supabase

2. **خطأ في رفع الملفات**
   - تأكد من إعداد Storage bucket في Supabase
   - تأكد من سياسات الأمان

3. **خطأ في Socket.io**
   - تأكد من أن Backend يدعم WebSocket
   - تأكد من إعدادات CORS

## 📞 الدعم

إذا واجهت أي مشاكل، تأكد من:
1. جميع متغيرات البيئة صحيحة
2. قاعدة البيانات تم إعدادها بشكل صحيح
3. Backend يعمل (اختبر `/api/health`)
4. Frontend يتصل بـ Backend الصحيح

## 🎉 تهانينا!

النظام الآن يعمل بشكل كامل على الإنترنت مع:
- ✅ قاعدة بيانات حقيقية
- ✅ حفظ دائم للبيانات
- ✅ رفع ونشر الملفات
- ✅ إدارة المستخدمين
- ✅ شاشات العرض التفاعلية
- ✅ تحديثات مباشرة
