const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 بدء تشغيل نظام إدارة التلفزيونات المكتبية...\n');

// تشغيل Backend
console.log('📡 تشغيل Backend Server...');
const backend = spawn('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, 'backend'),
  stdio: 'inherit',
  shell: true
});

// انتظار قليل ثم تشغيل Frontend
setTimeout(() => {
  console.log('\n🎨 تشغيل Frontend...');
  const frontend = spawn('npm', ['run', 'dev'], {
    cwd: path.join(__dirname, 'frontend'),
    stdio: 'inherit',
    shell: true
  });

  frontend.on('error', (err) => {
    console.error('❌ خطأ في Frontend:', err);
  });
}, 3000);

backend.on('error', (err) => {
  console.error('❌ خطأ في Backend:', err);
});

console.log('\n✅ النظام قيد التشغيل...');
console.log('🌐 Frontend: http://localhost:3000');
console.log('🔧 Backend: http://localhost:5000');
console.log('\n📋 بيانات تسجيل الدخول:');
console.log('المدير الرئيسي: admin / admin123');
console.log('مدير التشغيل: operations_mgr / 123456');
console.log('\nاضغط Ctrl+C لإيقاف النظام');

// معالجة إيقاف النظام
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف النظام...');
  backend.kill();
  process.exit();
});
